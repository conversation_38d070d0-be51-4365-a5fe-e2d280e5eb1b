package com.storeflow.configurations;

import com.storeflow.enums.Role;
import com.storeflow.models.Admin;
import com.storeflow.models.Employee;
import com.storeflow.models.Supplier;
import com.storeflow.models.User;
import com.storeflow.repositories.AdminRepository;
import com.storeflow.repositories.EmployeeRepository;
import com.storeflow.repositories.SupplierRepository;
import com.storeflow.repositories.UserRepository;
import com.storeflow.services.FileService;
import com.storeflow.utils.PhoneNumberFormatter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@Slf4j
@Component
@RequiredArgsConstructor
@Order(2) // Run after DatabaseSchemaUpdater
public class SystemBootstrapper implements CommandLineRunner {

    private final FileService fileService;
    private final UserRepository userRepository;
    private final AdminRepository adminRepository;
    private final EmployeeRepository employeeRepository;
    private final SupplierRepository supplierRepository;
    private final PasswordEncoder passwordEncoder;
    private final PhoneNumberFormatter phoneNumberFormatter;

    private final Random random = new Random();

    @Override
    @Transactional
    public void run(String... args) {
        createAdminIfNeeded();
        createTestEmployeesIfNeeded();
        createTestSuppliersIfNeeded();
    }

    /**
     * Creates the initial admin user if no admin exists in the system
     */
    private void createAdminIfNeeded() {
        // Check if we have any admins in the system
        if (userRepository.countByRole(Role.ADMIN) == 0) {
            // Create the first admin if none exists
            User user = User.builder()
                .firstName("Ilyas")
                .lastName("BLIDI")
                .email("<EMAIL>")
                .phoneNumber(phoneNumberFormatter.formatToInternational("+212 771078110"))
                .password(passwordEncoder.encode("adminPassword123$"))
                .profilePictureUrl(fileService.getDefaultProfilePictureUrl())
                .role(Role.ADMIN)
                .build();

            // Save the user first
            user = userRepository.save(user);  // Make sure to capture the returned user

            // Create and save Admin entity
            Admin admin = Admin.builder()
                .user(user)
                .build();

            adminRepository.save(admin);

            log.info("Initial admin account created successfully.");
        } else {
            log.debug("Admin account already exists - skipping initialization.");
        }
    }

    /**
     * Creates 20 test employees if no employees exist in the system
     */
    private void createTestEmployeesIfNeeded() {
        // Check if we have any employees in the system
        if (userRepository.countByRole(Role.EMPLOYEE) == 0) {
            log.info("Creating 20 test employees for development...");

            List<User> users = new ArrayList<>();
            List<Employee> employees = new ArrayList<>();

            // Arab first names for test data
            String[] firstNames = {
                    "Ahmed", "Fatima", "Mohamed", "Amina", "Youssef", "Khadija", "Omar", "Layla",
                    "Ali", "Sara", "Hassan", "Zahra", "Ibrahim", "Nour", "Tariq", "Mariam",
                    "Samir", "Huda", "Rami", "Salma"
            };

            // Arab last names for test data
            String[] lastNames = {
                    "Al-Farsi", "Benali", "El-Masri", "Haddad", "Khoury", "Mansour", "Nasr", "Qassem",
                    "Rahman", "Sabbagh", "Toumi", "Zein", "Abdelaziz", "Barakat", "Chehade", "Daher",
                    "Fahmy", "Ghandour", "Issa", "Jabari"
            };


            // Create 20 employees
            for (int i = 0; i < 20; i++) {
                String firstName = firstNames[i];
                String lastName = lastNames[i];
                String email = firstName.toLowerCase() + "." + lastName.toLowerCase() + "@employee.com";

                // Generate a random phone number in the format +212 7XXXXXXXX
                String phoneNumber = "+212 7" + String.format("%08d", random.nextInt(100000000));

                // Create the user
                User user = User.builder()
                    .firstName(firstName)
                    .lastName(lastName)
                    .email(email)
                    .phoneNumber(phoneNumberFormatter.formatToInternational(phoneNumber))
                    .password(passwordEncoder.encode("employeePassword123$"))
                    .profilePictureUrl(fileService.getDefaultProfilePictureUrl())
                    .role(Role.EMPLOYEE)
                    .build();

                users.add(user);
            }

            // Save all users
            users = userRepository.saveAll(users);

            // Create employee entities
            for (User user : users) {
                Employee employee = Employee.builder()
                    .user(user)
                    .build();

                employees.add(employee);
            }

            // Save all employees
            employeeRepository.saveAll(employees);

            log.info("Successfully created 20 test employees.");
        } else {
            log.debug("Employees already exist - skipping test data creation.");
        }
    }

    /**
     * Creates 20 test suppliers if no suppliers exist in the system
     */
    private void createTestSuppliersIfNeeded() {
        // Check if we have any suppliers in the system
        if (userRepository.countByRole(Role.SUPPLIER) == 0) {
            log.info("Creating 20 test suppliers for development...");

            List<User> users = new ArrayList<>();
            List<Supplier> suppliers = new ArrayList<>();

            // Arab first names for test data
            String[] firstNames = {
                    "Amine", "Basma", "Carim", "Dounia", "Elie", "Farah", "Ghassan", "Hiba",
                    "Ilyas", "Jana", "Khaled", "Lina", "Mounir", "Nisrine", "Othmane", "Perla",
                    "Qays", "Rania", "Sami", "Tala"
            };

            // Arab last names for test data
            String[] lastNames = {
                    "Abdelrahman", "Bouaziz", "Chakroun", "Dib", "Eid", "Fahem", "Gamal", "Hachem",
                    "Issami", "Jaziri", "Kanaan", "Lahlou", "Mekki", "Nouira", "Ouali", "Pacha",
                    "Qadiri", "Rahal", "Slaoui", "Tawfiq"
            };


            // Localizations for test data
            String[] localizations = {
                "Casablanca", "Rabat", "Marrakech", "Fes", "Tangier", "Agadir", "Meknes", "Oujda",
                "Kenitra", "Tetouan", "Safi", "El Jadida", "Beni Mellal", "Nador", "Taza", "Settat",
                "Berrechid", "Khemisset", "Guelmim", "Khouribga"
            };

            // Create 20 suppliers
            for (int i = 0; i < 20; i++) {
                String firstName = firstNames[i];
                String lastName = lastNames[i];
                String email = firstName.toLowerCase() + "." + lastName.toLowerCase() + "@supplier.com";
                String localization = localizations[i];

                // Generate a random phone number in the format +212 6XXXXXXXX
                String phoneNumber = "+212 6" + String.format("%08d", random.nextInt(100000000));

                // Create the user
                User user = User.builder()
                    .firstName(firstName)
                    .lastName(lastName)
                    .email(email)
                    .phoneNumber(phoneNumberFormatter.formatToInternational(phoneNumber))
                    .password(passwordEncoder.encode("supplierPassword123$"))
                    .profilePictureUrl(fileService.getDefaultProfilePictureUrl())
                    .role(Role.SUPPLIER)
                    .build();

                users.add(user);
            }

            // Save all users
            users = userRepository.saveAll(users);

            // Create supplier entities
            for (int i = 0; i < users.size(); i++) {
                User user = users.get(i);
                String localization = localizations[i];

                Supplier supplier = Supplier.builder()
                    .user(user)
                    .localization(localization)
                    .build();

                suppliers.add(supplier);
            }

            // Save all suppliers
            supplierRepository.saveAll(suppliers);

            log.info("Successfully created 20 test suppliers.");
        } else {
            log.debug("Suppliers already exist - skipping test data creation.");
        }
    }
}
