package com.storeflow.controllers;

import com.storeflow.models.User;
import com.storeflow.services.FileService;
import com.storeflow.services.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.UUID;

@RestController
@RequestMapping("/api/files")
@RequiredArgsConstructor
@Slf4j
public class FileController {

    private final UserService userService;
    private final FileService fileService;

    @PostMapping(value = "/product-pictures", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<String> uploadProductPicture(
            @RequestParam("file") MultipartFile file) {
        log.info("REST request to upload product picture");
        String fileUrl = fileService.uploadProductPicture(file);
        return ResponseEntity.ok(fileUrl);
    }
}
