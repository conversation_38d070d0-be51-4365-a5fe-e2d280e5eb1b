import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { isDesktop, ripple } from "@/utils";
import { usePasswordVisibility } from "@/hooks";
import {
  EyeIcon,
  EyeOffIcon,
  KeyRoundIcon,
  CheckCircleIcon,
  ArrowLeftIcon,
} from "lucide-react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useState, useEffect } from "react";
import { authApi } from "@/context/auth-context";
import { toast } from "sonner";

export const ResetPassword = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [resetSuccess, setResetSuccess] = useState(false);
  const email = searchParams.get("email");
  const code = searchParams.get("code");

  const {
    isVisible: isPwdVisible,
    toggleVisibility: togglePwdVisibility,
    inputRef: pwdInputRef,
  } = usePasswordVisibility();
  const {
    isVisible: isConfPwdVisible,
    toggleVisibility: toggleConfPwdVisibility,
    inputRef: confPwdInputRef,
  } = usePasswordVisibility();

  // Redirect if no email or code is provided
  useEffect(() => {
    if (!email || !code) {
      toast.error("Invalid reset link. Please request a new password reset.");
      navigate("/auth/forgot-password");
    }
  }, [email, code, navigate]);

  // Define validation schema using Yup
  const ResetPasswordSchema = Yup.object().shape({
    newPassword: Yup.string()
      .min(8, "Password must contain at least 8 characters")
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&].*$/,
        "Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character"
      )
      .required("New password is required"),
    confirmationPassword: Yup.string()
      .oneOf([Yup.ref("newPassword"), null], "Passwords must match")
      .required("Password confirmation is required"),
  });

  // Initialize formik
  const formik = useFormik({
    initialValues: {
      newPassword: "",
      confirmationPassword: "",
    },
    validationSchema: ResetPasswordSchema,
    onSubmit: async (values) => {
      setLoading(true);
      try {
        const response = await authApi.post("/api/v1/auth/reset-password", {
          email,
          verificationCode: code,
          newPassword: values.newPassword,
          confirmationPassword: values.confirmationPassword,
        });

        if (response.data.success) {
          setResetSuccess(true);
          toast.success("Password reset successfully!");
        }
      } catch (error) {
        console.error("Reset password error:", error);
        const errorMessage =
          error.response?.data?.message ||
          "Failed to reset password. Please try again.";
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    },
  });

  if (resetSuccess) {
    return (
      <section className="min-h-screen flex flex-col justify-center items-center px-8 py-20 lg:px-[340px] bg-blue-gray-50/40">
        <div className="flex items-center justify-center gap-x-1.5 absolute top-0 right-0 px-6 py-3 select-none">
          <img src="/icon.png" className="size-7" alt="Logo" />
          <p className="text-blue-gray-700 font-satoshi text-xl">
            <span className="text-crimson">S</span>tore
            <span className="text-crimson">F</span>low
          </p>
        </div>

        <div className="lg:bg-white lg:rounded-3xl w-full lg:p-10 lg:border lg:border-blue-gray-50 lg:shadow-dark text-blue-gray-700">
          <div className="text-center">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
              <CheckCircleIcon className="w-8 h-8 text-green-600" />
            </div>

            <p className="font-satoshi font-bold text-header text-blue-gray-800 text-center w-full py-0 mb-4">
              Password reset successful!
            </p>

            <p className="font-normal text-subheader text-blue-gray-600 text-center w-full mb-8 py-0">
              Your password has been successfully reset. <br />
              You can now login with your new password.
            </p>

            <button
              onClick={() => navigate("/auth/login")}
              onMouseDown={(event) => ripple.create(event, "light")}
              className="relative overflow-hidden w-full bg-blue-gray-800 text-white py-4 px-6 rounded-xl font-medium text-base lg:text-lg transition-all duration-300 hover:bg-blue-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-gray-500 focus:ring-offset-2 flex items-center justify-center"
            >
              Continue to login
            </button>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="min-h-screen flex flex-col justify-center items-center px-8 py-20 lg:px-[340px] bg-blue-gray-50/40">
      <div className="flex items-center justify-center gap-x-1.5 absolute top-0 right-0 px-6 py-3 select-none">
        <img src="/icon.png" className="size-7" alt="Logo" />
        <p className="text-blue-gray-700 font-satoshi text-xl">
          <span className="text-crimson">S</span>tore
          <span className="text-crimson">F</span>low
        </p>
      </div>

      <div className="lg:bg-white lg:rounded-3xl w-full lg:p-10 lg:border lg:border-blue-gray-50 lg:shadow-dark text-blue-gray-700">
        <p className="font-satoshi font-bold text-header text-blue-gray-800 text-center w-full py-0">
          Reset your password
        </p>
        <p className="font-normal text-subheader text-blue-gray-800 text-center w-full mb-7 py-0">
          Enter your new password below
        </p>

        <form
          onSubmit={formik.handleSubmit}
          className="w-full flex flex-col items-center gap-y-7 mb-7"
        >
          {/* New Password Input */}
          <div className="w-full relative">
            <div className="relative">
              <input
                ref={pwdInputRef}
                type={isPwdVisible ? "text" : "password"}
                name="newPassword"
                placeholder="Enter new password"
                value={formik.values.newPassword}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                className={`w-full h-[53px] pl-12 pr-12 border rounded-xl text-base lg:text-lg placeholder-blue-gray-400 focus:outline-none focus:ring-2 transition-all duration-300 ${
                  formik.touched.newPassword && formik.errors.newPassword
                    ? "border-red-500 focus:ring-red-200 bg-red-50"
                    : "border-blue-gray-200 focus:ring-blue-300 focus:border-blue-500 bg-white"
                }`}
              />
              <KeyRoundIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 text-blue-gray-400 size-5 lg:size-6" />
              <button
                type="button"
                onClick={togglePwdVisibility}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-blue-gray-400 hover:text-blue-gray-600 transition-colors duration-200"
              >
                {isPwdVisible ? (
                  <EyeIcon className="size-5 lg:size-6" />
                ) : (
                  <EyeOffIcon className="size-5 lg:size-6" />
                )}
              </button>
            </div>
            {formik.touched.newPassword && formik.errors.newPassword && (
              <p className="text-red-500 text-sm mt-1 ml-1">
                {formik.errors.newPassword}
              </p>
            )}
          </div>

          {/* Confirm Password Input */}
          <div className="w-full relative">
            <div className="relative">
              <input
                ref={confPwdInputRef}
                type={isConfPwdVisible ? "text" : "password"}
                name="confirmationPassword"
                placeholder="Confirm new password"
                value={formik.values.confirmationPassword}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                className={`w-full h-[53px] pl-12 pr-12 border rounded-xl text-base lg:text-lg placeholder-blue-gray-400 focus:outline-none focus:ring-2 transition-all duration-300 ${
                  formik.touched.confirmationPassword &&
                  formik.errors.confirmationPassword
                    ? "border-red-500 focus:ring-red-200 bg-red-50"
                    : "border-blue-gray-200 focus:ring-blue-300 focus:border-blue-500 bg-white"
                }`}
              />
              <KeyRoundIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 text-blue-gray-400 size-5 lg:size-6" />
              <button
                type="button"
                onClick={toggleConfPwdVisibility}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-blue-gray-400 hover:text-blue-gray-600 transition-colors duration-200"
              >
                {isConfPwdVisible ? (
                  <EyeIcon className="size-5 lg:size-6" />
                ) : (
                  <EyeOffIcon className="size-5 lg:size-6" />
                )}
              </button>
            </div>
            {formik.touched.confirmationPassword &&
              formik.errors.confirmationPassword && (
                <p className="text-red-500 text-sm mt-1 ml-1">
                  {formik.errors.confirmationPassword}
                </p>
              )}
          </div>

          <button
            type="submit"
            disabled={loading || !formik.isValid}
            onMouseDown={(event) => ripple.create(event, "light")}
            className="relative overflow-hidden w-full bg-blue-gray-800 text-white py-4 px-6 rounded-xl font-medium text-base lg:text-lg transition-all duration-300 hover:bg-blue-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed group flex items-center justify-center gap-x-2"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white"></div>
                Resetting...
              </>
            ) : (
              "Reset password"
            )}
          </button>
        </form>

        <div className="text-center">
          <Link
            to="/auth/login"
            className="inline-flex items-center justify-center gap-2 text-blue-gray-600 hover:text-blue-gray-800 transition-colors duration-200 font-medium"
          >
            <ArrowLeftIcon className="w-4 h-4" />
            Back to login
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ResetPassword;
