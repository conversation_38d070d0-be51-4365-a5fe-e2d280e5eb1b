package com.storeflow.repositories;

import com.storeflow.models.CompanyProduct;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for CompanyProduct entity.
 */
@Repository
public interface CompanyProductRepository extends JpaRepository<CompanyProduct, UUID> {

    /**
     * Find a company product by label.
     *
     * @param label The product label
     * @return Optional containing the company product if found
     */
    Optional<CompanyProduct> findByLabel(String label);

    /**
     * Find a company product by product ID.
     *
     * @param productId The product ID
     * @return Optional containing the company product if found
     */
    Optional<CompanyProduct> findByProductId(UUID productId);

    /**
     * Find all company products with stock quantity greater than the specified value.
     *
     * @param stockQuantity The minimum stock quantity
     * @return List of company products with sufficient stock
     */
    List<CompanyProduct> findByStockQuantityGreaterThan(Integer stockQuantity);

    /**
     * Find a company product by label with stock quantity greater than the specified value.
     *
     * @param label The product label
     * @param stockQuantity The minimum stock quantity
     * @return Optional containing the company product if found and has sufficient stock
     */
    Optional<CompanyProduct> findByLabelAndStockQuantityGreaterThan(String label, Integer stockQuantity);

    /**
     * Find all company products with pagination.
     *
     * @param pageable The pagination information
     * @return Page of company products
     */
    Page<CompanyProduct> findAll(Pageable pageable);

    /**
     * Search company products by label or description with pagination.
     *
     * @param searchTerm The search term
     * @param pageable The pagination information
     * @return Page of matching company products
     */
    @Query("SELECT cp FROM CompanyProduct cp WHERE " +
           "LOWER(cp.label) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(cp.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(cp.product.brand) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<CompanyProduct> searchByLabelOrDescriptionOrBrand(@Param("searchTerm") String searchTerm, Pageable pageable);

    /**
     * Check if a company product exists by label.
     *
     * @param label The product label
     * @return true if exists, false otherwise
     */
    boolean existsByLabel(String label);

    /**
     * Check if a company product exists by product ID.
     *
     * @param productId The product ID
     * @return true if exists, false otherwise
     */
    boolean existsByProductId(UUID productId);

    /**
     * Count total number of company products.
     *
     * @return Total count of company products
     */
    long count();

    /**
     * Count company products with stock quantity greater than zero.
     *
     * @return Count of company products in stock
     */
    @Query("SELECT COUNT(cp) FROM CompanyProduct cp WHERE cp.stockQuantity > 0")
    long countInStock();

    /**
     * Calculate total inventory value.
     *
     * @return Total value of company inventory
     */
    @Query("SELECT COALESCE(SUM(cp.averageCostPrice * cp.stockQuantity), 0) FROM CompanyProduct cp")
    java.math.BigDecimal calculateTotalInventoryValue();
}
