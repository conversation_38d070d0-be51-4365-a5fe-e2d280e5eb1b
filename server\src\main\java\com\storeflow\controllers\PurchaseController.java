package com.storeflow.controllers;

import com.storeflow.DTOs.PurchaseDTO;
import com.storeflow.DTOs.PurchaseItemDTO;
import com.storeflow.DTOs.requests.CreatePurchaseRequest;
import com.storeflow.DTOs.requests.UpdatePurchaseStatusRequest;
import com.storeflow.DTOs.responses.MonthlyAnalyticsResponse;
import com.storeflow.responses.ApiResponse;
import com.storeflow.services.PurchaseService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.UUID;

/**
 * Controller for managing purchases from suppliers.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/purchases")
@RequiredArgsConstructor
public class PurchaseController {
    private final PurchaseService purchaseService;

    /**
     * Create a new purchase from a supplier.
     *
     * @param request The purchase request
     * @return The created purchase
     */
    @PostMapping
//    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE')")
    public ResponseEntity<ApiResponse<PurchaseDTO>> createPurchase(
            @Valid @RequestBody CreatePurchaseRequest request) {
        // Log the incoming request for debugging
        log.info("Received request to create purchase from supplier: {}", request.supplierId());
        log.info("Purchase items count: {}", request.items().size());

        // Process the purchase
        PurchaseDTO purchase = purchaseService.createPurchase(request);

        // Log the successful creation
        log.info("Purchase created successfully with ID: {}", purchase.id());

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(new ApiResponse<>(true, "Purchase created successfully", purchase));
    }

    /**
     * Update the status of a purchase.
     *
     * @param purchaseId The ID of the purchase
     * @param request    The update request
     * @return The updated purchase
     */
    @PutMapping("/{purchaseId}/status")
//    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE')")
    public ResponseEntity<ApiResponse<PurchaseDTO>> updatePurchaseStatus(
            @PathVariable UUID purchaseId,
            @Valid @RequestBody UpdatePurchaseStatusRequest request) {
        PurchaseDTO purchase = purchaseService.updatePurchaseStatus(purchaseId, request);
        return ResponseEntity.ok(new ApiResponse<>(true, "Purchase status updated successfully", purchase));
    }

    /**
     * Get a purchase by ID.
     *
     * @param purchaseId The ID of the purchase
     * @return The purchase
     */
    @GetMapping("/{purchaseId}")
//    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE', 'SUPPLIER')")
    public ResponseEntity<ApiResponse<PurchaseDTO>> getPurchaseById(@PathVariable UUID purchaseId) {
        PurchaseDTO purchase = purchaseService.getPurchaseById(purchaseId);

        // Log the purchase details for debugging
        log.info("Retrieved purchase with ID: {}", purchase.id());
        log.info("Purchase has {} items", purchase.items().size());

        // Log each item's details
        for (PurchaseItemDTO item : purchase.items()) {
            log.info("Item: {}, Product ID: {}, Image URL: {}, Brand: {}",
                    item.productLabel(), item.productId(), item.imageUrl(), item.brand());
        }

        return ResponseEntity.ok(new ApiResponse<>(true, "Purchase retrieved successfully", purchase));
    }

    /**
     * Get all purchases with pagination (Admin only - sees all purchases).
     *
     * @param pageable The pagination information
     * @return A page of purchases
     */
    @GetMapping
//    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<PurchaseDTO>>> getAllPurchases(Pageable pageable) {
        Page<PurchaseDTO> purchases = purchaseService.getAllPurchases(pageable);
        return ResponseEntity.ok(new ApiResponse<>(true, "Purchases retrieved successfully", purchases));
    }

    /**
     * Get purchases for the current user with pagination (Employee sees only their purchases).
     *
     * @param principal The authenticated user
     * @param pageable The pagination information
     * @return A page of purchases for the current user
     */
    @GetMapping("/my-purchases")
//    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE')")
    public ResponseEntity<ApiResponse<Page<PurchaseDTO>>> getMyPurchases(
            Principal principal,
            Pageable pageable) {
        Page<PurchaseDTO> purchases = purchaseService.getPurchasesForCurrentUser(principal, pageable);
        return ResponseEntity.ok(new ApiResponse<>(true, "Purchases retrieved successfully", purchases));
    }

    /**
     * Get all purchases for a supplier with pagination.
     *
     * @param supplierId The ID of the supplier
     * @param pageable   The pagination information
     * @return A page of purchases
     */
    @GetMapping("/supplier/{supplierId}")
//    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE', 'SUPPLIER')")
    public ResponseEntity<ApiResponse<Page<PurchaseDTO>>> getPurchasesBySupplier(
            @PathVariable UUID supplierId,
            Pageable pageable) {
        Page<PurchaseDTO> purchases = purchaseService.getPurchasesBySupplier(supplierId, pageable);
        return ResponseEntity.ok(new ApiResponse<>(true, "Purchases retrieved successfully", purchases));
    }

    /**
     * Get monthly analytics data for a supplier.
     *
     * @param supplierId The ID of the supplier
     * @return The monthly analytics data
     */
    @GetMapping("/supplier/{supplierId}/analytics/monthly")
//    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE', 'SUPPLIER')")
    public ResponseEntity<ApiResponse<MonthlyAnalyticsResponse>> getMonthlyAnalytics(
            @PathVariable UUID supplierId) {
        MonthlyAnalyticsResponse analytics = purchaseService.getMonthlyAnalytics(supplierId);
        return ResponseEntity.ok(new ApiResponse<>(true, "Monthly analytics retrieved successfully", analytics));
    }
}
