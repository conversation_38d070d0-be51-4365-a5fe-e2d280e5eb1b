package com.storeflow.repositories;

import com.storeflow.models.Employee;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface EmployeeRepository extends JpaRepository<Employee, UUID>, JpaSpecificationExecutor<Employee> {
    Optional<Employee> findByUserId(UUID userId);

    /**
     * Find employees with pagination and search by firstName, lastName, email, or phoneNumber
     *
     * @param searchQuery The search query to match against firstName, lastName, email, or phoneNumber
     * @param pageable The pagination information
     * @return A page of employees matching the search criteria
     */
    @Query("SELECT e FROM Employee e JOIN e.user u WHERE " +
           "LOWER(u.firstName) LIKE LOWER(CONCAT('%', :searchQuery, '%')) OR " +
           "LOWER(u.lastName) LIKE LOWER(CONCAT('%', :searchQuery, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :searchQuery, '%')) OR " +
           "LOWER(u.phoneNumber) LIKE LOWER(CONCAT('%', :searchQuery, '%'))")
    Page<Employee> findBySearchQuery(@Param("searchQuery") String searchQuery, Pageable pageable);
}
