package com.storeflow.services;

import com.storeflow.repositories.TokenRepository;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutHandler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Service
@RequiredArgsConstructor
public class LogoutService implements LogoutHandler {
    private final TokenRepository tokenRepository;

    @Override
    @Transactional
    public void logout(
        HttpServletRequest request,
        HttpServletResponse response,
        Authentication authentication
    ) {
        try {
            final String authHeader = request.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                log.warn("Logout attempt with missing or invalid authorization header");

                // For missing authorization header, use UNAUTHORIZED instead of BAD_REQUEST
                response.setStatus(HttpStatus.UNAUTHORIZED.value());

                // Add content type
                response.setContentType("application/json");

                // Create error response body with timestamp at the end
                String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME);
                String errorJson = String.format(
                    "{\"status\":%d,\"error\":\"%s\",\"message\":\"%s\",\"path\":\"%s\",\"timestamp\":\"%s\"}",
                    HttpStatus.UNAUTHORIZED.value(),
                    HttpStatus.UNAUTHORIZED.getReasonPhrase(),
                    "Authorization header with Bearer token is required",
                    request.getRequestURI(),
                    timestamp
                );

                // Write to the response
                response.getWriter().write(errorJson);
                return;
            }

            final String jwt = authHeader.substring(7);
            var storedToken = tokenRepository.findByToken(jwt);

            if (storedToken.isEmpty()) {
                log.warn("Logout attempt with unknown token");

                // For invalid token, use FORBIDDEN status
                response.setStatus(HttpStatus.FORBIDDEN.value());

                // Add content type
                response.setContentType("application/json");

                // Create error response body with timestamp at the end
                String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME);
                String errorJson = String.format(
                    "{\"status\":%d,\"error\":\"%s\",\"message\":\"%s\",\"path\":\"%s\",\"timestamp\":\"%s\"}",
                    HttpStatus.FORBIDDEN.value(),
                    HttpStatus.FORBIDDEN.getReasonPhrase(),
                    "Invalid token",
                    request.getRequestURI(),
                    timestamp
                );

                // Write to the response
                response.getWriter().write(errorJson);
                return;
            }

            // Check if token is already expired or revoked
            var token = storedToken.get();
            if (token.isExpired() || token.isRevoked()) {
                log.warn("Logout attempt with already expired or revoked token");

                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setContentType("application/json");

                String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME);
                String errorJson = String.format(
                    "{\"status\":%d,\"error\":\"%s\",\"message\":\"%s\",\"path\":\"%s\",\"timestamp\":\"%s\"}",
                    HttpStatus.FORBIDDEN.value(),
                    HttpStatus.FORBIDDEN.getReasonPhrase(),
                    "Token is already expired or revoked",
                    request.getRequestURI(),
                    timestamp
                );

                response.getWriter().write(errorJson);
                return;
            }

            // Process the logout
            token.setExpired(true);
            token.setRevoked(true);
            tokenRepository.save(token);

            // Also revoke all tokens for this user (optional but more secure)
            tokenRepository.findAllValidTokensByUser(token.getUser().getId())
                .forEach(userToken -> {
                    userToken.setExpired(true);
                    userToken.setRevoked(true);
                    tokenRepository.save(userToken);
                });

            log.info("User logged out successfully: {}",
                authentication != null ? authentication.getName() : token.getUser().getEmail());

            // Return success response with timestamp at the end
            response.setStatus(HttpStatus.OK.value());
            response.setContentType("application/json");

            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME);
            String successJson = String.format(
                "{\"status\":%d,\"message\":\"%s\",\"timestamp\":\"%s\"}",
                HttpStatus.OK.value(),
                "Logged out successfully",
                timestamp
            );

            response.getWriter().write(successJson);

        } catch (Exception e) {
            log.error("Error during logout: ", e);
            try {
                response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
                response.setContentType("application/json");

                String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME);
                String errorJson = String.format(
                    "{\"status\":%d,\"error\":\"%s\",\"message\":\"%s\",\"path\":\"%s\",\"timestamp\":\"%s\"}",
                    HttpStatus.INTERNAL_SERVER_ERROR.value(),
                    HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase(),
                    "An error occurred during logout",
                    request.getRequestURI(),
                    timestamp
                );

                response.getWriter().write(errorJson);
            } catch (IOException ex) {
                log.error("Error writing to response", ex);
            }
        }
    }
}
