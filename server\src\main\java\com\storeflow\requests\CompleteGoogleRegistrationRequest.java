package com.storeflow.requests;

import com.storeflow.validation.PhoneNumber;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;

public record CompleteGoogleRegistrationRequest(
    @NotBlank(message = "Phone number is required.")
    @PhoneNumber
    String phoneNumber,

    @NotBlank(message = "First name is required.")
    String firstName,

    @NotBlank(message = "Last name is required.")
    String lastName,

    @NotBlank(message = "Email is required.")
    @Email(message = "Invalid email format.")
    String email,

    @NotBlank(message = "Google ID is required.")
    String googleId,

    String profilePictureUrl
) {}
