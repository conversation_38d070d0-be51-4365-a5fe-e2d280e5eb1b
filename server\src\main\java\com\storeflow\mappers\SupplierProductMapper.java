package com.storeflow.mappers;

import com.storeflow.DTOs.SupplierProductDTO;
import com.storeflow.models.SupplierProduct;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.data.domain.Page;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SupplierProductMapper {

    @Mapping(source = "id.productId", target = "productId")
    @Mapping(source = "product.brand", target = "brand")
    @Mapping(source = "product.imageUrl", target = "imageUrl")
    SupplierProductDTO toSupplierProductDTO(SupplierProduct supplierProduct);

    List<SupplierProductDTO> toSupplierProductDTOs(List<SupplierProduct> supplierProducts);

    // Helper method to convert Page<SupplierProduct> to Page<SupplierProductDTO>
    default Page<SupplierProductDTO> toSupplierProductDTOPage(Page<SupplierProduct> supplierProductPage) {
        return supplierProductPage.map(this::toSupplierProductDTO);
    }
}