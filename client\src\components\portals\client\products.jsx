import { useState, useEffect } from "react";
import {
  ShoppingCartIcon,
  SearchIcon,
  PlusIcon,
  MinusIcon,
  EyeIcon,
  PackageIcon,
  Loader2,
  ChevronLeftIcon,
  ChevronRightIcon,
  XIcon,
  ShoppingBagIcon,
} from "lucide-react";
import { catalogService } from "@/services/catalog-service";
import { orderService } from "@/services/order-service";
import { toast } from "sonner";
import { useAuth } from "@/context/auth-context";
import { useSelector } from "react-redux";
import { isDesktop, ripple } from "@/utils";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

export const Products = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [cart, setCart] = useState([]);
  const [showCart, setShowCart] = useState(false);
  const [orderLoading, setOrderLoading] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [showProductDetails, setShowProductDetails] = useState(false);
  const [pagination, setPagination] = useState({
    page: 0,
    size: 12,
    totalElements: 0,
    totalPages: 0,
  });

  // Get user data from Redux store instead of auth context
  const { userData: user } = useSelector((state) => state.user);

  useEffect(() => {
    fetchProducts();
  }, [pagination.page]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await catalogService.getAvailableProducts({
        page: pagination.page,
        size: pagination.size,
        sortBy: "label",
        sortDir: "asc",
      });

      if (response.success) {
        setProducts(response.data.content);
        setPagination((prev) => ({
          ...prev,
          totalElements: response.data.totalElements,
          totalPages: response.data.totalPages,
        }));
      }
    } catch (error) {
      console.error("Error fetching products:", error);
      toast.error("Failed to load products");
    } finally {
      setLoading(false);
    }
  };

  const addToCart = (product) => {
    // Check if product is out of stock
    if (product.totalAvailableQuantity === 0) {
      toast.info(
        `Sorry, "${product.label}" is currently out of stock. Please check back later!`
      );
      return;
    }

    setCart((prevCart) => {
      const existingItem = prevCart.find(
        (item) => item.label === product.label
      );
      if (existingItem) {
        return prevCart.map((item) =>
          item.label === product.label
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        return [...prevCart, { ...product, quantity: 1 }];
      }
    });
    toast.success(`${product.label} added to cart`);
  };

  const removeFromCart = (productLabel) => {
    setCart((prevCart) =>
      prevCart.filter((item) => item.label !== productLabel)
    );
  };

  const updateCartQuantity = (productLabel, newQuantity) => {
    if (newQuantity <= 0) {
      removeFromCart(productLabel);
      return;
    }

    setCart((prevCart) =>
      prevCart.map((item) =>
        item.label === productLabel ? { ...item, quantity: newQuantity } : item
      )
    );
  };

  const getTotalPrice = () => {
    return cart.reduce(
      (total, item) => total + item.averagePrice * item.quantity,
      0
    );
  };

  const handlePlaceOrder = async () => {
    if (cart.length === 0) {
      toast.error("Your cart is empty");
      return;
    }

    try {
      setOrderLoading(true);

      const orderData = {
        items: cart.map((item) => ({
          productLabel: item.label,
          quantity: item.quantity,
        })),
        notes: "",
        phoneNumber: user?.phoneNumber || "",
      };

      const response = await orderService.createOrder(orderData);

      if (response.success) {
        toast.success("Order placed successfully!");
        setCart([]);
        setShowCart(false);
      }
    } catch (error) {
      console.error("Error placing order:", error);
      toast.error("Failed to place order");
    } finally {
      setOrderLoading(false);
    }
  };

  const handleViewProduct = (product) => {
    setSelectedProduct(product);
    setShowProductDetails(true);
  };

  const filteredProducts = products.filter(
    (product) =>
      product.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.brand?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mb-2" />
          <p className="text-blue-gray-500 text-lg">Loading products...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen mx-auto space-y-4">
      {/* Page header with title and search */}
      <div className="bg-white rounded-xl shadow-sm border border-blue-gray-200 p-4 sm:p-6">
        <div className="flex flex-col gap-4">
          {/* Title Section */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div className="flex-1 min-w-0">
              <h1 className="text-xl sm:text-2xl font-bold text-blue-gray-900 flex items-center gap-2">
                <PackageIcon className="h-5 w-5 sm:h-6 sm:w-6 text-blue-gray-700 flex-shrink-0" />
                <span className="truncate">Products Catalog</span>
              </h1>
              <p className="text-blue-gray-500 mt-1 text-sm sm:text-base">
                Browse and order products from our catalog
              </p>
            </div>

            {/* Cart Button - Desktop */}
            <div className="hidden sm:block">
              <button
                onMouseDown={(event) =>
                  ripple.create(event, isDesktop() ? "light" : "dark")
                }
                onClick={() => setShowCart(!showCart)}
                className="group cursor-pointer text-sm text-white bg-blue-gray-900 border border-blue-gray-800 shadow-dark flex items-center justify-center gap-2 px-4 py-3 rounded-full font-medium transition-all duration-300 hover:bg-transparent hover:text-blue-gray-800 hover:border-blue-gray-50 hover:shadow-darker active:shadow-darker"
                type="button"
              >
                <ShoppingCartIcon className="text-white transition-all duration-300 group-hover:text-blue-gray-800 size-4 flex-shrink-0" />
                <span>Cart ({cart.length})</span>
              </button>
            </div>
          </div>

          {/* Search Bar */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <SearchIcon className="h-5 w-5 text-blue-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full pl-10 pr-3 py-3 border border-blue-gray-200 rounded-lg leading-5 bg-white placeholder-blue-gray-400 focus:outline-none focus:placeholder-blue-gray-500 focus:ring-1 focus:ring-blue-gray-500 focus:border-blue-gray-500 text-blue-gray-900"
            />
          </div>

          {/* Cart Button - Mobile */}
          <div className="sm:hidden">
            <button
              onMouseDown={(event) =>
                ripple.create(event, isDesktop() ? "light" : "dark")
              }
              onClick={() => setShowCart(!showCart)}
              className="group cursor-pointer text-sm text-white bg-blue-gray-900 border border-blue-gray-800 shadow-dark flex items-center justify-center gap-2 px-4 py-3 rounded-full font-medium transition-all duration-300 hover:bg-transparent hover:text-blue-gray-800 hover:border-blue-gray-50 hover:shadow-darker active:shadow-darker w-full"
              type="button"
            >
              <ShoppingCartIcon className="text-white transition-all duration-300 group-hover:text-blue-gray-800 size-4 flex-shrink-0" />
              <span>Cart ({cart.length})</span>
            </button>
          </div>
        </div>
      </div>

      {/* Shopping Cart */}
      {showCart && (
        <div className="bg-white rounded-xl shadow-sm border border-blue-gray-200 p-4 sm:p-6">
          <h2 className="text-lg font-semibold text-blue-gray-900 mb-4 flex items-center gap-2">
            <ShoppingBagIcon className="h-5 w-5 text-blue-gray-700" />
            Shopping Cart
          </h2>
          {cart.length === 0 ? (
            <div className="text-center py-8">
              <ShoppingBagIcon className="h-12 w-12 text-blue-gray-300 mx-auto mb-3" />
              <p className="text-blue-gray-500">Your cart is empty</p>
            </div>
          ) : (
            <>
              <div className="space-y-3 mb-4">
                {cart.map((item) => (
                  <div
                    key={item.label}
                    className="flex items-center gap-4 p-3 border border-blue-gray-200 rounded-lg"
                  >
                    <div className="w-12 h-12 bg-white rounded-lg overflow-hidden border border-blue-gray-100 flex-shrink-0">
                      <img
                        src={item.imageUrl || "/img/default-product.jpg"}
                        alt={item.label}
                        className="w-full h-full object-contain"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-blue-gray-900 truncate">
                        {item.label}
                      </h3>
                      <p className="text-sm text-blue-gray-500">
                        {item.averagePrice?.toFixed(2)} MAD each
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onMouseDown={(event) => ripple.create(event, "dark")}
                        onClick={() =>
                          updateCartQuantity(item.label, item.quantity - 1)
                        }
                        className="p-1 rounded-lg border border-blue-gray-200 hover:bg-blue-gray-50 transition-colors"
                        type="button"
                      >
                        <MinusIcon className="h-4 w-4 text-blue-gray-600" />
                      </button>
                      <span className="w-8 text-center font-medium text-blue-gray-900">
                        {item.quantity}
                      </span>
                      <button
                        onMouseDown={(event) => ripple.create(event, "dark")}
                        onClick={() =>
                          updateCartQuantity(item.label, item.quantity + 1)
                        }
                        className="p-1 rounded-lg border border-blue-gray-200 hover:bg-blue-gray-50 transition-colors"
                        type="button"
                      >
                        <PlusIcon className="h-4 w-4 text-blue-gray-600" />
                      </button>
                      <button
                        onMouseDown={(event) => ripple.create(event, "dark")}
                        onClick={() => removeFromCart(item.label)}
                        className="ml-2 px-2 py-1 text-red-600 hover:bg-red-50 rounded-lg transition-colors text-sm"
                        type="button"
                      >
                        Remove
                      </button>
                    </div>
                  </div>
                ))}
              </div>
              <div className="border-t border-blue-gray-200 pt-4">
                <div className="flex justify-between items-center mb-4">
                  <span className="text-lg font-semibold text-blue-gray-900">
                    Total: {getTotalPrice().toFixed(2)} MAD
                  </span>
                </div>
                <button
                  onMouseDown={(event) =>
                    ripple.create(event, isDesktop() ? "light" : "dark")
                  }
                  onClick={handlePlaceOrder}
                  disabled={orderLoading}
                  className={`w-full text-base text-white bg-green-600 border border-green-500 shadow-dark flex items-center justify-center gap-2 px-4 py-3 rounded-full font-medium transition-all duration-300 hover:bg-green-700 hover:shadow-darker active:shadow-darker ${
                    orderLoading ? "opacity-70 cursor-not-allowed" : ""
                  }`}
                  type="button"
                >
                  {orderLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Placing Order...</span>
                    </>
                  ) : (
                    <span>Place Order</span>
                  )}
                </button>
              </div>
            </>
          )}
        </div>
      )}

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredProducts.length === 0 ? (
          <div className="col-span-3 py-12 text-center">
            <div className="flex flex-col items-center justify-center">
              <PackageIcon className="h-12 w-12 text-blue-gray-300 mb-3" />
              <p className="text-blue-gray-500 text-lg">No products found</p>
            </div>
          </div>
        ) : (
          filteredProducts.map((product) => (
            <Card
              key={product.label}
              className="overflow-hidden border-blue-gray-200 rounded-xl hover:shadow-md transition-all duration-300"
            >
              {/* Product image - Fixed height container like in PurchaseProducts */}
              <div className="h-48 relative bg-white flex items-center justify-center p-4 border-b border-blue-gray-100">
                <div className="w-full h-40 flex items-center justify-center">
                  {product.imageUrl ? (
                    <img
                      src={product.imageUrl}
                      alt={product.label}
                      className="max-h-full max-w-full object-contain"
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = "/img/default-product.jpg";
                      }}
                    />
                  ) : (
                    <div className="flex flex-col items-center justify-center text-blue-gray-300">
                      <PackageIcon className="h-12 w-12 mb-2" />
                      <span className="text-xs text-blue-gray-400">
                        No image available
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Product details */}
              <CardContent className="p-4">
                {/* Product title - Fixed height */}
                <div className="h-7 mb-2">
                  <h3 className="font-semibold text-lg text-blue-gray-900 line-clamp-1">
                    {product.label}
                  </h3>
                </div>

                {/* Brand */}
                {product.brand && (
                  <div className="mb-2">
                    <p className="text-sm text-blue-gray-600">
                      {product.brand}
                    </p>
                  </div>
                )}

                {/* Description - Fixed height with truncation */}
                <div className="text-blue-gray-500 text-sm h-10 mb-3">
                  <p className="line-clamp-2">
                    {product.description && product.description.length > 60
                      ? `${product.description.substring(0, 60)}...`
                      : product.description || "No description available"}
                  </p>
                </div>

                {/* Price and availability */}
                <div className="flex justify-between items-center mb-4">
                  <div className="flex flex-col">
                    <span className="text-lg font-bold text-green-600">
                      {product.averagePrice?.toFixed(2)} MAD
                    </span>
                  </div>
                  <div className="text-right">
                    <span className="text-xs text-blue-gray-500">
                      Available: {product.totalAvailableQuantity}
                    </span>
                  </div>
                </div>

                {/* Action buttons */}
                <div className="flex gap-2">
                  <button
                    onMouseDown={(event) => ripple.create(event, "dark")}
                    onClick={() => handleViewProduct(product)}
                    className="flex-1 py-2 px-3 text-blue-600 rounded-lg hover:bg-blue-50 transition-all duration-300 flex items-center justify-center gap-1 text-sm font-medium"
                    type="button"
                  >
                    <EyeIcon className="h-4 w-4" />
                    <span className="hidden sm:inline">Details</span>
                  </button>
                  <button
                    onMouseDown={(event) => ripple.create(event, "light")}
                    onClick={() => addToCart(product)}
                    className={`flex-1 py-2 px-3 rounded-lg font-medium transition-all duration-300 flex items-center justify-center gap-1 text-sm cursor-pointer ${
                      product.totalAvailableQuantity === 0
                        ? "bg-blue-gray-100 text-blue-gray-400"
                        : "bg-blue-gray-900 text-white hover:bg-blue-gray-800 hover:shadow-md"
                    }`}
                    type="button"
                  >
                    <ShoppingCartIcon className="h-4 w-4" />
                    <span className="hidden sm:inline">
                      {product.totalAvailableQuantity === 0
                        ? "Out of Stock"
                        : "Add to Cart"}
                    </span>
                  </button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center items-center gap-4 mt-8">
          <button
            onMouseDown={(event) => ripple.create(event, "dark")}
            onClick={() =>
              setPagination((prev) => ({ ...prev, page: prev.page - 1 }))
            }
            disabled={pagination.page === 0}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 ${
              pagination.page === 0
                ? "border-blue-gray-200 text-blue-gray-400 cursor-not-allowed"
                : "border-blue-gray-300 text-blue-gray-700 hover:bg-blue-gray-50 hover:border-blue-gray-400"
            }`}
            type="button"
          >
            <ChevronLeftIcon className="h-4 w-4" />
            <span>Previous</span>
          </button>

          <span className="text-blue-gray-600 font-medium">
            Page {pagination.page + 1} of {pagination.totalPages}
          </span>

          <button
            onMouseDown={(event) => ripple.create(event, "dark")}
            onClick={() =>
              setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
            }
            disabled={pagination.page >= pagination.totalPages - 1}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 ${
              pagination.page >= pagination.totalPages - 1
                ? "border-blue-gray-200 text-blue-gray-400 cursor-not-allowed"
                : "border-blue-gray-300 text-blue-gray-700 hover:bg-blue-gray-50 hover:border-blue-gray-400"
            }`}
            type="button"
          >
            <span>Next</span>
            <ChevronRightIcon className="h-4 w-4" />
          </button>
        </div>
      )}

      {/* Product Details Modal */}
      {selectedProduct && (
        <Dialog open={showProductDetails} onOpenChange={setShowProductDetails}>
          <DialogContent className="sm:max-w-2xl p-0 overflow-hidden">
            <div className="max-h-[90vh] overflow-y-auto scrollbar-thin scrollbar-thumb-blue-400 hover:scrollbar-thumb-blue-500 scrollbar-track-transparent scrollbar-thumb-rounded-md px-4 md:px-6 pt-4 md:pt-6 pb-6">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <PackageIcon className="h-5 w-5 text-blue-gray-700" />
                  Product Details
                </DialogTitle>
              </DialogHeader>

              <div className="space-y-6">
                {/* Product Image */}
                <div className="flex justify-center">
                  <div className="w-64 h-64 bg-white rounded-lg border border-blue-gray-200 flex items-center justify-center p-4">
                    {selectedProduct.imageUrl ? (
                      <img
                        src={selectedProduct.imageUrl}
                        alt={selectedProduct.label}
                        className="max-h-full max-w-full object-contain"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = "/img/default-product.jpg";
                        }}
                      />
                    ) : (
                      <div className="flex flex-col items-center justify-center text-blue-gray-300">
                        <PackageIcon className="h-16 w-16 mb-2" />
                        <span className="text-sm text-blue-gray-400">
                          No image available
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Product Information */}
                <div className="space-y-4">
                  <div>
                    <h3 className="text-xl font-bold text-blue-gray-900 mb-2">
                      {selectedProduct.label}
                    </h3>
                    {selectedProduct.brand && (
                      <p className="text-blue-gray-600 font-medium">
                        Brand: {selectedProduct.brand}
                      </p>
                    )}
                  </div>

                  <div>
                    <h4 className="font-semibold text-blue-gray-900 mb-2">
                      Description
                    </h4>
                    <p className="text-blue-gray-600 leading-relaxed">
                      {selectedProduct.description ||
                        "No description available"}
                    </p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold text-blue-gray-900 mb-1">
                        Price
                      </h4>
                      <p className="text-2xl font-bold text-green-600">
                        {selectedProduct.averagePrice?.toFixed(2)} MAD
                      </p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-blue-gray-900 mb-1">
                        Availability
                      </h4>
                      <p className="text-lg text-blue-gray-600">
                        {selectedProduct.totalAvailableQuantity} units
                      </p>
                    </div>
                  </div>

                  {selectedProduct.averageDeliveryTime && (
                    <div>
                      <h4 className="font-semibold text-blue-gray-900 mb-1">
                        Delivery Time
                      </h4>
                      <p className="text-blue-gray-600">
                        {selectedProduct.averageDeliveryTime} days
                      </p>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4 border-t border-blue-gray-200">
                  <button
                    onMouseDown={(event) => ripple.create(event, "dark")}
                    onClick={() => setShowProductDetails(false)}
                    className="flex-1 px-4 py-3 rounded-xl border border-blue-gray-200 bg-white text-blue-gray-700 transition-colors text-base font-medium hover:bg-blue-gray-50"
                    type="button"
                  >
                    Close
                  </button>
                  <button
                    onMouseDown={(event) => ripple.create(event, "light")}
                    onClick={() => {
                      if (selectedProduct.totalAvailableQuantity === 0) {
                        toast.info(
                          `Sorry, "${selectedProduct.label}" is currently out of stock. Please check back later!`
                        );
                        return;
                      }
                      addToCart(selectedProduct);
                      setShowProductDetails(false);
                    }}
                    className={`flex-1 px-4 py-3 rounded-xl transition-colors flex items-center justify-center gap-2 text-base font-medium ${
                      selectedProduct.totalAvailableQuantity === 0
                        ? "bg-blue-gray-100 text-blue-gray-400 cursor-pointer"
                        : "bg-blue-gray-900 text-white hover:bg-blue-gray-800"
                    }`}
                    type="button"
                  >
                    <ShoppingCartIcon className="h-4 w-4" />
                    {selectedProduct.totalAvailableQuantity === 0
                      ? "Out of Stock"
                      : "Add to Cart"}
                  </button>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default Products;
