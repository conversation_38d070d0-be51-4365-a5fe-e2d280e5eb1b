# OAuth2 Google Authentication Setup

This document explains how to set up Google OAuth2 authentication for the StoreFlow application.

## Required Environment Variables

Add the following environment variables to your system or IDE configuration:

```bash
# Google OAuth2 Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
FRONTEND_URL=http://localhost:5173
```

## Google Cloud Console Setup

1. **Create a Google Cloud Project**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select an existing one

2. **Enable Google+ API**
   - Navigate to "APIs & Services" > "Library"
   - Search for "Google+ API" and enable it
   - Also enable "Google OAuth2 API"

3. **Create OAuth2 Credentials**
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Web application" as the application type
   - Add authorized redirect URIs:
     - `http://localhost:8080/api/v1/auth/oauth2/callback/google`
     - `http://localhost:8080/login/oauth2/code/google`

4. **Configure OAuth Consent Screen**
   - Go to "APIs & Services" > "OAuth consent screen"
   - Choose "External" user type (for testing)
   - Fill in the required information:
     - App name: StoreFlow
     - User support email: your email
     - Developer contact information: your email
   - Add scopes: email, profile, openid

## Backend Configuration

The backend is already configured with the necessary OAuth2 settings in:
- `application.properties` - OAuth2 client configuration
- `SecurityConfiguration.java` - Security filter chain with OAuth2 login
- `OAuth2AuthenticationSuccessHandler.java` - Handles successful authentication
- `OAuth2AuthenticationFailureHandler.java` - Handles authentication failures

## Frontend Integration

The frontend includes:
- Google OAuth2 login button in the login component
- OAuth2 success page for handling authentication completion
- OAuth2 error page for handling authentication failures
- Complete Google registration page for phone number completion

## Testing OAuth2 Flow

1. **Start the backend server**
   ```bash
   cd server
   ./mvnw spring-boot:run
   ```

2. **Start the frontend development server**
   ```bash
   cd client
   npm run dev
   ```

3. **Test the OAuth2 flow**
   - Navigate to `http://localhost:5173/auth/login`
   - Click the "Continue with Google" button
   - Complete the Google authentication
   - Provide phone number if prompted
   - You should be redirected to the appropriate dashboard

## Troubleshooting

### Common Issues

1. **"redirect_uri_mismatch" error**
   - Ensure the redirect URI in Google Cloud Console matches exactly: `http://localhost:8080/api/v1/auth/oauth2/callback/google`

2. **"invalid_client" error**
   - Check that GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET are correctly set
   - Verify the credentials in Google Cloud Console

3. **"access_denied" error**
   - User cancelled the authentication or denied permissions
   - Check OAuth consent screen configuration

4. **ClassCastException in ApplicationAuditAware**
   - This has been fixed to handle both User and OAuth2User principals

### Debug Logging

Enable debug logging for OAuth2 by adding to `application.properties`:
```properties
logging.level.org.springframework.security.oauth2=DEBUG
logging.level.com.storeflow.configurations=DEBUG
```

## Security Notes

- Never commit OAuth2 credentials to version control
- Use environment variables for all sensitive configuration
- In production, update redirect URIs to use HTTPS and your production domain
- Consider implementing additional security measures like CSRF protection for OAuth2 flows

## Production Deployment

For production deployment:

1. **Update redirect URIs** in Google Cloud Console to use your production domain
2. **Set production environment variables**:
   ```bash
   GOOGLE_CLIENT_ID=your_production_client_id
   GOOGLE_CLIENT_SECRET=your_production_client_secret
   FRONTEND_URL=https://your-production-domain.com
   ```
3. **Enable HTTPS** for all OAuth2 endpoints
4. **Update OAuth consent screen** to use production app information
