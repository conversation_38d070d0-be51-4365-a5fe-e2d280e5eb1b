import { Profile } from "@/components/portals";
import { Dashboard, Products, Orders } from "@/components/portals/client";
import {
  CircleUserRoundIcon,
  HouseIcon,
  ShoppingCartIcon,
  PackageIcon,
} from "lucide-react";

const icon = {
  className: "size-[25px] text-inherit",
};

export const routes = [
  {
    icon: <HouseIcon {...icon} />,
    name: "dashboard",
    path: "/dashboard",
    element: <Dashboard />,
  },
  {
    icon: <CircleUserRoundIcon {...icon} />,
    name: "profile",
    path: "/profile",
    element: <Profile />,
  },
  {
    icon: <ShoppingCartIcon {...icon} />,
    name: "products",
    path: "/products",
    element: <Products />,
  },
  {
    icon: <PackageIcon {...icon} />,
    name: "orders",
    path: "/orders",
    element: <Orders />,
  },
];

export default routes;
