package com.storeflow.requests;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;

public record SupplierProductUpdateRequest(
    @Size(min = 2, max = 100, message = "Label must be between 2 and 100 characters")
    String label,

    @Size(min = 10, message = "Description must contain at least 10 characters")
    String description,

    @Size(max = 50, message = "Brand name must be less than 50 characters")
    String brand,

    String imageUrl,

    @Min(value = 0, message = "Stock quantity must be zero or positive")
    Integer stockQuantity,

    @DecimalMin(value = "0.01", message = "Purchase price must be greater than 0")
    @Digits(integer = 8, fraction = 2, message = "Purchase price must have at most 8 integer digits and 2 fraction digits")
    BigDecimal purchasePrice,

    @DecimalMin(value = "0.01", message = "Selling price must be greater than 0")
    @Digits(integer = 8, fraction = 2, message = "Selling price must have at most 8 integer digits and 2 fraction digits")
    BigDecimal sellingPrice,

    @Min(value = 1, message = "Delivery time must be at least 1 day")
    Integer deliveryTime
) {
}
