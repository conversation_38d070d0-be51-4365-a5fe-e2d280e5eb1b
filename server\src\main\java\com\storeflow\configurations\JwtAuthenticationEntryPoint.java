package com.storeflow.configurations;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.storeflow.responses.ErrorResponse;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws IOException, ServletException {

        HttpStatus status = HttpStatus.UNAUTHORIZED;
        response.setStatus(status.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);

        // Get the original request URL from either the "javax.servlet.forward.request_uri" attribute
        // or fall back to the actual request URI if not available
        String requestUri = (String) request.getAttribute("javax.servlet.forward.request_uri");
        if (requestUri == null) {
            requestUri = request.getRequestURI();
        }

        String message = "Authentication failed: " + authException.getMessage() + ".";

        // Create ErrorResponse instance
        ErrorResponse errorResponse = new ErrorResponse(
            status.value(),
            message,
            requestUri
        );

        // Configure ObjectMapper to handle Java 8 date/time types
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // Write the error response as JSON to the response body
        mapper.writeValue(response.getOutputStream(), errorResponse);
    }
}
