package com.storeflow.services;

import com.storeflow.DTOs.SupplierDTO;
import com.storeflow.enums.Role;
import com.storeflow.exception.DuplicateResourceException;
import com.storeflow.exception.ResourceNotFoundException;
import com.storeflow.mappers.SupplierMapper;
import com.storeflow.models.Supplier;
import com.storeflow.models.SupplierProduct;
import com.storeflow.models.User;
import com.storeflow.repositories.ProductRepository;
import com.storeflow.repositories.SupplierProductRepository;
import com.storeflow.repositories.SupplierRepository;
import com.storeflow.repositories.TokenRepository;
import com.storeflow.repositories.UserRepository;
import com.storeflow.requests.SupplierAdditionRequest;
import com.storeflow.utils.PhoneNumberFormatter;
import com.storeflow.utils.SecurePasswordGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.security.Principal;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierService {
    private final FileService fileService;
    private final UserRepository userRepository;
    private final TokenRepository tokenRepository;
    private final SupplierMapper supplierMapper;
    private final PasswordEncoder passwordEncoder;
    private final ProductRepository productRepository;
    private final SupplierRepository supplierRepository;
    private final SupplierProductRepository supplierProductRepository;
    private final PhoneNumberFormatter phoneNumberFormatter;
    private final SecurePasswordGenerator securePasswordGenerator;

    /**
     * Retrieves all suppliers in the system.
     *
     * @return A list of all suppliers
     * @deprecated Use {@link #getAllSuppliers(String, Pageable)} instead for pagination and search
     */
    @Deprecated
    public List<SupplierDTO> getAllSuppliers() {
        List<Supplier> suppliers = supplierRepository.findAll();
        return supplierMapper.toSupplierDTOs(suppliers);
    }

    /**
     * Get all suppliers with pagination and search functionality
     *
     * @param searchQuery The search query to filter suppliers (can be null or empty)
     * @param pageable The pagination information
     * @return A page of supplier DTOs
     */
    @Transactional(readOnly = true)
    public Page<SupplierDTO> getAllSuppliers(String searchQuery, Pageable pageable) {
        log.info("Fetching suppliers with search query: '{}', page: {}, size: {}",
                searchQuery, pageable.getPageNumber(), pageable.getPageSize());

        Page<Supplier> supplierPage;

        if (StringUtils.hasText(searchQuery)) {
            // If search query is provided, use the search method
            supplierPage = supplierRepository.findBySearchQuery(searchQuery, pageable);
            log.debug("Found {} suppliers matching search query '{}'", supplierPage.getTotalElements(), searchQuery);
        } else {
            // Otherwise, get all suppliers with pagination
            supplierPage = supplierRepository.findAll(pageable);
            log.debug("Found {} total suppliers", supplierPage.getTotalElements());
        }

        // Map to DTOs and return
        return supplierPage.map(supplierMapper::toSupplierDTO);
    }

    /**
     * Adds a new supplier to the system.
     *
     * @param supplierDetails The details of the supplier to add
     * @return The UUID of the newly created supplier
     * @throws DuplicateResourceException if the email or phone number already exists
     */
    @Transactional
    public UUID addSupplier(SupplierAdditionRequest supplierDetails) {
        // Check if email already exists
        String employeeEmail = supplierDetails.email();
        if (userRepository.existsByEmail(employeeEmail)) {
            log.warn("Registration attempt with existing email: {}", employeeEmail);
            throw new DuplicateResourceException("Email already exists");
        }

        // Check if phone number already exists
        String employeeFormattedPhoneNumber = phoneNumberFormatter.formatToInternational(supplierDetails.phoneNumber());
        if (userRepository.existsByPhoneNumber(employeeFormattedPhoneNumber)) {
            log.warn("Registration attempt with existing phone number: {}", employeeFormattedPhoneNumber);
            throw new DuplicateResourceException("Phone number already exists");
        }

        // Generate a secure password
        String generatedPassword = securePasswordGenerator.generateSecurePassword();

        // Create the base user
        User user = User.builder()
            .firstName(supplierDetails.firstName().trim())
            .lastName(supplierDetails.lastName().trim())
            .email(employeeEmail)
            .phoneNumber(employeeFormattedPhoneNumber)
            .password(passwordEncoder.encode(generatedPassword))
            .role(Role.SUPPLIER)
            .profilePictureUrl(fileService.getDefaultProfilePictureUrl())
            .build();

        userRepository.save(user);

        // Create the supplier
        Supplier supplier = Supplier.builder()
            .user(user)
            .localization(supplierDetails.localization())
            .build();

        supplierRepository.save(supplier);

        // Log credentials in development mode
        log.info("""
        \n
        ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
          SUPPLIER CREATED - CREDENTIALS (DEV MODE)       \s
                                                          \s
          Phone Number: {}                                \s
          Email: {}                                       \s
          Password: {}                                    \s
        ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛""",
            user.getPhoneNumber(), user.getEmail(), generatedPassword);

        return user.getId();
    }

    /**
     * Deletes a supplier from the system along with all their products.
     *
     * @param supplierId The ID of the supplier to delete
     * @throws ResourceNotFoundException if the supplier doesn't exist
     */
    @Transactional
    public void deleteSupplier(UUID supplierId) {
        // Verify the supplier exists
        Supplier supplier = supplierRepository.findById(supplierId)
            .orElseThrow(() -> {
                log.warn("Attempted to delete non-existent supplier with ID: {}", supplierId);
                return new ResourceNotFoundException("Supplier not found with ID: " + supplierId);
            });

        User user = supplier.getUser();
        String supplierEmail = user.getEmail();
        String supplierPhone = user.getPhoneNumber();

        log.info("Deleting supplier {} ({} {}) and associated products",
            supplierId, user.getFirstName(), user.getLastName());

        // Get all supplier-product associations for this supplier
        List<SupplierProduct> supplierProducts = supplierProductRepository.findAllBySupplierId(supplierId);
        log.info("Found {} products in supplier's inventory to be processed", supplierProducts.size());

        // Process each product
        for (SupplierProduct supplierProduct : supplierProducts) {
            UUID productId = supplierProduct.getProduct().getId();

            // Remove the supplier-product association
            supplierProductRepository.delete(supplierProduct);
            log.debug("Deleted supplier-product association for product: {}", productId);

            // Check if this was the last supplier for this product
            boolean hasOtherSuppliers = supplierProductRepository.existsByProductId(productId);

            // If no other suppliers have this product, delete the product too
            if (!hasOtherSuppliers) {
                log.debug("Product {} has no other suppliers, deleting base product", productId);
                productRepository.deleteById(productId);
            }
        }

        // Delete all tokens associated with the user first
        tokenRepository.deleteAllTokensByUserId(user.getId());
        log.debug("Deleted all tokens for user with ID: {}", user.getId());

        // Delete the supplier
        supplierRepository.delete(supplier);

        // Delete the associated user account
        userRepository.delete(user);

        log.info("""
        \n
        ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
          SUPPLIER DELETED                                \s
                                                          \s
          ID: {}                                          \s
          Name: {} {}                                     \s
          Email: {}                                       \s
          Phone: {}                                       \s
          Products removed: {}                            \s
        ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛""",
            supplierId, user.getFirstName(), user.getLastName(),
            supplierEmail, supplierPhone, supplierProducts.size());
    }

    /**
     * Deletes a supplier's own account from the system.
     *
     * @param connectedSupplier The authenticated supplier requesting deletion
     * @throws ResourceNotFoundException if the supplier profile is not found
     * @throws AccessDeniedException if the user is not a supplier
     */
    @Transactional
    public void deleteOwnAccount(Principal connectedSupplier) {
        // Get the authenticated supplier
        User user = userRepository.findByEmail(connectedSupplier.getName())
            .orElseThrow(() -> {
                log.error("Failed to find user account for: {}", connectedSupplier.getName());
                return new ResourceNotFoundException("User account not found");
            });

        if (user.getRole() != Role.SUPPLIER) {
            log.error("Non-supplier account {} attempted to use supplier deletion endpoint", user.getId());
            throw new AccessDeniedException("Only supplier accounts can use this endpoint");
        }

        Supplier supplier = supplierRepository.findByUserId(user.getId())
            .orElseThrow(() -> {
                log.error("User {} has SUPPLIER role but no supplier profile", user.getId());
                return new ResourceNotFoundException("Supplier profile not found");
            });

        // Call the main deletion method
        deleteSupplier(supplier.getId());
    }

    /**
     * Retrieves a specific supplier by ID.
     *
     * @param supplierId The ID of the supplier to retrieve
     * @return The supplier DTO
     * @throws ResourceNotFoundException if the supplier doesn't exist
     */
    public SupplierDTO getSupplierById(UUID supplierId) {
        Supplier supplier = supplierRepository.findById(supplierId)
            .orElseThrow(() -> {
                log.warn("Attempted to find non-existent supplier with ID: {}", supplierId);
                return new ResourceNotFoundException("Supplier not found with ID: " + supplierId);
            });

        return supplierMapper.toSupplierDTO(supplier);
    }
}
