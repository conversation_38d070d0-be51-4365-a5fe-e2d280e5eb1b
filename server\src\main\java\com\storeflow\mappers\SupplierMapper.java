// SupplierDTO.java
package com.storeflow.mappers;

import com.storeflow.DTOs.SupplierDTO;
import com.storeflow.models.Supplier;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SupplierMapper {
    @Mapping(source = "user.firstName", target = "firstName")
    @Mapping(source = "user.lastName", target = "lastName")
    @Mapping(source = "user.email", target = "email")
    @Mapping(source = "user.phoneNumber", target = "phoneNumber")
    @Mapping(source = "user.profilePictureUrl", target = "profilePictureUrl")
    @Mapping(source = "user.role", target = "role")
    @Mapping(source = "user.addedDate", target = "addedDate")
    SupplierDTO toSupplierDTO(Supplier supplier);

    // Batch mapping for collections
    List<SupplierDTO> toSupplierDTOs(List<Supplier> suppliers);
}
