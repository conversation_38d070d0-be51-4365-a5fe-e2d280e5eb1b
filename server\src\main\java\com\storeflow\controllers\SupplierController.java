package com.storeflow.controllers;

import com.storeflow.DTOs.SupplierDTO;
import com.storeflow.requests.SupplierAdditionRequest;
import com.storeflow.responses.ApiResponse;
import com.storeflow.services.SupplierService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.List;
import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("api/v1/suppliers")
@RequiredArgsConstructor
public class SupplierController {
    private final SupplierService supplierService;


    /**
     * @deprecated Use {@link #getAllSuppliersWithPagination} instead for pagination and search
     */
    @Deprecated
    @GetMapping("/all")
//    @PreAuthorize("hasAnyAuthority('admin:read', 'employee:read')")
    public ResponseEntity<ApiResponse<List<SupplierDTO>>> getAllSuppliers() {
        log.info("Request to get all suppliers (deprecated endpoint)");
        List<SupplierDTO> suppliers = supplierService.getAllSuppliers();
        String message = suppliers.isEmpty() ? "No supplier found." : "Suppliers retrieved successfully.";

        ApiResponse<List<SupplierDTO>> response = ApiResponse.<List<SupplierDTO>>builder()
            .success(true)
            .message(message)
            .data(suppliers)
            .build();

        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    @GetMapping
//    @PreAuthorize("hasAnyAuthority('admin:read', 'employee:read')")
    public ResponseEntity<ApiResponse<Page<SupplierDTO>>> getAllSuppliersWithPagination(
        @RequestParam(required = false, defaultValue = "") String query,
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(defaultValue = "addedDate") String sortBy,
        @RequestParam(defaultValue = "desc") String sortDir
    ) {
        log.info("Request to get all suppliers with query: '{}', page: {}, size: {}, sortBy: {}, sortDir: {}",
                query, page, size, sortBy, sortDir);

        // Create sort object - map frontend field names to entity field names
        String entitySortBy = mapSortField(sortBy);
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), entitySortBy);

        // Create pageable object with sorting
        Pageable pageable = PageRequest.of(page, size, sort);

        // Get suppliers with pagination and search
        Page<SupplierDTO> suppliers = supplierService.getAllSuppliers(query, pageable);

        String message = suppliers.isEmpty() ? "No suppliers found." : "Suppliers retrieved successfully.";

        ApiResponse<Page<SupplierDTO>> response = ApiResponse.<Page<SupplierDTO>>builder()
            .success(true)
            .message(message)
            .data(suppliers)
            .build();

        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    /**
     * Maps frontend sort field names to entity field names
     */
    private String mapSortField(String sortBy) {
        return switch (sortBy) {
            case "addedDate" -> "user.addedDate";
            case "firstName" -> "user.firstName";
            case "lastName" -> "user.lastName";
            case "email" -> "user.email";
            case "phoneNumber" -> "user.phoneNumber";
            case "localization" -> "localization";
            default -> "user.addedDate"; // Default to added date
        };
    }


    @PostMapping
//    @PreAuthorize("hasAnyAuthority('admin:create', 'employee:create')")
    public ResponseEntity<ApiResponse<UUID>> addSupplier(
        @Valid @RequestBody SupplierAdditionRequest supplier
    ) {
        log.info("Request to add new supplier: {} {}", supplier.firstName(), supplier.lastName());
        UUID supplierId = supplierService.addSupplier(supplier);

        ApiResponse<UUID> response = ApiResponse.<UUID>builder()
            .success(true)
            .message("Supplier created successfully.")
            .data(supplierId)
            .build();

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }


    @GetMapping("/{supplierId}")
//    @PreAuthorize("hasAnyAuthority('admin:read', 'employee:read')")
    public ResponseEntity<ApiResponse<SupplierDTO>> getSupplierById(
        @PathVariable UUID supplierId
    ) {
        log.info("Request to get supplier with ID: {}", supplierId);
        SupplierDTO supplier = supplierService.getSupplierById(supplierId);

        ApiResponse<SupplierDTO> response = ApiResponse.<SupplierDTO>builder()
            .success(true)
            .message("Supplier retrieved successfully.")
            .data(supplier)
            .build();

        return ResponseEntity.status(HttpStatus.OK).body(response);
    }


    @DeleteMapping("/{supplierId}")
//    @PreAuthorize("hasAuthority('admin:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteSupplier(
        @PathVariable UUID supplierId
    ) {
        log.info("Admin request to delete supplier with ID: {}", supplierId);
        supplierService.deleteSupplier(supplierId);

        ApiResponse<Void> response = ApiResponse.<Void>builder()
            .success(true)
            .message("Supplier deleted successfully.")
            .data(null)
            .build();

        return ResponseEntity.status(HttpStatus.OK).body(response);
    }


    @DeleteMapping("/my-account")
//    @PreAuthorize("hasRole('SUPPLIER')")
    public ResponseEntity<ApiResponse<Void>> deleteMyAccount(Principal principal) {
        log.info("Supplier request to delete own account: {}", principal.getName());
        supplierService.deleteOwnAccount(principal);

        ApiResponse<Void> response = ApiResponse.<Void>builder()
            .success(true)
            .message("Your account has been deleted successfully.")
            .data(null)
            .build();

        return ResponseEntity.status(HttpStatus.OK).body(response);
    }
}
