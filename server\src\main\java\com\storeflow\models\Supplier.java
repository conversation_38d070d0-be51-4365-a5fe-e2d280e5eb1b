package com.storeflow.models;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;

import java.util.LinkedHashSet;
import java.util.Set;
import java.util.UUID;

@Entity
@Getter
@Setter
@Builder
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Table(
    name = "suppliers",
    indexes = {
        @Index(name = "supplier_primary_key", columnList = "user_id", unique = true)
    }
)
public class Supplier {
    @Id
    private UUID id;

    @Column(nullable = false)
    private String localization;

    @MapsId
    @OneToOne
    @JoinColumn(name = "user_id", foreignKey = @ForeignKey(name = "user_id"))
    private User user;

    @OneToMany(mappedBy = "supplier", cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("label ASC")
    @JsonIgnore
    private Set<SupplierProduct> supplierProducts = new LinkedHashSet<>();
}
