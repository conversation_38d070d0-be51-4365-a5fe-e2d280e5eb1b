import { useState, useEffect } from "react";
import { useAuth } from "@/context/auth-context";
import { useSelector, useDispatch } from "react-redux";
import {
  UserCircle,
  KeyRound,
  ImageIcon,
  ShieldCheck,
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  CircleUserRoundIcon,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { ProfileForm, ProfilePicture, PasswordForm } from "./profile-info";
import { Link } from "react-router-dom";
import { capitalize } from "@/utils";
import { setUserData } from "@/redux/user-slice";

export const Profile = () => {
  const { isAuthenticated, loading: authLoading, getUserProfile } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("info");
  const dispatch = useDispatch();

  // Get user data from Redux store
  const { userData: user, loading: reduxLoading } = useSelector(
    (state) => state.user
  );

  // Define a single loading state that combines auth and redux loading
  const loading = authLoading || reduxLoading;

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated()) {
      navigate("/auth/login");
    }
  }, [authLoading]); // Only depend on authLoading, not the function

  if (loading || !user) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-blue-gray-50/40">
        <div className="flex flex-col items-center gap-4">
          <div className="animate-spin rounded-full h-14 w-14 border-4 border-blue-gray-200 border-t-blue-gray-800"></div>
          <p className="text-blue-gray-600 font-medium">
            Loading your profile...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen mx-auto space-y-4">
      {/* Page title and role badge */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-blue-gray-800 flex items-center gap-2">
            <UserCircle className="h-6 w-6 text-blue-gray-700" />
            Account Settings
          </h2>
          <p className="text-blue-gray-600 mt-1">
            Manage your personal information, profile picture and security
            settings
          </p>
        </div>
        <div className="mt-4 sm:mt-0 bg-gradient-to-r from-blue-gray-50 to-blue-gray-100 px-4 py-2.5 rounded-lg flex items-center gap-2 shadow-sm border border-blue-gray-100/50">
          <ShieldCheck size={18} className="text-blue-gray-700" />
          <span className="text-blue-gray-700 font-medium capitalize">
            {capitalize(user?.role) || "User"} Account
          </span>
        </div>
      </div>

      {/* Profile header with user summary */}
      <div className="bg-white rounded-xl shadow-sm border border-blue-gray-100 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-shrink-0">
            <div className="relative w-24 h-24 sm:w-28 sm:h-28 rounded-full overflow-hidden border-4 border-blue-gray-100 shadow-sm mx-auto sm:mx-0">
              {user?.profilePictureUrl ? (
                <img
                  src={user.profilePictureUrl}
                  alt={user.firstName}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-blue-gray-100">
                  <User size={40} className="text-blue-gray-400" />
                </div>
              )}
            </div>
          </div>

          <div className="flex-1 text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl font-bold text-blue-gray-900">
              {user?.firstName} {user?.lastName}
            </h1>

            <div className="flex flex-col sm:flex-row items-center sm:items-start gap-1 sm:gap-6 mt-2">
              <div className="flex items-center gap-1.5 text-blue-gray-600">
                <Mail size={16} className="text-blue-gray-400" />
                <span className="text-sm">{user?.email}</span>
              </div>

              {user?.phoneNumber && (
                <div className="flex items-center gap-1.5 text-blue-gray-600">
                  <Phone size={16} className="text-blue-gray-400" />
                  <span className="text-sm">{user?.phoneNumber}</span>
                </div>
              )}
            </div>

            <div className="flex items-center justify-center sm:justify-start mt-3 gap-2 flex-wrap">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-gray-100 text-blue-gray-800 capitalize">
                {capitalize(user?.role) || "User"}
              </span>

              {user?.role === "supplier" && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-50 text-blue-600">
                  <MapPin size={14} className="mr-1" />
                  Business Account
                </span>
              )}

              {user?.addedDate && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-50 text-green-700">
                  <Calendar size={14} className="mr-1" />
                  Member since {new Date(user.addedDate).toLocaleDateString()}
                </span>
              )}
            </div>
          </div>

          {/* Action button section */}
          <div className="mt-4 sm:mt-0 flex items-start justify-end">
            <button
              onClick={() => setActiveTab("picture")}
              className="text-blue-600 text-sm hover:text-blue-800 font-medium flex items-center gap-1.5"
            >
              <ImageIcon size={15} />
              Change Photo
            </button>
          </div>
        </div>
      </div>

      {/* Tab navigation - Premium Style */}
      <div className="mx-auto">
        <div className="bg-blue-gray-50/60 rounded-full p-1.5 shadow-sm border border-blue-gray-100/80">
          <div className="text-lg grid grid-cols-3 gap-1">
            <button
              onClick={() => setActiveTab("info")}
              className={`flex items-center justify-center gap-2 py-2.5 px-4 rounded-full transition-all ${
                activeTab === "info"
                  ? "bg-white shadow-sm text-blue-gray-800"
                  : "text-blue-gray-600 hover:text-blue-gray-800"
              }`}
            >
              <CircleUserRoundIcon className="size-[22px]" />
              <span className="hidden md:inline font-medium">
                Personal Info
              </span>
            </button>

            <button
              onClick={() => setActiveTab("picture")}
              className={`flex items-center justify-center gap-2 py-2.5 px-4 rounded-full transition-all ${
                activeTab === "picture"
                  ? "bg-white shadow-sm text-blue-gray-800"
                  : "text-blue-gray-600 hover:text-blue-gray-800"
              }`}
            >
              <ImageIcon className="size-[22px]" />
              <span className="hidden md:inline font-medium">
                Profile Picture
              </span>
            </button>

            <button
              onClick={() => setActiveTab("password")}
              className={`flex items-center justify-center gap-2 py-2.5 px-4 rounded-full transition-all ${
                activeTab === "password"
                  ? "bg-white shadow-sm text-blue-gray-800"
                  : "text-blue-gray-600 hover:text-blue-gray-800"
              }`}
            >
              <KeyRound className="size-[22px]" />
              <span className="hidden md:inline font-medium">Password</span>
            </button>
          </div>
        </div>
      </div>

      {/* Tab content */}
      <div className="bg-white p-4 rounded-xl shadow-sm border border-blue-gray-100">
        {activeTab === "info" && (
          <div className="space-y-4">
            <div className="pb-4 border-b border-blue-gray-100">
              <h3 className="text-xl font-bold text-blue-gray-800">
                Personal Information
              </h3>
              <p className="text-sm text-blue-gray-600 mt-1">
                Update your personal details and contact information
              </p>
            </div>
            <ProfileForm user={user} />
          </div>
        )}

        {activeTab === "picture" && (
          <div className="space-y-4">
            <div className="pb-4 border-b border-blue-gray-100">
              <h3 className="text-xl font-bold text-blue-gray-800">
                Profile Picture
              </h3>
              <p className="text-sm lg:text-base text-blue-gray-700">
                Upload or update your profile picture to personalize your
                account
              </p>
            </div>
            <ProfilePicture user={user} />
          </div>
        )}

        {activeTab === "password" && (
          <div className="space-y-6">
            <div className="mb-3 pb-4 border-b border-blue-gray-100">
              <h3 className="text-xl font-bold text-blue-gray-800">
                Security Settings
              </h3>
              <p className="text-sm text-blue-gray-600 mt-1">
                Update your password to maintain account security
              </p>
            </div>
            <PasswordForm />
          </div>
        )}
      </div>

      {/* Footer note */}
      <div className="border border-blue-gray-100 bg-white rounded-xl text-center py-4">
        <p className="text-base lg:text-lg text-blue-gray-500">
          Need help with your account? Contact our support team at{" "}
          <Link
            to="mailto:<EMAIL>"
            className="text-blue-600 hover:underline"
          >
            <EMAIL>
          </Link>
        </p>
      </div>
    </div>
  );
};

export default Profile;
