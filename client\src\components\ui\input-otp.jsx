import * as React from "react";
import { OTPInput, OTPInputContext } from "input-otp";
import { MinusIcon } from "lucide-react";

import { cn } from "@/lib/utils";

function InputOTP({ className, containerClassName, ...props }) {
  return (
    <OTPInput
      data-slot="input-otp"
      containerClassName={cn(
        "flex items-center gap-2 has-disabled:opacity-50",
        containerClassName
      )}
      className={cn("disabled:cursor-not-allowed", className)}
      {...props}
    />
  );
}

function InputOTPGroup({ className, ...props }) {
  return (
    <div
      data-slot="input-otp-group"
      className={cn("flex items-center", className)}
      {...props}
    />
  );
}

function InputOTPSlot({ index, className, ...props }) {
  const inputOTPContext = React.useContext(OTPInputContext);
  const { char, hasFakeCaret, isActive } = inputOTPContext?.slots[index] ?? {};

  return (
    <div
      data-slot="input-otp-slot"
      data-active={isActive}
      className={cn(
        // Base styles - modern, clean design
        "relative flex h-12 w-12 items-center justify-center text-lg font-medium",
        // Border and background
        "border border-blue-gray-500 bg-white rounded-xl",
        // Transitions
        "transition-all duration-200 ease-in-out",
        // Hover state
        "hover:border-blue-500 hover:shadow-sm",
        // Active/Focus state
        "data-[active=true]:border-blue-600 data-[active=true]:ring-2 data-[active=true]:ring-blue-200 data-[active=true]:bg-blue-50/50",
        // Filled state (when has character)
        char ? "border-blue-600 bg-blue-50/30 text-blue-900" : "",
        // Error state
        "aria-invalid:border-red-500 aria-invalid:ring-red-200",
        // Dark mode support
        "dark:bg-gray-800 dark:border-gray-600 dark:text-white",
        "dark:data-[active=true]:border-blue-400 dark:data-[active=true]:ring-blue-400/30",
        className
      )}
      {...props}
    >
      {char}
      {hasFakeCaret && (
        <div className="pointer-events-none absolute inset-0 flex items-center justify-center">
          <div className="animate-caret-blink bg-blue-500 h-5 w-0.5 duration-1000" />
        </div>
      )}
    </div>
  );
}

function InputOTPSeparator({ ...props }) {
  return (
    <div
      data-slot="input-otp-separator"
      role="separator"
      className="flex items-center justify-center mx-2"
      {...props}
    >
      <div className="w-3 h-0.5 bg-gray-300 rounded-full"></div>
    </div>
  );
}

export { InputOTP, InputOTPGroup, InputOTPSlot, InputOTPSeparator };
