import { Navigate, Route, Routes } from "react-router-dom";
import { Home } from "./pages";
import { Auth } from "./pages/auth";
import { Admin, Client, Supplier, Employee } from "./pages/portals";
import { useAuth } from "./context/auth-context";
import { ProtectedRoute, ToasterProvider } from "./components/portals";

function App() {
  const { loading } = useAuth();

  // Show loading spinner during initial auth check
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <>
      <Routes>
        {/* Public routes */}
        <Route path="/" element={<Home />} />
        <Route path="/auth/*" element={<Auth />} />

        {/* Protected admin routes */}
        <Route element={<ProtectedRoute allowedRoles={["admin"]} />}>
          <Route path="/admin/*" element={<Admin />} />
        </Route>

        {/* Protected client routes */}
        <Route element={<ProtectedRoute allowedRoles={["client"]} />}>
          <Route path="/client/*" element={<Client />} />
        </Route>

        {/* Protected supplier routes */}
        <Route element={<ProtectedRoute allowedRoles={["supplier"]} />}>
          <Route path="/supplier/*" element={<Supplier />} />
        </Route>

        {/* Protected employee routes */}
        <Route element={<ProtectedRoute allowedRoles={["employee"]} />}>
          <Route path="/employee/*" element={<Employee />} />
        </Route>

        {/* Catch all - 404 page */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
      <ToasterProvider />
    </>
  );
}

export default App;
