package com.storeflow.repositories;

import com.storeflow.models.Supplier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface SupplierRepository extends JpaRepository<Supplier, UUID>, JpaSpecificationExecutor<Supplier> {
    Optional<Supplier> findByUserId(UUID userId);

    /**
     * Find suppliers with pagination and search by firstName, lastName, email, phoneNumber, or localization
     *
     * @param searchQuery The search query to match against firstName, lastName, email, phoneNumber, or localization
     * @param pageable The pagination information
     * @return A page of suppliers matching the search criteria
     */
    @Query("SELECT s FROM Supplier s JOIN s.user u WHERE " +
           "LOWER(u.firstName) LIKE LOWER(CONCAT('%', :searchQuery, '%')) OR " +
           "LOWER(u.lastName) LIKE LOWER(CONCAT('%', :searchQuery, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :searchQuery, '%')) OR " +
           "LOWER(u.phoneNumber) LIKE LOWER(CONCAT('%', :searchQuery, '%')) OR " +
           "LOWER(s.localization) LIKE LOWER(CONCAT('%', :searchQuery, '%'))")
    Page<Supplier> findBySearchQuery(@Param("searchQuery") String searchQuery, Pageable pageable);
}
