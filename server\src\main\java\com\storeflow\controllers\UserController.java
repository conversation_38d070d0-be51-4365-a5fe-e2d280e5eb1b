package com.storeflow.controllers;

import com.storeflow.responses.ApiResponse;
import com.storeflow.requests.ChangePasswordRequest;
import com.storeflow.requests.ProfileUpdateRequest;
import com.storeflow.enums.Role;
import com.storeflow.mappers.UserMapper;
import com.storeflow.services.UserService;
import com.storeflow.repositories.UserRepository;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.security.Principal;
import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("/api/v1/users")
@RequiredArgsConstructor
public class UserController {
    private final UserMapper userMapper;
    private final UserService userService;
    private final UserRepository userRepository;

    @GetMapping("/role")
//    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<Role>> getCurrentUserRole(Principal principal) {
        Role authenticatedUserRole = userService.getAuthenticatedUserRole(principal);

        ApiResponse<Role> response = ApiResponse.<Role>builder()
            .success(true)
            .message("Authenticated user role received successfully.")
            .data(authenticatedUserRole)
            .build();

        return ResponseEntity
            .status(HttpStatus.OK)
            .body(response);
    }

    @PatchMapping("/profile")
//    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<UUID>> updateProfile(
        @Valid @RequestBody ProfileUpdateRequest request,
        Principal principal) {
        UUID updatedUserId = userService.updatePersonalInfo(request, principal);

        ApiResponse<UUID> response = ApiResponse.<UUID>builder()
            .success(true)
            .message("Profile updated successfully")
            .data(updatedUserId)
            .build();

        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    @PatchMapping(value = "/profile-picture", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
//    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<String>> updateProfilePicture(
        @RequestParam("image") MultipartFile file,
        Principal principal) {

        String userProfilePictureUrl = userService.updateProfilePicture(file, principal);

        ApiResponse<String> response = ApiResponse.<String>builder()
            .success(true)
            .message("Profile picture updated successfully")
            .data(userProfilePictureUrl)
            .build();

        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    @DeleteMapping("/profile-picture")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<String>> resetProfilePicture(Principal principal) {

        String userProfilePictureUrl = userService.resetToDefaultProfilePicture(principal);

        ApiResponse<String> response = ApiResponse.<String>builder()
            .success(true)
            .message("Profile picture has been reset to default")
            .data(userProfilePictureUrl)
            .build();

        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    @PostMapping("/change-password")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponse<Void>> changePassword(
        @Valid @RequestBody ChangePasswordRequest request,
        Principal principal) {

        userService.changePassword(request, principal);

        ApiResponse<Void> response = ApiResponse.<Void>builder()
            .success(true)
            .message("Password changed successfully")
            .build();

        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    @GetMapping("/count/clients")
//    @PreAuthorize("hasAnyAuthority('admin:read', 'employee:read')")
    public ResponseEntity<ApiResponse<Long>> getClientCount() {
        log.info("Request to get client count");
        // Direct repository access as a temporary workaround
        long clientCount = userRepository.countByRole(Role.CLIENT);

        ApiResponse<Long> response = ApiResponse.<Long>builder()
            .success(true)
            .message("Client count retrieved successfully")
            .data(clientCount)
            .build();

        return ResponseEntity.status(HttpStatus.OK).body(response);
    }
}
