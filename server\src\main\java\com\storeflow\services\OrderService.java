package com.storeflow.services;

import com.storeflow.DTOs.CatalogProductDTO;
import com.storeflow.DTOs.OrderDTO;
import com.storeflow.DTOs.OrderItemDTO;
import com.storeflow.DTOs.requests.CreateOrderItemRequest;
import com.storeflow.DTOs.requests.CreateOrderRequest;
import com.storeflow.DTOs.requests.UpdateOrderStatusRequest;
import com.storeflow.enums.OrderStatus;
import com.storeflow.enums.PurchaseStatus;
import com.storeflow.exception.ResourceNotFoundException;
import com.storeflow.models.Client;
import com.storeflow.models.Order;
import com.storeflow.models.OrderItem;
import com.storeflow.models.PurchaseItem;
import com.storeflow.models.User;
import com.storeflow.repositories.ClientRepository;
import com.storeflow.repositories.OrderRepository;
import com.storeflow.repositories.PurchaseItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service for managing client orders.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderService {

    private final OrderRepository orderRepository;
    private final ClientRepository clientRepository;
    private final CatalogService catalogService;
    private final PurchaseItemRepository purchaseItemRepository;

    /**
     * Create a new order for a client.
     *
     * @param request The order creation request
     * @return The created order DTO
     */
    @Transactional
    public OrderDTO createOrder(CreateOrderRequest request) {
        log.info("Creating new order with {} items", request.items().size());

        // Get the current authenticated user
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = (User) authentication.getPrincipal();

        // Find the client
        Client client = clientRepository.findByUserId(currentUser.getId())
            .orElseThrow(() -> new ResourceNotFoundException("Client not found"));

        log.info("Creating order for client: {} {}",
            client.getUser().getFirstName(), client.getUser().getLastName());

        // Create the order
        Order order = Order.builder()
            .client(client)
            .orderDate(LocalDateTime.now())
            .status(OrderStatus.PENDING)
            .notes(request.notes())
            .phoneNumber(request.phoneNumber())
            .totalAmount(BigDecimal.ZERO) // Will be calculated later
            .build();

        // Create order items
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (CreateOrderItemRequest itemRequest : request.items()) {
            // Get product information from catalog
            CatalogProductDTO catalogProduct = catalogService.getProductByLabel(itemRequest.productLabel());
            if (catalogProduct == null) {
                throw new ResourceNotFoundException("Product not found: " + itemRequest.productLabel());
            }

            // Check if enough quantity is available
            if (catalogProduct.totalAvailableQuantity() < itemRequest.quantity()) {
                throw new IllegalArgumentException(
                    String.format("Insufficient quantity for product %s. Available: %d, Requested: %d",
                        itemRequest.productLabel(), catalogProduct.totalAvailableQuantity(), itemRequest.quantity())
                );
            }

            OrderItem orderItem = OrderItem.builder()
                .productLabel(catalogProduct.label())
                .productDescription(catalogProduct.description())
                .productImageUrl(catalogProduct.imageUrl())
                .productBrand(catalogProduct.brand())
                .quantity(itemRequest.quantity())
                .unitPrice(catalogProduct.averagePrice())
                .build();

            order.addItem(orderItem);
            totalAmount = totalAmount.add(orderItem.getTotalPrice());
        }

        order.setTotalAmount(totalAmount);

        // Save the order first
        Order savedOrder = orderRepository.save(order);

        // Deduct stock from purchase items for each order item
        for (CreateOrderItemRequest itemRequest : request.items()) {
            boolean stockRemoved = removeStockFromPurchaseItems(
                itemRequest.productLabel(), itemRequest.quantity());

            if (!stockRemoved) {
                // If stock removal fails, we should rollback the order
                log.error("Failed to remove stock for product: {} in order: {}",
                    itemRequest.productLabel(), savedOrder.getId());
                throw new IllegalStateException("Failed to update inventory for product: " + itemRequest.productLabel());
            }
        }

        log.info("Order created successfully with ID: {} and total amount: {}",
            savedOrder.getId(), savedOrder.getTotalAmount());

        return convertToDTO(savedOrder);
    }

    /**
     * Remove stock from purchase items when clients order.
     * This method finds purchase items with the given label and reduces their quantities.
     *
     * @param productLabel The product label
     * @param quantityToRemove The quantity to remove
     * @return true if successful, false if insufficient stock
     */
    @Transactional
    public boolean removeStockFromPurchaseItems(String productLabel, Integer quantityToRemove) {
        log.info("Removing {} units of '{}' from purchase items", quantityToRemove, productLabel);

        // Get all completed purchase items for this product label with available stock, ordered by creation date (FIFO)
        List<PurchaseItem> availablePurchaseItems = purchaseItemRepository
            .findByProductLabelAndPurchaseStatusOrderByCreatedDateAsc(productLabel, PurchaseStatus.COMPLETED)
            .stream()
            .filter(PurchaseItem::hasAvailableStock)
            .collect(Collectors.toList());

        if (availablePurchaseItems.isEmpty()) {
            log.error("No completed purchase items with available stock found for product label: '{}'", productLabel);
            return false;
        }

        // Calculate total available quantity
        int totalAvailable = availablePurchaseItems.stream()
            .mapToInt(PurchaseItem::getAvailableQuantity)
            .sum();

        if (totalAvailable < quantityToRemove) {
            log.error("Insufficient stock for '{}': requested {}, available {}",
                productLabel, quantityToRemove, totalAvailable);
            return false;
        }

        // Consume stock using FIFO (First In, First Out) approach
        int remainingToConsume = quantityToRemove;
        List<PurchaseItem> itemsToUpdate = new ArrayList<>();

        for (PurchaseItem purchaseItem : availablePurchaseItems) {
            if (remainingToConsume <= 0) break;

            int availableInThisItem = purchaseItem.getAvailableQuantity();
            int quantityToConsumeFromThisItem = Math.min(remainingToConsume, availableInThisItem);

            // Consume from this purchase item (preserves original quantity for history)
            boolean consumed = purchaseItem.consumeQuantity(quantityToConsumeFromThisItem);
            if (consumed) {
                itemsToUpdate.add(purchaseItem);
                remainingToConsume -= quantityToConsumeFromThisItem;

                log.info("Consumed {} units from purchase item {} (original: {}, consumed: {}, available: {})",
                    quantityToConsumeFromThisItem, purchaseItem.getId(),
                    purchaseItem.getQuantity(), purchaseItem.getConsumedQuantity(), purchaseItem.getAvailableQuantity());
            }
        }

        // Save all updated purchase items
        purchaseItemRepository.saveAll(itemsToUpdate);

        log.info("Successfully consumed {} units of '{}' from purchase items (history preserved)", quantityToRemove, productLabel);
        return true;
    }

    /**
     * Update the status of an order.
     *
     * @param orderId The ID of the order
     * @param request The update request
     * @return The updated order DTO
     */
    @Transactional
    public OrderDTO updateOrderStatus(UUID orderId, UpdateOrderStatusRequest request) {
        log.info("Updating order status for order ID: {} to {}", orderId, request.status());

        Order order = orderRepository.findById(orderId)
            .orElseThrow(() -> new ResourceNotFoundException("Order not found with ID: " + orderId));

        order.setStatus(request.status());
        if (request.notes() != null) {
            order.setNotes(request.notes());
        }

        Order savedOrder = orderRepository.save(order);

        log.info("Order status updated successfully");

        return convertToDTO(savedOrder);
    }

    /**
     * Get an order by ID.
     *
     * @param orderId The ID of the order
     * @return The order DTO
     */
    public OrderDTO getOrderById(UUID orderId) {
        log.info("Fetching order with ID: {}", orderId);

        Order order = orderRepository.findById(orderId)
            .orElseThrow(() -> new ResourceNotFoundException("Order not found with ID: " + orderId));

        return convertToDTO(order);
    }

    /**
     * Get all orders with pagination.
     *
     * @param pageable The pagination information
     * @return A page of order DTOs
     */
    public Page<OrderDTO> getAllOrders(Pageable pageable) {
        log.info("Fetching all orders with pagination");

        Page<Order> orders = orderRepository.findAll(pageable);

        return orders.map(this::convertToDTO);
    }

    /**
     * Get all orders for the current client with pagination.
     *
     * @param pageable The pagination information
     * @return A page of order DTOs
     */
    public Page<OrderDTO> getOrdersForCurrentClient(Pageable pageable) {
        log.info("Fetching orders for current client");

        // Get the current authenticated user
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = (User) authentication.getPrincipal();

        // Find the client
        Client client = clientRepository.findByUserId(currentUser.getId())
            .orElseThrow(() -> new ResourceNotFoundException("Client not found"));

        Page<Order> orders = orderRepository.findByClientId(client.getId(), pageable);

        return orders.map(this::convertToDTO);
    }

    /**
     * Convert Order entity to OrderDTO.
     *
     * @param order The order entity
     * @return The order DTO
     */
    private OrderDTO convertToDTO(Order order) {
        List<OrderItemDTO> itemDTOs = order.getItems() != null ?
            order.getItems().stream()
                .map(item -> new OrderItemDTO(
                    item.getId(),
                    item.getProductLabel(),
                    item.getProductDescription(),
                    item.getProductImageUrl(),
                    item.getProductBrand(),
                    item.getQuantity(),
                    item.getUnitPrice(),
                    item.getTotalPrice(),
                    item.getCreatedDate(),
                    item.getLastModifiedDate()
                ))
                .collect(Collectors.toList()) :
            new ArrayList<>();

        return new OrderDTO(
            order.getId(),
            order.getClient().getId(),
            order.getClient().getUser().getFirstName() + " " + order.getClient().getUser().getLastName(),
            order.getOrderDate(),
            order.getStatus(),
            order.getTotalAmount(),
            order.getNotes(),
            order.getPhoneNumber(),
            itemDTOs,
            order.getCreatedDate(),
            order.getLastModifiedDate(),
            order.getCreatedBy(),
            order.getLastModifiedBy()
        );
    }
}
