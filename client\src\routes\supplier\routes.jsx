import { Profile } from "@/components/portals";
import { Dashboard, Products } from "@/components/portals/supplier";
import { CircleUserRoundIcon, HouseIcon, PackageIcon } from "lucide-react";

const icon = {
  className: "size-[25px] text-inherit",
};

export const routes = [
  {
    icon: <HouseIcon {...icon} />,
    name: "dashboard",
    path: "/dashboard",
    element: <Dashboard />,
  },
  {
    icon: <CircleUserRoundIcon {...icon} />,
    name: "profile",
    path: "/profile",
    element: <Profile />,
  },
  {
    icon: <PackageIcon {...icon} />,
    name: "products",
    path: "/products",
    element: <Products />,
  },
];

export default routes;
