import { authApi } from "@/context/auth-context";
import axios from "axios";
import { directApi } from "./api-client";

/**
 * Service for purchase-related API calls
 */
export const purchaseService = {
  /**
   * Get all purchases with pagination and search
   * @param {Object} params - Query parameters
   * @param {string} params.query - Search query
   * @param {number} params.page - Page number (0-based)
   * @param {number} params.size - Page size
   * @param {string} params.sortBy - Field to sort by
   * @param {string} params.sortDir - Sort direction ('asc' or 'desc')
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  getAllPurchases: async (params = {}) => {
    try {
      const {
        query = "",
        page = 0,
        size = 10,
        sortBy = "purchaseDate",
        sortDir = "desc",
      } = params;

      const response = await authApi.get("/api/v1/purchases", {
        params: {
          query,
          page,
          size,
          sortBy,
          sortDir,
        },
      });

      return {
        success: response.data.success,
        message: response.data.message,
        data: response.data.data,
      };
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || "Failed to fetch purchases",
        data: { content: [], totalPages: 0, totalElements: 0 },
      };
    }
  },

  /**
   * Get purchases for the current user with pagination (employees see only their purchases)
   * @param {Object} params - Query parameters
   * @param {string} params.query - Search query
   * @param {number} params.page - Page number (0-based)
   * @param {number} params.size - Page size
   * @param {string} params.sortBy - Field to sort by
   * @param {string} params.sortDir - Sort direction ('asc' or 'desc')
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  getMyPurchases: async (params = {}) => {
    try {
      const {
        query = "",
        page = 0,
        size = 10,
        sortBy = "purchaseDate",
        sortDir = "desc",
      } = params;

      const response = await authApi.get("/api/v1/purchases/my-purchases", {
        params: {
          query,
          page,
          size,
          sortBy,
          sortDir,
        },
      });

      return {
        success: response.data.success,
        message: response.data.message,
        data: response.data.data,
      };
    } catch (error) {
      return {
        success: false,
        message:
          error.response?.data?.message || "Failed to fetch your purchases",
        data: { content: [], totalPages: 0, totalElements: 0 },
      };
    }
  },

  /**
   * Get purchases for a specific supplier with pagination
   * @param {string} supplierId - Supplier ID
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number (0-based)
   * @param {number} params.size - Page size
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  getPurchasesBySupplier: async (supplierId, params = {}) => {
    try {
      const { page = 0, size = 10 } = params;

      const response = await authApi.get(
        `/api/v1/purchases/supplier/${supplierId}`,
        {
          params: {
            page,
            size,
          },
        }
      );

      return {
        success: response.data.success,
        message: response.data.message,
        data: response.data.data,
      };
    } catch (error) {
      return {
        success: false,
        message:
          error.response?.data?.message || "Failed to fetch supplier purchases",
        data: { content: [], totalPages: 0, totalElements: 0 },
      };
    }
  },

  /**
   * Get a purchase by ID
   * @param {string} purchaseId - Purchase ID
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  getPurchaseById: async (purchaseId) => {
    try {
      const response = await authApi.get(`/api/v1/purchases/${purchaseId}`);

      return {
        success: response.data.success,
        message: response.data.message,
        data: response.data.data,
      };
    } catch (error) {
      return {
        success: false,
        message:
          error.response?.data?.message || "Failed to fetch purchase details",
        data: null,
      };
    }
  },

  /**
   * Create a new purchase
   * @param {Object} purchaseData - Purchase data
   * @param {string} purchaseData.supplierId - Supplier ID
   * @param {string} purchaseData.notes - Optional notes
   * @param {Array} purchaseData.items - Array of purchase items
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  createPurchase: async (purchaseData) => {
    try {
      // Validate the purchase data before sending
      if (!purchaseData.supplierId) {
        return {
          success: false,
          message: "Missing supplierId in purchase data",
          data: null,
        };
      }

      if (
        !purchaseData.items ||
        !Array.isArray(purchaseData.items) ||
        purchaseData.items.length === 0
      ) {
        return {
          success: false,
          message: "Missing or invalid items in purchase data",
          data: null,
        };
      }

      // Validate each item
      for (const item of purchaseData.items) {
        if (!item.productId) {
          return {
            success: false,
            message: "Missing productId in one or more items",
            data: null,
          };
        }
        if (!item.quantity || item.quantity < 1) {
          return {
            success: false,
            message: "Invalid quantity in one or more items",
            data: null,
          };
        }
      }

      // Format the request data to match the backend's expected format
      const formattedPurchaseData = {
        supplierId: purchaseData.supplierId, // This should be a UUID string
        notes: purchaseData.notes || "",
        items: purchaseData.items.map((item) => ({
          productId: item.productId, // This should be a UUID string
          quantity: item.quantity,
        })),
      };

      // Make a direct API call using authApi
      const response = await authApi.post(
        "/api/v1/purchases",
        formattedPurchaseData
      );

      return {
        success: response.data.success,
        message: response.data.message,
        data: response.data.data,
      };
    } catch (error) {
      return {
        success: false,
        message:
          error.response?.data?.message ||
          error.message ||
          "Failed to create purchase",
        data: null,
      };
    }
  },

  /**
   * Update the status of a purchase
   * @param {string} purchaseId - Purchase ID
   * @param {Object} updateData - Update data
   * @param {string} updateData.status - New status (PENDING, CONFIRMED, COMPLETED, CANCELLED)
   * @param {string} updateData.notes - Optional notes
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  updatePurchaseStatus: async (purchaseId, updateData) => {
    try {
      const response = await authApi.patch(
        `/api/v1/purchases/${purchaseId}/status`,
        updateData
      );

      return {
        success: response.data.success,
        message: response.data.message,
        data: response.data.data,
      };
    } catch (error) {
      return {
        success: false,
        message:
          error.response?.data?.message || "Failed to update purchase status",
        data: null,
      };
    }
  },

  /**
   * Get monthly analytics data for purchases
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  getMonthlyAnalytics: async () => {
    try {
      const response = await authApi.get("/api/v1/purchases/analytics/monthly");

      return {
        success: response.data.success,
        message: response.data.message,
        data: response.data.data,
      };
    } catch (error) {
      return {
        success: false,
        message:
          error.response?.data?.message || "Failed to fetch purchase analytics",
        data: null,
      };
    }
  },
};

export default purchaseService;
