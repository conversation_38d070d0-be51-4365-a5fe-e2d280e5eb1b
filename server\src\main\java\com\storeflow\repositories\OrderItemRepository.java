package com.storeflow.repositories;

import com.storeflow.models.OrderItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Repository for managing OrderItem entities.
 */
@Repository
public interface OrderItemRepository extends JpaRepository<OrderItem, UUID> {
    
    /**
     * Find all order items for a specific order.
     * 
     * @param orderId The ID of the order
     * @return A list of order items for the order
     */
    List<OrderItem> findByOrderId(UUID orderId);
    
    /**
     * Find all order items for a specific product label.
     * 
     * @param productLabel The label of the product
     * @return A list of order items for the product
     */
    List<OrderItem> findByProductLabel(String productLabel);
    
    /**
     * Get monthly sales statistics for a product label.
     * 
     * @param productLabel The label of the product
     * @param startDate The start date
     * @param endDate The end date
     * @return A list of objects containing month, year, total quantity, and total amount
     */
    @Query("""
        SELECT 
            MONTH(o.orderDate) as month, 
            YEAR(o.orderDate) as year, 
            SUM(oi.quantity) as totalQuantity, 
            SUM(oi.quantity * oi.unitPrice) as totalAmount 
        FROM OrderItem oi 
        JOIN oi.order o 
        WHERE oi.productLabel = :productLabel 
            AND o.status = 'COMPLETED' 
            AND o.orderDate BETWEEN :startDate AND :endDate 
        GROUP BY YEAR(o.orderDate), MONTH(o.orderDate) 
        ORDER BY YEAR(o.orderDate), MONTH(o.orderDate)
    """)
    List<Object[]> getMonthlyProductSalesStatistics(
        @Param("productLabel") String productLabel,
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );
}
