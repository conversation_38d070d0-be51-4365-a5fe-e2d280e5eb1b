import { usePasswordVisibility } from "@/hooks";
import { Link, useNavigate } from "react-router-dom";
import { isDesktop, ripple } from "@/utils";
import {
  AtSignIcon,
  CircleAlertIcon,
  EyeIcon,
  EyeOffIcon,
  KeyRoundIcon,
  UserRoundIcon,
} from "lucide-react";
import PhoneInput from "react-phone-number-input";
import "react-phone-number-input/style.css";
import { allowedCountries } from "@/utils/phone-utils";
import { useFormik } from "formik";
import * as Yup from "yup";
// Import and apply the yup phone extension properly
import "yup-phone";
import { useAuth } from "@/context/auth-context";
import { toast } from "sonner";

export const Signup = () => {
  const navigate = useNavigate();
  const { register, loading, error, initiateGoogleOAuth } = useAuth();

  const {
    isVisible: isPwdVisible,
    toggleVisibility: togglePwdVisibility,
    inputRef: pwdInputRef,
  } = usePasswordVisibility();
  const {
    isVisible: isConfPwdVisible,
    toggleVisibility: toggleConfPwdVisibility,
    inputRef: confPwdInputRef,
  } = usePasswordVisibility();

  // Define validation schema using Yup
  const SignupSchema = Yup.object().shape({
    firstName: Yup.string()
      .test("two-words", "Must contain valid names", (value) => {
        if (!value) return false;
        const trimmedValue = value.trim();
        const words = trimmedValue.split(" ");
        return words.every((word) => word.length >= 2); // Each word must have at least 2 letters
      })
      .required("First name is required"),
    lastName: Yup.string()
      .test("two-words", "Must contain valid names", (value) => {
        if (!value) return false;
        const trimmedValue = value.trim();
        const words = trimmedValue.split(" ");
        return words.every((word) => word.length >= 2); // Each word must have at least 2 letters
      })
      .required("Last name is required"),
    email: Yup.string()
      .email("Invalid email format")
      .required("Email is required"),
    // International phone validation
    phoneNumber: Yup.string()
      .test("phone", "Invalid phone number", function (value) {
        if (!value) return false;

        // Basic international phone number validation
        // This regex validates most international phone numbers
        // It allows for different country codes and formats
        const internationalPattern = /^\+[1-9]\d{1,14}$/;

        // Special case for Moroccan numbers (including local format)
        const moroccanPattern = /^(\+212|0)[5-7][0-9]{8}$/;

        return internationalPattern.test(value) || moroccanPattern.test(value);
      })
      .required("Phone number is required"),
    password: Yup.string()
      .min(8, "Password must be at least 8 characters")
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
        "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"
      )
      .required("Password is required"),
    confirmationPassword: Yup.string()
      .oneOf([Yup.ref("password"), null], "Passwords must match")
      .required("Confirm password is required"),
  });

  // Initialize formik
  const formik = useFormik({
    initialValues: {
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      password: "",
      confirmationPassword: "",
    },
    validationSchema: SignupSchema,
    onSubmit: async (values) => {
      try {
        const registrationResult = await register(values);

        if (registrationResult) {
          toast.success(
            "Registration successful! Please login with your credentials."
          );
          navigate("/auth/login");
        } else {
          // Error is handled by the AuthContext and stored in error state
          toast.error(error || "Registration failed. Please try again.");
        }
      } catch (err) {
        console.error("Registration error:", err);
        toast.error("An unexpected error occurred. Please try again.");
      }
    },
  });

  return (
    <section className="min-h-screen flex flex-col justify-center items-center px-8 py-20 lg:px-[360px] bg-blue-gray-50/40">
      <div className="flex items-center justify-center absolute top-0 right-0 px-6 py-3 select-none">
        <Link to="/">
          <div className="flex items-center justify-center gap-x-1.5 ">
            <img src="/icon.png" className="size-7" alt="Logo" />
            <p className="text-blue-gray-700 font-satoshi text-xl">
              <span className="text-crimson">S</span>tore
              <span className="text-crimson">F</span>low
            </p>
          </div>
        </Link>
      </div>

      <div className="lg:bg-white lg:rounded-3xl w-full lg:p-10 lg:border lg:border-blue-gray-50 lg:shadow-dark text-blue-gray-700">
        <p className="font-satoshi font-bold text-header text-blue-gray-800 text-center w-full py-0">
          Join us today
        </p>
        <p className="font-normal text-subheader text-blue-gray-800 text-center w-full mb-7 py-0">
          We're waiting for you
        </p>
        <form
          className="w-full flex flex-col items-center gap-y-7 mb-7"
          onSubmit={formik.handleSubmit}
        >
          {/* Show API/registration errors */}
          {error && (
            <div className="w-full mb-4 p-3 bg-red-50 text-red-600 rounded-lg flex items-center justify-center">
              <p className="text-base">{error}</p>
            </div>
          )}
          {/* First Name Field */}
          <div className="flex flex-col gap-y-1 w-full">
            <div
              className={`flex items-center w-full border ${
                formik.touched.firstName && formik.errors.firstName
                  ? "border-red-500"
                  : "border-blue-gray-500"
              } rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px]`}
            >
              <UserRoundIcon
                className={`ml-3 size-5 lg:size-6 ${
                  formik.values.firstName ? "" : "text-blue-gray-500"
                } ${
                  formik.touched.firstName && formik.errors.firstName
                    ? "text-red-500"
                    : ""
                }`}
                onMouseDown={(event) => event.preventDefault()}
              />
              <input
                type="text"
                id="firstName"
                name="firstName"
                className="flex-1 h-full text-base focus:outline-0 transition-all placeholder:text-blue-gray-500 rounded-xl pr-3 pl-2 lg:text-lg"
                placeholder="First name"
                value={formik.values.firstName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {formik.touched.firstName && formik.errors.firstName && (
                <CircleAlertIcon className="mr-3 size-5 text-red-500" />
              )}
            </div>
            {formik.touched.firstName && formik.errors.firstName && (
              <p className="text-red-500 text-sm pl-2">
                {formik.errors.firstName}
              </p>
            )}
          </div>

          {/* Last Name Field */}
          <div className="flex flex-col gap-y-1 w-full">
            <div
              className={`flex items-center w-full border ${
                formik.touched.lastName && formik.errors.lastName
                  ? "border-red-500"
                  : "border-blue-gray-500"
              } rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px]`}
            >
              <UserRoundIcon
                className={`ml-3 size-5 lg:size-6 ${
                  formik.values.lastName ? "" : "text-blue-gray-500"
                } ${
                  formik.touched.lastName && formik.errors.lastName
                    ? "text-red-500"
                    : ""
                }`}
                onMouseDown={(event) => event.preventDefault()}
              />
              <input
                type="text"
                id="lastName"
                name="lastName"
                className="flex-1 h-full text-base focus:outline-0 transition-all placeholder:text-blue-gray-500 rounded-xl pr-3 pl-2 lg:text-lg"
                placeholder="Last name"
                value={formik.values.lastName}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {formik.touched.lastName && formik.errors.lastName && (
                <CircleAlertIcon className="mr-3 size-5 text-red-500" />
              )}
            </div>
            {formik.touched.lastName && formik.errors.lastName && (
              <p className="text-red-500 text-sm pl-2">
                {formik.errors.lastName}
              </p>
            )}
          </div>

          {/* Email Field */}
          <div className="flex flex-col gap-y-1 w-full">
            <div
              className={`flex items-center w-full border ${
                formik.touched.email && formik.errors.email
                  ? "border-red-500"
                  : "border-blue-gray-500"
              } rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px]`}
            >
              <AtSignIcon
                className={`ml-3 size-5 lg:size-6 ${
                  formik.values.email ? "" : "text-blue-gray-500"
                } ${
                  formik.touched.email && formik.errors.email
                    ? "text-red-500"
                    : ""
                }`}
                onMouseDown={(event) => event.preventDefault()}
              />
              <input
                type="email"
                id="email"
                name="email"
                className="flex-1 h-full text-base focus:outline-0 transition-all placeholder:text-blue-gray-500 rounded-xl pr-3 pl-2 lg:text-lg"
                placeholder="Email address"
                value={formik.values.email}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {formik.touched.email && formik.errors.email && (
                <CircleAlertIcon className="mr-3 size-5 text-red-500" />
              )}
            </div>
            {formik.touched.email && formik.errors.email && (
              <p className="text-red-500 text-sm pl-2">{formik.errors.email}</p>
            )}
          </div>

          {/* Phone Number Field using react-phone-number-input */}
          <div className="flex flex-col gap-y-1 w-full">
            <div
              className={`phone-input-container w-full border ${
                formik.touched.phoneNumber && formik.errors.phoneNumber
                  ? "border-red-500"
                  : "border-blue-gray-500"
              } rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px] overflow-hidden`}
            >
              <PhoneInput
                international
                defaultCountry="MA"
                className="custom-phone-input"
                countries={allowedCountries}
                value={formik.values.phoneNumber}
                onChange={(value) => formik.setFieldValue("phoneNumber", value)}
                onBlur={() => formik.setFieldTouched("phoneNumber", true)}
              />
            </div>
            {formik.touched.phoneNumber && formik.errors.phoneNumber && (
              <p className="text-red-500 text-sm pl-2">
                {formik.errors.phoneNumber}
              </p>
            )}
          </div>

          {/* Password Field */}
          <div className="flex flex-col gap-y-1 w-full">
            <div
              className={`flex items-center w-full border ${
                formik.touched.password && formik.errors.password
                  ? "border-red-500"
                  : "border-blue-gray-500"
              } rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px]`}
            >
              <KeyRoundIcon
                className={`ml-3 size-5 lg:size-6 ${
                  formik.values.password
                    ? "text-blue-gray-800"
                    : "text-blue-gray-500"
                } ${
                  formik.touched.password && formik.errors.password
                    ? "text-red-500"
                    : ""
                }`}
              />
              <input
                ref={pwdInputRef}
                type={isPwdVisible ? "text" : "password"}
                id="password"
                name="password"
                className="flex-1 h-full text-base focus:outline-0 transition-all placeholder:text-blue-gray-500 rounded-xl px-2 lg:text-lg"
                placeholder="Password"
                value={formik.values.password}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {isPwdVisible ? (
                <EyeIcon
                  onClick={togglePwdVisibility}
                  className={`mr-3 size-5 cursor-pointer lg:size-6 ${
                    formik.values.password
                      ? "text-blue-gray-800"
                      : "text-blue-gray-500"
                  }`}
                  onMouseDown={(event) => event.preventDefault()}
                />
              ) : (
                <EyeOffIcon
                  onClick={togglePwdVisibility}
                  className={`mr-3 size-5 cursor-pointer lg:size-6 ${
                    formik.values.password
                      ? "text-blue-gray-800"
                      : "text-blue-gray-500"
                  }`}
                  onMouseDown={(event) => event.preventDefault()}
                />
              )}
            </div>
            {formik.touched.password && formik.errors.password && (
              <p className="text-red-500 text-sm pl-2">
                {formik.errors.password}
              </p>
            )}
          </div>

          {/* Confirm Password Field */}
          <div className="flex flex-col gap-y-1 w-full">
            <div
              className={`flex items-center w-full border ${
                formik.touched.confirmationPassword &&
                formik.errors.confirmationPassword
                  ? "border-red-500"
                  : "border-blue-gray-500"
              } rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px]`}
            >
              <KeyRoundIcon
                className={`ml-3 size-5 lg:size-6 ${
                  formik.values.confirmationPassword
                    ? "text-blue-gray-800"
                    : "text-blue-gray-500"
                } ${
                  formik.touched.confirmationPassword &&
                  formik.errors.confirmationPassword
                    ? "text-red-500"
                    : ""
                }`}
              />
              <input
                ref={confPwdInputRef}
                type={isConfPwdVisible ? "text" : "password"}
                id="confirmationPassword"
                name="confirmationPassword"
                className="flex-1 h-full text-base focus:outline-0 transition-all placeholder:text-blue-gray-500 rounded-xl px-2 lg:text-lg"
                placeholder="Confirm password"
                value={formik.values.confirmationPassword}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {isConfPwdVisible ? (
                <EyeIcon
                  onClick={toggleConfPwdVisibility}
                  className={`mr-3 size-5 cursor-pointer lg:size-6 ${
                    formik.values.confirmationPassword
                      ? "text-blue-gray-800"
                      : "text-blue-gray-500"
                  }`}
                  onMouseDown={(event) => event.preventDefault()}
                />
              ) : (
                <EyeOffIcon
                  onClick={toggleConfPwdVisibility}
                  className={`mr-3 size-5 cursor-pointer lg:size-6 ${
                    formik.values.confirmationPassword
                      ? "text-blue-gray-800"
                      : "text-blue-gray-500"
                  }`}
                  onMouseDown={(event) => event.preventDefault()}
                />
              )}
            </div>
            {formik.touched.confirmationPassword &&
              formik.errors.confirmationPassword && (
                <p className="text-red-500 text-sm pl-2">
                  {formik.errors.confirmationPassword}
                </p>
              )}
          </div>

          {/* Submit Button */}
          <button
            onMouseDown={(event) =>
              ripple.create(event, isDesktop() ? "light" : "dark")
            }
            type="submit"
            disabled={loading}
            className={`group cursor-pointer text-base text-white bg-blue-gray-900 border border-blue-gray-800 shadow-dark flex items-center justify-center px-4 w-full rounded-full py-3 font-medium transition-all duration-300 hover:bg-transparent hover:text-blue-gray-800 hover:border-blue-gray-50 hover:shadow-darker active:shadow-darker lg:rounded-xl lg:text-lg ${
              loading ? "opacity-70 cursor-not-allowed" : ""
            }`}
          >
            <span className="text-center">
              {loading ? "Creating account..." : "Sign up"}
            </span>
          </button>

          {/* OR Separator */}
          <div className="flex items-center justify-center w-full">
            <hr className="border-t w-full border-t-blue-gray-800" />
            <p className="text-base px-3 text-blue-gray-800 font-normal">OR</p>
            <hr className="border-t w-full border-t-blue-gray-800" />
          </div>

          {/* Continue with Google Button */}
          <button
            onMouseDown={(event) => ripple.create(event, "dark")}
            onClick={initiateGoogleOAuth}
            className="relative group cursor-pointer text-base text-blue-gray-700 bg-white border border-blue-gray-800 shadow-dark flex items-center justify-center w-full gap-x-2 rounded-full py-3 font-medium transition-all duration-300 hover:text-blue-gray-800 hover:border-blue-gray-50 hover:shadow-darker active:shadow-darker lg:rounded-xl lg:text-lg"
            type="button"
          >
            <svg
              className="absolute left-4 size-5 lg:size-6"
              viewBox="0 0 17 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g clipPath="url(#clip0_1156_824)">
                <path
                  d="M16.3442 8.18429C16.3442 7.64047 16.3001 7.09371 16.206 6.55872H8.66016V9.63937H12.9813C12.802 10.6329 12.2258 11.5119 11.3822 12.0704V14.0693H13.9602C15.4741 12.6759 16.3442 10.6182 16.3442 8.18429Z"
                  fill="#4285F4"
                />
                <path
                  d="M8.65974 16.0006C10.8174 16.0006 12.637 15.2922 13.9627 14.0693L11.3847 12.0704C10.6675 12.5584 9.7415 12.8347 8.66268 12.8347C6.5756 12.8347 4.80598 11.4266 4.17104 9.53357H1.51074V11.5942C2.86882 14.2956 5.63494 16.0006 8.65974 16.0006Z"
                  fill="#34A853"
                />
                <path
                  d="M4.16852 9.53356C3.83341 8.53999 3.83341 7.46411 4.16852 6.47054V4.40991H1.51116C0.376489 6.67043 0.376489 9.33367 1.51116 11.5942L4.16852 9.53356Z"
                  fill="#FBBC04"
                />
                <path
                  d="M8.65974 3.16644C9.80029 3.1488 10.9026 3.57798 11.7286 4.36578L14.0127 2.08174C12.5664 0.72367 10.6469 -0.0229773 8.65974 0.000539111C5.63494 0.000539111 2.86882 1.70548 1.51074 4.40987L4.1681 6.4705C4.8001 4.57449 6.57266 3.16644 8.65974 3.16644Z"
                  fill="#EA4335"
                />
              </g>
              <defs>
                <clipPath id="clip0_1156_824">
                  <rect
                    width="16"
                    height="16"
                    fill="white"
                    transform="translate(0.5)"
                  />
                </clipPath>
              </defs>
            </svg>
            <span className="w-full text-center">Continue with Google</span>
          </button>
        </form>
        <p className="text-center text-base lg:text-lg">
          Already have an account?{" "}
          <Link to="/auth/login" className="underline">
            Log in
          </Link>
        </p>
      </div>
    </section>
  );
};

export default Signup;
