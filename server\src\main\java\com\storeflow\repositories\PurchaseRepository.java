package com.storeflow.repositories;

import com.storeflow.enums.PurchaseStatus;
import com.storeflow.models.Purchase;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Repository for managing Purchase entities.
 */
@Repository
public interface PurchaseRepository extends JpaRepository<Purchase, UUID> {

    /**
     * Find all purchases for a specific supplier.
     *
     * @param supplierId The ID of the supplier
     * @return A list of purchases for the supplier
     */
    List<Purchase> findBySupplierId(UUID supplierId);

    /**
     * Find all purchases for a specific supplier with pagination.
     *
     * @param supplierId The ID of the supplier
     * @param pageable Pagination information
     * @return A page of purchases for the supplier
     */
    Page<Purchase> findBySupplierId(UUID supplierId, Pageable pageable);

    /**
     * Find all purchases with a specific status.
     *
     * @param status The purchase status
     * @return A list of purchases with the specified status
     */
    List<Purchase> findByStatus(PurchaseStatus status);

    /**
     * Find all purchases with a specific status and supplier.
     *
     * @param status The purchase status
     * @param supplierId The ID of the supplier
     * @return A list of purchases with the specified status and supplier
     */
    List<Purchase> findByStatusAndSupplierId(PurchaseStatus status, UUID supplierId);

    /**
     * Find all purchases created between two dates.
     *
     * @param startDate The start date
     * @param endDate The end date
     * @return A list of purchases created between the specified dates
     */
    List<Purchase> findByCreatedDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find all purchases for a specific supplier created between two dates.
     *
     * @param supplierId The ID of the supplier
     * @param startDate The start date
     * @param endDate The end date
     * @return A list of purchases for the supplier created between the specified dates
     */
    List<Purchase> findBySupplierIdAndCreatedDateBetween(
        UUID supplierId,
        LocalDateTime startDate,
        LocalDateTime endDate
    );

    /**
     * Find all purchases created by a specific user with pagination.
     *
     * @param createdBy The username/email of the user who created the purchases
     * @param pageable Pagination information
     * @return A page of purchases created by the specified user
     */
    Page<Purchase> findByCreatedBy(String createdBy, Pageable pageable);

    /**
     * Find all purchases created by a specific user.
     *
     * @param createdBy The username/email of the user who created the purchases
     * @return A list of purchases created by the specified user
     */
    List<Purchase> findByCreatedBy(String createdBy);

    /**
     * Find all purchases for a specific supplier in a specific year.
     *
     * @param supplierId The ID of the supplier
     * @param year The year
     * @return A list of purchases for the supplier in the specified year
     */
    @Query("SELECT p FROM Purchase p WHERE p.supplier.id = :supplierId AND YEAR(p.purchaseDate) = :year")
    List<Purchase> findBySupplierIdAndYear(
        @Param("supplierId") UUID supplierId,
        @Param("year") int year
    );

    /**
     * Get monthly purchase statistics for a supplier.
     *
     * @param supplierId The ID of the supplier
     * @param startDate The start date
     * @param endDate The end date
     * @return A list of objects containing month, year, total purchases, and total amount
     */
    @Query("""
        SELECT
            MONTH(p.purchaseDate) as month,
            YEAR(p.purchaseDate) as year,
            COUNT(p) as totalPurchases,
            SUM(p.totalAmount) as totalAmount
        FROM Purchase p
        WHERE p.supplier.id = :supplierId
            AND p.status = 'COMPLETED'
            AND p.purchaseDate BETWEEN :startDate AND :endDate
        GROUP BY YEAR(p.purchaseDate), MONTH(p.purchaseDate)
        ORDER BY YEAR(p.purchaseDate), MONTH(p.purchaseDate)
    """)
    List<Object[]> getMonthlyPurchaseStatistics(
        @Param("supplierId") UUID supplierId,
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );
}
