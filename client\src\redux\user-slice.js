import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  userData: null,
  loading: false,
  error: null,
};

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setUserData: (state, action) => {
      state.userData = action.payload;
      state.loading = false;
    },
    updateUserProfile: (state, action) => {
      // Create a new object reference to ensure components re-render
      state.userData = {
        ...state.userData,
        ...action.payload,
      };
    },
    updateProfilePicture: (state, action) => {
      if (state.userData) {
        // Create a new object reference to ensure components re-render
        state.userData = {
          ...state.userData,
          profilePictureUrl: action.payload,
        };
      }
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
      state.loading = false;
    },
  },
});

export const {
  setUserData,
  updateUserProfile,
  updateProfilePicture,
  setLoading,
  setError,
} = userSlice.actions;

export default userSlice.reducer;
