package com.storeflow.DTOs;

import java.math.BigDecimal;

/**
 * DTO for aggregated products in the client catalog.
 * This represents products grouped by label with averaged prices.
 */
public record CatalogProductDTO(
    String label,
    String description,
    String imageUrl,
    String brand,
    BigDecimal averagePrice,
    Integer totalAvailableQuantity,
    Integer averageDeliveryTime
) {
}
