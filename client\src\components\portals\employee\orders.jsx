import { useState, useEffect, useMemo, useCallback } from "react";
import { toast } from "react-hot-toast";
import {
  ClipboardListIcon,
  SearchIcon,
  EyeIcon,
  ClockIcon,
  TruckIcon,
  CheckCircleIcon,
  PackageIcon,
  UserIcon,
  PhoneIcon,
  CalendarIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  Loader2,
  FilterIcon,
  SortAscIcon,
  SortDescIcon,
  XIcon,
} from "lucide-react";
import { orderService } from "@/services/order-service";
import { ripple, isDesktop } from "@/utils";

const EmployeeOrders = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("ALL");
  const [updatingStatus, setUpdatingStatus] = useState(false);
  const [sortBy, setSortBy] = useState("orderDate");
  const [sortDir, setSortDir] = useState("desc");
  const [pagination, setPagination] = useState({
    page: 0,
    size: 15, // Increased for better performance
    totalElements: 0,
    totalPages: 0,
  });

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (pagination.page === 0) {
        fetchOrders();
      } else {
        setPagination((prev) => ({ ...prev, page: 0 }));
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, statusFilter, sortBy, sortDir]);

  useEffect(() => {
    fetchOrders();
  }, [pagination.page, sortBy, sortDir]);

  const fetchOrders = useCallback(async () => {
    try {
      setLoading(true);
      const response = await orderService.getAllOrders({
        page: pagination.page,
        size: pagination.size,
        sortBy,
        sortDir,
        search: searchQuery.trim() || undefined,
        status: statusFilter !== "ALL" ? statusFilter : undefined,
      });

      if (response.success) {
        setOrders(response.data.content);
        setPagination((prev) => ({
          ...prev,
          totalElements: response.data.totalElements,
          totalPages: response.data.totalPages,
        }));
      }
    } catch (error) {
      console.error("Error fetching orders:", error);
      toast.error("Failed to load orders");
    } finally {
      setLoading(false);
    }
  }, [
    pagination.page,
    pagination.size,
    sortBy,
    sortDir,
    searchQuery,
    statusFilter,
  ]);

  const handleStatusUpdate = async (orderId, newStatus) => {
    try {
      setUpdatingStatus(true);
      const response = await orderService.updateOrderStatus(orderId, {
        status: newStatus,
        notes: `Status updated to ${newStatus}`,
      });

      if (response.success) {
        toast.success(`Order status updated to ${newStatus}`);
        fetchOrders(); // Refresh the orders list
        if (selectedOrder && selectedOrder.id === orderId) {
          setSelectedOrder({ ...selectedOrder, status: newStatus });
        }
      }
    } catch (error) {
      console.error("Error updating order status:", error);
      toast.error("Failed to update order status");
    } finally {
      setUpdatingStatus(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "PENDING":
        return "bg-amber-100 text-amber-800 border-amber-200";
      case "IN_PROGRESS":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "COMPLETED":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-blue-gray-100 text-blue-gray-800 border-blue-gray-200";
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "PENDING":
        return <ClockIcon className="h-4 w-4" />;
      case "IN_PROGRESS":
        return <TruckIcon className="h-4 w-4" />;
      case "COMPLETED":
        return <CheckCircleIcon className="h-4 w-4" />;
      default:
        return <PackageIcon className="h-4 w-4" />;
    }
  };

  const getNextStatus = (currentStatus) => {
    switch (currentStatus) {
      case "PENDING":
        return "IN_PROGRESS";
      case "IN_PROGRESS":
        return "COMPLETED";
      default:
        return null;
    }
  };

  const getNextStatusLabel = (currentStatus) => {
    switch (currentStatus) {
      case "PENDING":
        return "Start Processing";
      case "IN_PROGRESS":
        return "Mark Complete";
      default:
        return null;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const viewOrderDetails = (order) => {
    setSelectedOrder(order);
    setShowOrderDetails(true);
  };

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortDir(sortDir === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortDir("desc");
    }
  };

  const handlePageChange = (newPage) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  const clearFilters = () => {
    setSearchQuery("");
    setStatusFilter("ALL");
    setSortBy("orderDate");
    setSortDir("desc");
  };

  // Memoized pagination numbers for better performance
  const paginationNumbers = useMemo(() => {
    const { page, totalPages } = pagination;
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (
      let i = Math.max(0, page - delta);
      i <= Math.min(totalPages - 1, page + delta);
      i++
    ) {
      range.push(i);
    }

    if (range[0] > 0) {
      if (range[0] > 1) {
        rangeWithDots.push(0, "...");
      } else {
        rangeWithDots.push(0);
      }
    }

    rangeWithDots.push(...range);

    if (range[range.length - 1] < totalPages - 1) {
      if (range[range.length - 1] < totalPages - 2) {
        rangeWithDots.push("...", totalPages - 1);
      } else {
        rangeWithDots.push(totalPages - 1);
      }
    }

    return rangeWithDots;
  }, [pagination.page, pagination.totalPages]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mb-2" />
          <p className="text-blue-gray-500 text-lg">Loading orders...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen mx-auto space-y-4">
      {/* Page header */}
      <div className="bg-white rounded-xl shadow-sm border border-blue-gray-200 p-4 sm:p-6">
        <div className="flex flex-col gap-4">
          {/* Title Section */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div className="flex-1 min-w-0">
              <h1 className="text-xl sm:text-2xl font-bold text-blue-gray-900 flex items-center gap-2">
                <ClipboardListIcon className="h-5 w-5 sm:h-6 sm:w-6 text-blue-gray-700 flex-shrink-0" />
                <span className="truncate">Orders Management</span>
              </h1>
              <p className="text-blue-gray-500 mt-1 text-sm sm:text-base">
                Manage client orders and update their status
              </p>
            </div>
          </div>

          {/* Search, Filter, and Sort Controls */}
          <div className="flex flex-col gap-4">
            <div className="flex flex-col sm:flex-row gap-3">
              {/* Search Bar with custom styling */}
              <div className="relative flex-1">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <SearchIcon className="h-5 w-5 text-blue-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search by client name, order ID, or phone..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full h-[53px] pl-12 pr-4 border border-blue-gray-200 rounded-xl leading-5 bg-white placeholder-blue-gray-400 focus:outline-none focus:placeholder-blue-gray-500 focus:ring-1 focus:ring-blue-gray-500 focus:border-blue-gray-500 text-blue-gray-900 text-base lg:text-lg transition-all duration-300"
                />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery("")}
                    className="absolute inset-y-0 right-0 pr-4 flex items-center text-blue-gray-400 hover:text-blue-gray-600 transition-colors"
                    type="button"
                  >
                    <XIcon className="h-5 w-5" />
                  </button>
                )}
              </div>

              {/* Status Filter with custom styling */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <FilterIcon className="h-5 w-5 text-blue-gray-400" />
                </div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="h-[53px] pl-12 pr-10 border border-blue-gray-200 rounded-xl bg-white text-blue-gray-900 focus:outline-none focus:ring-1 focus:ring-blue-gray-500 focus:border-blue-gray-500 appearance-none cursor-pointer text-base lg:text-lg transition-all duration-300 min-w-[160px]"
                >
                  <option value="ALL">All Status</option>
                  <option value="PENDING">Pending</option>
                  <option value="IN_PROGRESS">In Progress</option>
                  <option value="COMPLETED">Completed</option>
                </select>
              </div>
            </div>

            {/* Sort Controls and Clear Filters */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-blue-gray-700">
                  Sort by:
                </span>
                <div className="flex gap-2">
                  <button
                    onMouseDown={(event) => ripple.create(event, "dark")}
                    onClick={() => handleSort("orderDate")}
                    className={`flex items-center gap-1 px-3 py-2 rounded-lg border transition-all duration-300 text-sm ${
                      sortBy === "orderDate"
                        ? "border-blue-gray-400 bg-blue-gray-50 text-blue-gray-800"
                        : "border-blue-gray-200 text-blue-gray-600 hover:bg-blue-gray-50"
                    }`}
                    type="button"
                  >
                    <CalendarIcon className="h-4 w-4" />
                    Date
                    {sortBy === "orderDate" &&
                      (sortDir === "asc" ? (
                        <SortAscIcon className="h-4 w-4" />
                      ) : (
                        <SortDescIcon className="h-4 w-4" />
                      ))}
                  </button>
                  <button
                    onMouseDown={(event) => ripple.create(event, "dark")}
                    onClick={() => handleSort("totalAmount")}
                    className={`flex items-center gap-1 px-3 py-2 rounded-lg border transition-all duration-300 text-sm ${
                      sortBy === "totalAmount"
                        ? "border-blue-gray-400 bg-blue-gray-50 text-blue-gray-800"
                        : "border-blue-gray-200 text-blue-gray-600 hover:bg-blue-gray-50"
                    }`}
                    type="button"
                  >
                    Amount
                    {sortBy === "totalAmount" &&
                      (sortDir === "asc" ? (
                        <SortAscIcon className="h-4 w-4" />
                      ) : (
                        <SortDescIcon className="h-4 w-4" />
                      ))}
                  </button>
                  <button
                    onMouseDown={(event) => ripple.create(event, "dark")}
                    onClick={() => handleSort("status")}
                    className={`flex items-center gap-1 px-3 py-2 rounded-lg border transition-all duration-300 text-sm ${
                      sortBy === "status"
                        ? "border-blue-gray-400 bg-blue-gray-50 text-blue-gray-800"
                        : "border-blue-gray-200 text-blue-gray-600 hover:bg-blue-gray-50"
                    }`}
                    type="button"
                  >
                    Status
                    {sortBy === "status" &&
                      (sortDir === "asc" ? (
                        <SortAscIcon className="h-4 w-4" />
                      ) : (
                        <SortDescIcon className="h-4 w-4" />
                      ))}
                  </button>
                </div>
              </div>

              {(searchQuery ||
                statusFilter !== "ALL" ||
                sortBy !== "orderDate" ||
                sortDir !== "desc") && (
                <button
                  onMouseDown={(event) => ripple.create(event, "dark")}
                  onClick={clearFilters}
                  className="flex items-center gap-2 px-4 py-2 text-blue-gray-600 rounded-lg hover:bg-blue-gray-50 transition-all duration-300 text-sm font-medium border border-blue-gray-200"
                  type="button"
                >
                  <XIcon className="h-4 w-4" />
                  Clear Filters
                </button>
              )}
            </div>

            {/* Results Summary */}
            <div className="flex items-center justify-between text-sm text-blue-gray-600">
              <span>
                Showing {orders.length} of {pagination.totalElements} orders
              </span>
              {loading && (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Loading...</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Orders Grid */}
      <div className="grid grid-cols-1 gap-4">
        {orders.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm border border-blue-gray-200 p-8 text-center">
            <ClipboardListIcon className="h-16 w-16 mx-auto text-blue-gray-300 mb-4" />
            <h3 className="text-lg font-semibold text-blue-gray-900 mb-2">
              No orders found
            </h3>
            <p className="text-blue-gray-500">
              {searchQuery || statusFilter !== "ALL"
                ? "No orders match your current filters."
                : "No orders have been placed yet."}
            </p>
          </div>
        ) : (
          orders.map((order) => (
            <div
              key={order.id}
              className="bg-white rounded-xl shadow-sm border border-blue-gray-200 hover:shadow-md hover:border-blue-gray-300 transition-all duration-300 overflow-hidden"
            >
              <div className="p-6">
                <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
                  {/* Order Info */}
                  <div className="flex-1 space-y-3">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-3">
                      <div className="flex items-center gap-3">
                        <h3 className="text-lg font-semibold text-blue-gray-900">
                          Order #{order.id.slice(-8)}
                        </h3>
                        <div
                          className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(
                            order.status
                          )}`}
                        >
                          {getStatusIcon(order.status)}
                          <span>{order.status}</span>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 text-sm">
                      <div className="flex items-center gap-2">
                        <UserIcon className="h-4 w-4 text-blue-gray-400" />
                        <span className="text-blue-gray-600">
                          {order.clientName || "Unknown Client"}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <PhoneIcon className="h-4 w-4 text-blue-gray-400" />
                        <span className="text-blue-gray-600">
                          {order.phoneNumber || "No phone"}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CalendarIcon className="h-4 w-4 text-blue-gray-400" />
                        <span className="text-blue-gray-600">
                          {formatDate(order.orderDate)}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-2xl font-bold text-green-600">
                          {order.totalAmount.toFixed(2)} MAD
                        </p>
                        <p className="text-sm text-blue-gray-500">
                          {order.items.length} item
                          {order.items.length !== 1 ? "s" : ""}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col sm:flex-row lg:flex-col gap-2 lg:min-w-[200px]">
                    <button
                      onMouseDown={(event) => ripple.create(event, "dark")}
                      onClick={() => viewOrderDetails(order)}
                      className="flex items-center justify-center gap-2 px-4 py-2 text-blue-600 rounded-lg hover:bg-blue-50 transition-all duration-300 text-sm font-medium border border-blue-200"
                      type="button"
                    >
                      <EyeIcon className="h-4 w-4" />
                      <span>View Details</span>
                    </button>

                    {getNextStatus(order.status) && (
                      <button
                        onMouseDown={(event) =>
                          ripple.create(event, isDesktop() ? "light" : "dark")
                        }
                        onClick={() =>
                          handleStatusUpdate(
                            order.id,
                            getNextStatus(order.status)
                          )
                        }
                        disabled={updatingStatus}
                        className={`flex items-center justify-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 text-sm ${
                          updatingStatus
                            ? "bg-blue-gray-100 text-blue-gray-400 cursor-not-allowed"
                            : "bg-blue-gray-900 text-white hover:bg-blue-gray-800 hover:shadow-md"
                        }`}
                        type="button"
                      >
                        {updatingStatus ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          getStatusIcon(getNextStatus(order.status))
                        )}
                        <span>{getNextStatusLabel(order.status)}</span>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Enhanced Pagination */}
      {pagination.totalPages > 1 && (
        <div className="bg-white rounded-xl shadow-sm border border-blue-gray-200 p-4 mt-6">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            {/* Pagination Info */}
            <div className="text-sm text-blue-gray-600">
              Showing {pagination.page * pagination.size + 1} to{" "}
              {Math.min(
                (pagination.page + 1) * pagination.size,
                pagination.totalElements
              )}{" "}
              of {pagination.totalElements} orders
            </div>

            {/* Pagination Controls */}
            <div className="flex items-center gap-2">
              {/* Previous Button */}
              <button
                onMouseDown={(event) =>
                  ripple.create(event, isDesktop() ? "light" : "dark")
                }
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 0}
                className={`flex items-center gap-1 px-3 py-2 rounded-lg border transition-all duration-300 ${
                  pagination.page === 0
                    ? "border-blue-gray-200 text-blue-gray-400 cursor-not-allowed"
                    : "border-blue-gray-300 text-blue-gray-700 hover:bg-blue-gray-50 hover:border-blue-gray-400"
                }`}
                type="button"
              >
                <ChevronLeftIcon className="h-4 w-4" />
                <span className="hidden sm:inline">Previous</span>
              </button>

              {/* Page Numbers */}
              <div className="flex gap-1">
                {paginationNumbers.map((pageNum, index) => (
                  <div key={index}>
                    {pageNum === "..." ? (
                      <span className="px-3 py-2 text-blue-gray-400">...</span>
                    ) : (
                      <button
                        onMouseDown={(event) =>
                          ripple.create(event, isDesktop() ? "light" : "dark")
                        }
                        onClick={() => handlePageChange(pageNum)}
                        className={`w-10 h-10 rounded-lg border transition-all duration-300 text-sm font-medium ${
                          pagination.page === pageNum
                            ? "bg-blue-gray-900 text-white border-blue-gray-800 shadow-md"
                            : "border-blue-gray-300 text-blue-gray-700 hover:bg-blue-gray-50 hover:border-blue-gray-400"
                        }`}
                        type="button"
                      >
                        {pageNum + 1}
                      </button>
                    )}
                  </div>
                ))}
              </div>

              {/* Next Button */}
              <button
                onMouseDown={(event) =>
                  ripple.create(event, isDesktop() ? "light" : "dark")
                }
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page >= pagination.totalPages - 1}
                className={`flex items-center gap-1 px-3 py-2 rounded-lg border transition-all duration-300 ${
                  pagination.page >= pagination.totalPages - 1
                    ? "border-blue-gray-200 text-blue-gray-400 cursor-not-allowed"
                    : "border-blue-gray-300 text-blue-gray-700 hover:bg-blue-gray-50 hover:border-blue-gray-400"
                }`}
                type="button"
              >
                <span className="hidden sm:inline">Next</span>
                <ChevronRightIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Order Details Modal */}
      {showOrderDetails && selectedOrder && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
          <div className="bg-white rounded-xl shadow-xl border border-blue-gray-200 w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="max-h-[90vh] overflow-y-auto scrollbar-thin scrollbar-thumb-blue-400 hover:scrollbar-thumb-blue-500 scrollbar-track-transparent scrollbar-thumb-rounded-md">
              {/* Modal Header */}
              <div className="sticky top-0 bg-white border-b border-blue-gray-200 px-6 py-4 z-10">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-blue-gray-900 flex items-center gap-2">
                    <ClipboardListIcon className="h-5 w-5 text-blue-gray-700" />
                    Order Details - #{selectedOrder.id.slice(-8)}
                  </h2>
                  <button
                    onMouseDown={(event) => ripple.create(event, "dark")}
                    onClick={() => setShowOrderDetails(false)}
                    className="p-2 rounded-lg text-blue-gray-400 hover:text-blue-gray-600 hover:bg-blue-gray-50 transition-all duration-300"
                    type="button"
                  >
                    <XIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="px-6 py-6 space-y-6">
                {/* Order Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-blue-gray-900">
                      Order Information
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium text-blue-gray-700 mb-1">
                          Status
                        </p>
                        <div
                          className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(
                            selectedOrder.status
                          )}`}
                        >
                          {getStatusIcon(selectedOrder.status)}
                          <span>{selectedOrder.status}</span>
                        </div>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-blue-gray-700 mb-1">
                          Order Date
                        </p>
                        <p className="text-blue-gray-600">
                          {formatDate(selectedOrder.orderDate)}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-blue-gray-700 mb-1">
                          Total Amount
                        </p>
                        <p className="text-2xl font-bold text-green-600">
                          {selectedOrder.totalAmount.toFixed(2)} MAD
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-blue-gray-900">
                      Client Information
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium text-blue-gray-700 mb-1">
                          Client Name
                        </p>
                        <p className="text-blue-gray-600">
                          {selectedOrder.clientName || "Unknown Client"}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-blue-gray-700 mb-1">
                          Phone Number
                        </p>
                        <p className="text-blue-gray-600">
                          {selectedOrder.phoneNumber || "No phone provided"}
                        </p>
                      </div>
                      {selectedOrder.notes && (
                        <div>
                          <p className="text-sm font-medium text-blue-gray-700 mb-1">
                            Notes
                          </p>
                          <p className="text-blue-gray-600">
                            {selectedOrder.notes}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Order Items */}
                <div>
                  <h3 className="text-lg font-semibold text-blue-gray-900 mb-4">
                    Order Items
                  </h3>
                  <div className="space-y-3">
                    {selectedOrder.items.map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-4 p-4 border border-blue-gray-200 rounded-lg bg-blue-gray-50"
                      >
                        <div className="w-16 h-16 bg-white rounded-lg overflow-hidden border border-blue-gray-100 flex-shrink-0">
                          <img
                            src={
                              item.productImageUrl || "/img/default-product.jpg"
                            }
                            alt={item.productLabel}
                            className="w-full h-full object-contain"
                            onError={(e) => {
                              e.target.onerror = null;
                              e.target.src = "/img/default-product.jpg";
                            }}
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-blue-gray-900 truncate">
                            {item.productLabel}
                          </h4>
                          {item.productBrand && (
                            <p className="text-sm text-blue-gray-500">
                              Brand: {item.productBrand}
                            </p>
                          )}
                          <p className="text-sm text-blue-gray-600 line-clamp-2">
                            {item.productDescription ||
                              "No description available"}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-blue-gray-900">
                            {item.quantity} × {item.unitPrice.toFixed(2)} MAD
                          </p>
                          <p className="text-lg font-bold text-green-600">
                            {item.totalPrice.toFixed(2)} MAD
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t border-blue-gray-200">
                  <button
                    onMouseDown={(event) => ripple.create(event, "dark")}
                    onClick={() => setShowOrderDetails(false)}
                    className="flex-1 px-6 py-3 rounded-xl border border-blue-gray-200 bg-white text-blue-gray-700 transition-all duration-300 text-base font-medium hover:bg-blue-gray-50 hover:border-blue-gray-300"
                    type="button"
                  >
                    Close
                  </button>
                  {getNextStatus(selectedOrder.status) && (
                    <button
                      onMouseDown={(event) =>
                        ripple.create(event, isDesktop() ? "light" : "dark")
                      }
                      onClick={() => {
                        handleStatusUpdate(
                          selectedOrder.id,
                          getNextStatus(selectedOrder.status)
                        );
                        setShowOrderDetails(false);
                      }}
                      disabled={updatingStatus}
                      className={`flex-1 px-6 py-3 rounded-xl font-medium transition-all duration-300 text-base flex items-center justify-center gap-2 ${
                        updatingStatus
                          ? "bg-blue-gray-100 text-blue-gray-400 cursor-not-allowed"
                          : "bg-blue-gray-900 text-white hover:bg-blue-gray-800 shadow-md hover:shadow-lg"
                      }`}
                      type="button"
                    >
                      {updatingStatus ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        getStatusIcon(getNextStatus(selectedOrder.status))
                      )}
                      {getNextStatusLabel(selectedOrder.status)}
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmployeeOrders;
