package com.storeflow.services;

import com.storeflow.DTOs.MonthlyAnalyticsDTO;
import com.storeflow.DTOs.SupplierDTO;
import com.storeflow.enums.PurchaseStatus;
import com.storeflow.enums.Role;
import com.storeflow.exception.ResourceNotFoundException;
import com.storeflow.models.Purchase;
import com.storeflow.models.PurchaseItem;
import com.storeflow.models.Supplier;
import com.storeflow.repositories.PurchaseItemRepository;
import com.storeflow.repositories.PurchaseRepository;
import com.storeflow.repositories.SupplierRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.security.Principal;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.YearMonth;
import java.time.format.TextStyle;
import java.util.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Locale;
import java.util.stream.Collectors;

/**
 * Service for analytics-related operations
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AnalyticsService {

    private final SupplierRepository supplierRepository;
    private final UserService userService;
    private final PurchaseItemRepository purchaseItemRepository;
    private final PurchaseRepository purchaseRepository;

    /**
     * Get monthly sales and revenue data for the authenticated supplier
     *
     * @param principal The authenticated user
     * @return Map containing monthly analytics data
     */
    public Map<String, Object> getSupplierMonthlyAnalytics(Principal principal) {
        log.info("Generating monthly analytics for supplier: {}", principal.getName());

        // Get the current supplier
        Object userInfo = userService.getAuthenticatedUserProfileInfo(principal);
        if (!(userInfo instanceof SupplierDTO)) {
            log.error("User is not a supplier: {}", principal.getName());
            throw new ResourceNotFoundException("Only suppliers can access this endpoint");
        }

        SupplierDTO supplierDTO = (SupplierDTO) userInfo;
        if (!supplierDTO.role().equals(Role.SUPPLIER)) {
            log.error("User is not a supplier: {}", principal.getName());
            throw new ResourceNotFoundException("Only suppliers can access this endpoint");
        }

        UUID userId = supplierDTO.id();

        Supplier supplier = supplierRepository.findByUserId(userId)
            .orElseThrow(() -> {
                log.error("Supplier not found for user ID: {}", userId);
                return new ResourceNotFoundException("Supplier not found");
            });

        // Get the current year
        int currentYear = YearMonth.now().getYear();

        // Initialize monthly data structures
        Map<String, MonthlyAnalyticsDTO> monthlySalesMap = initializeMonthlyDataMap();
        Map<String, MonthlyAnalyticsDTO> monthlyRevenueMap = initializeMonthlyDataMap();

        try {
            // Get all purchases for this supplier
            List<Purchase> purchases = purchaseRepository.findBySupplierId(supplier.getId());

            // Filter purchases for the current year
            purchases = purchases.stream()
                .filter(p -> {
                    LocalDateTime purchaseDate = p.getPurchaseDate();
                    return purchaseDate != null &&
                           purchaseDate.getYear() == currentYear;
                })
                .collect(Collectors.toList());
            log.info("Found {} purchases for supplier {} in year {}", purchases.size(), supplier.getId(), currentYear);

            // Process each purchase to calculate monthly sales and revenue
            for (Purchase purchase : purchases) {
                // Skip purchases that are not completed
                if (purchase.getStatus() != PurchaseStatus.COMPLETED) {
                    continue;
                }

                // Get the month name
                String monthName = purchase.getPurchaseDate().getMonth().getDisplayName(TextStyle.SHORT, Locale.ENGLISH);

                // Get purchase items for this purchase
                List<PurchaseItem> items = purchase.getItems();

                // Calculate total units sold and revenue for this purchase
                int unitsSold = items.stream().mapToInt(PurchaseItem::getQuantity).sum();
                BigDecimal revenue = items.stream()
                        .map(item -> item.getUnitPrice().multiply(BigDecimal.valueOf(item.getQuantity())))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                log.info("Purchase {} - Month: {}, Units: {}, Revenue: {}",
                        purchase.getId(), monthName, unitsSold, revenue);

                // Update monthly sales data
                MonthlyAnalyticsDTO currentSalesData = monthlySalesMap.get(monthName);
                monthlySalesMap.put(monthName, new MonthlyAnalyticsDTO(
                        monthName,
                        currentSalesData.sales() + unitsSold,
                        currentSalesData.revenue()
                ));

                // Update monthly revenue data
                MonthlyAnalyticsDTO currentRevenueData = monthlyRevenueMap.get(monthName);
                monthlyRevenueMap.put(monthName, new MonthlyAnalyticsDTO(
                        monthName,
                        0, // We don't use sales count for revenue data
                        currentRevenueData.revenue().add(revenue)
                ));
            }
        } catch (Exception e) {
            log.error("Error processing purchases for analytics", e);
            // Fall back to using the repository method if there's an error
            return getFallbackAnalytics(supplier.getId());
        }

        // Convert maps to lists in the correct order
        List<MonthlyAnalyticsDTO> salesData = convertMonthlyDataMapToList(monthlySalesMap);
        List<MonthlyAnalyticsDTO> revenueData = convertMonthlyDataMapToList(monthlyRevenueMap);

        // Return the data in a map
        Map<String, Object> result = new HashMap<>();
        result.put("sales", salesData);
        result.put("revenue", revenueData);

        return result;
    }

    /**
     * Fallback method to get analytics data using the repository method
     *
     * @param supplierId The ID of the supplier
     * @return Map containing monthly analytics data
     */
    private Map<String, Object> getFallbackAnalytics(UUID supplierId) {
        log.info("Using fallback method to get analytics for supplier: {}", supplierId);

        // Get real data from the database using repository methods
        List<MonthlyAnalyticsDTO> salesData = getMonthlySupplierSalesData(supplierId);
        List<MonthlyAnalyticsDTO> revenueData = getMonthlySupplierRevenueData(supplierId);

        // Return the data in a map
        Map<String, Object> result = new HashMap<>();
        result.put("sales", salesData);
        result.put("revenue", revenueData);

        return result;
    }

    /**
     * Get monthly sales data for a supplier from the database.
     *
     * @param supplierId The ID of the supplier
     * @return A list of monthly sales data
     */
    private List<MonthlyAnalyticsDTO> getMonthlySupplierSalesData(UUID supplierId) {
        log.info("Getting monthly sales data for supplier: {}", supplierId);

        // Get the start and end dates for the current year
        int currentYear = YearMonth.now().getYear();
        LocalDateTime startDate = LocalDateTime.of(currentYear, 1, 1, 0, 0, 0);
        LocalDateTime endDate = LocalDateTime.of(currentYear, 12, 31, 23, 59, 59);

        // Get the monthly sales statistics from the database
        List<Object[]> monthlyStats = purchaseItemRepository.getMonthlySupplierSalesStatistics(
                supplierId, startDate, endDate);

        log.info("Found {} monthly statistics records for sales", monthlyStats.size());

        // Create a map to store the data by month
        Map<String, MonthlyAnalyticsDTO> monthlyData = initializeMonthlyDataMap();

        // Fill in the actual data
        for (Object[] stat : monthlyStats) {
            int month = (int) stat[0]; // 1-based month
            int year = (int) stat[1];
            long totalQuantity = (long) stat[2];
            BigDecimal totalAmount = (BigDecimal) stat[3];

            String monthName = Month.of(month).getDisplayName(TextStyle.SHORT, Locale.ENGLISH);

            log.info("Sales - Month: {}, Year: {}, Quantity: {}, Amount: {}",
                    monthName, year, totalQuantity, totalAmount);

            // Update the data in the map
            monthlyData.put(monthName, new MonthlyAnalyticsDTO(
                    monthName,
                    totalQuantity > Integer.MAX_VALUE ? Integer.MAX_VALUE : (int) totalQuantity,
                    totalAmount));
        }

        // Convert the map to a list in the correct order
        return convertMonthlyDataMapToList(monthlyData);
    }

    /**
     * Get monthly revenue data for a supplier from the database.
     *
     * @param supplierId The ID of the supplier
     * @return A list of monthly revenue data
     */
    private List<MonthlyAnalyticsDTO> getMonthlySupplierRevenueData(UUID supplierId) {
        log.info("Getting monthly revenue data for supplier: {}", supplierId);

        // Get the start and end dates for the current year
        int currentYear = YearMonth.now().getYear();
        LocalDateTime startDate = LocalDateTime.of(currentYear, 1, 1, 0, 0, 0);
        LocalDateTime endDate = LocalDateTime.of(currentYear, 12, 31, 23, 59, 59);

        // Get the monthly sales statistics from the database
        List<Object[]> monthlyStats = purchaseItemRepository.getMonthlySupplierSalesStatistics(
                supplierId, startDate, endDate);

        log.info("Found {} monthly statistics records for revenue", monthlyStats.size());

        // Create a map to store the data by month
        Map<String, MonthlyAnalyticsDTO> monthlyData = initializeMonthlyDataMap();

        // Fill in the actual data
        for (Object[] stat : monthlyStats) {
            int month = (int) stat[0]; // 1-based month
            int year = (int) stat[1];
            BigDecimal totalAmount = (BigDecimal) stat[3];

            String monthName = Month.of(month).getDisplayName(TextStyle.SHORT, Locale.ENGLISH);

            log.info("Revenue - Month: {}, Year: {}, Amount: {}", monthName, year, totalAmount);

            // Update the data in the map (for revenue, we use 0 for sales count)
            monthlyData.put(monthName, new MonthlyAnalyticsDTO(monthName, 0, totalAmount));
        }

        // Convert the map to a list in the correct order
        return convertMonthlyDataMapToList(monthlyData);
    }

    /**
     * Initialize a map with all months and zero values.
     *
     * @return A map with all months initialized to zero
     */
    private Map<String, MonthlyAnalyticsDTO> initializeMonthlyDataMap() {
        Map<String, MonthlyAnalyticsDTO> monthlyData = new LinkedHashMap<>();

        // Initialize all months with zero values
        for (int i = 1; i <= 12; i++) {
            String monthName = Month.of(i).getDisplayName(TextStyle.SHORT, Locale.ENGLISH);
            monthlyData.put(monthName, new MonthlyAnalyticsDTO(monthName, 0, BigDecimal.ZERO));
        }

        return monthlyData;
    }

    /**
     * Convert a map of monthly data to a list in the correct order.
     *
     * @param monthlyData The map of monthly data
     * @return A list of monthly data in the correct order
     */
    private List<MonthlyAnalyticsDTO> convertMonthlyDataMapToList(Map<String, MonthlyAnalyticsDTO> monthlyData) {
        List<String> monthOrder = Arrays.asList(
                "Jan", "Feb", "Mar", "Apr", "May", "Jun",
                "Jul", "Aug", "Sep", "Oct", "Nov", "Dec");

        List<MonthlyAnalyticsDTO> result = new ArrayList<>();

        for (String month : monthOrder) {
            result.add(monthlyData.get(month));
        }

        return result;
    }
}
