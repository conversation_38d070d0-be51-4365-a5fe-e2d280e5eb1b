package com.storeflow.DTOs.requests;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;

import java.util.List;

/**
 * Request DTO for creating a new order.
 */
public record CreateOrderRequest(
    @NotEmpty(message = "Order must contain at least one item")
    @Valid
    List<CreateOrderItemRequest> items,

    String notes,

    @NotBlank(message = "Phone number is required")
    @Size(max = 20, message = "Phone number must not exceed 20 characters")
    String phoneNumber
) {
}
