package com.storeflow.mappers;

import com.storeflow.DTOs.EmployeeDTO;
import com.storeflow.models.Employee;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;
import org.springframework.data.domain.Page;

@Mapper(componentModel = "spring")
public interface EmployeeMapper {
    @Mapping(source = "user.id", target = "id")
    @Mapping(source = "user.firstName", target = "firstName")
    @Mapping(source = "user.lastName", target = "lastName")
    @Mapping(source = "user.email", target = "email")
    @Mapping(source = "user.phoneNumber", target = "phoneNumber")
    @Mapping(source = "user.profilePictureUrl", target = "profilePictureUrl")
    @Mapping(source = "user.role", target = "role")
    @Mapping(source = "user.addedDate", target = "addedDate")
    EmployeeDTO toEmployeeDTO(Employee employee);

    // Batch mapping for collections
    List<EmployeeDTO> toEmployeeDTOs(List<Employee> employees);
    
    // Helper method to convert Page<Employee> to Page<EmployeeDTO>
    default Page<EmployeeDTO> toEmployeeDTOPage(Page<Employee> employeePage) {
        return employeePage.map(this::toEmployeeDTO);
    }
}
