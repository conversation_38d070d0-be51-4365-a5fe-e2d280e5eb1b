package com.storeflow.DTOs;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for PurchaseItem entity.
 */
public record PurchaseItemDTO(
    UUID id,
    UUID purchaseId,
    UUID supplierId,
    String supplierName,
    UUID productId,
    String productLabel,
    Integer quantity,
    BigDecimal unitPrice,
    BigDecimal totalPrice,
    String imageUrl,
    String brand,
    LocalDateTime createdDate,
    LocalDateTime lastModifiedDate
) {
}
