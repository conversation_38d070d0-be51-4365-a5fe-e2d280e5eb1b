import { useState, useEffect } from "react";
import { toast } from "sonner";
import {
  ShoppingCart,
  Search,
  Package,
  Loader2,
  ChevronLeft,
  ChevronRight,
  Clock,
  DollarSign,
  Plus,
  Minus,
  Trash,
  ShoppingBag,
  Check,
} from "lucide-react";
import {
  supplierService,
  supplierProductService,
  purchaseService,
} from "@/services";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ripple, isDesktop } from "@/utils";

/**
 * PurchaseProducts component for purchasing products from suppliers
 * This component can be used by both admin and employee roles
 */
export const PurchaseProducts = () => {
  // We don't need user data for this component
  const [suppliers, setSuppliers] = useState([]);
  const [selectedSupplier, setSelectedSupplier] = useState(null);
  const [supplierProducts, setSupplierProducts] = useState([]);
  const [cartItems, setCartItems] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loadingSuppliers, setLoadingSuppliers] = useState(false);
  const [loadingProducts, setLoadingProducts] = useState(false);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [isSuccessDialogOpen, setIsSuccessDialogOpen] = useState(false);
  const [purchaseNotes, setPurchaseNotes] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [submitting, setSubmitting] = useState(false);
  const [successPurchaseId, setSuccessPurchaseId] = useState(null);

  const itemsPerPage = 6;

  // Fetch suppliers on component mount
  useEffect(() => {
    fetchSuppliers();
  }, []);

  // Fetch suppliers
  const fetchSuppliers = async () => {
    try {
      setLoadingSuppliers(true);
      const response = await supplierService.getAllSuppliers({
        page: 0,
        size: 100, // Get all suppliers
      });

      if (response.success && response.data) {
        const { content } = response.data;
        setSuppliers(content);
      } else {
        toast.error("Failed to fetch suppliers");
      }
    } catch (error) {
      toast.error("Failed to fetch suppliers");
    } finally {
      setLoadingSuppliers(false);
    }
  };

  // Fetch supplier products when a supplier is selected
  useEffect(() => {
    if (selectedSupplier) {
      fetchSupplierProducts();
    } else {
      setSupplierProducts([]);
    }
  }, [selectedSupplier, searchQuery]);

  // Fetch supplier products
  const fetchSupplierProducts = async () => {
    if (!selectedSupplier) return;

    try {
      setLoadingProducts(true);

      // Call the API to get products for the selected supplier
      const response = await supplierProductService.getSupplierProducts(
        selectedSupplier.id,
        {
          query: searchQuery,
          page: 0, // Get all products and handle pagination on client side
          size: 100, // Get a large number of products
        }
      );

      if (response.success && response.data) {
        // Extract products from the response
        // The API response structure is: response.data.content for paginated results
        const products = response.data.content || [];

        // Log the products data for debugging
        console.log("Products data from API:", products);

        // Log if we have products
        if (products.length === 0) {
          console.info("No products found for this supplier");
        } else {
          console.info(`Found ${products.length} products for this supplier`);
        }

        // Log raw product data to see the exact format
        console.log(
          "Raw product data from API:",
          JSON.stringify(products, null, 2)
        );

        // Map the API response to the format expected by the component
        const formattedProducts = products.map((product) => {
          // Use the productId directly as a UUID
          const productId = product.productId;

          console.log(
            `Processing product: ${product.label}, productId: ${productId}`
          );

          return {
            id: productId, // Use productId consistently
            productId: productId, // Keep a separate productId field to ensure it's available
            label: product.label,
            description: product.description,
            stockQuantity: product.stockQuantity,
            sellingPrice: product.sellingPrice,
            purchasePrice: product.purchasePrice,
            deliveryTime: product.deliveryTime,
            imageUrl: product.imageUrl,
            brand: product.brand,
          };
        });

        setSupplierProducts(formattedProducts);

        // Set total pages based on the response or calculate from the array length
        if (response.data.totalPages !== undefined) {
          setTotalPages(response.data.totalPages);
        } else {
          setTotalPages(Math.ceil(formattedProducts.length / itemsPerPage));
        }

        // No need to log if no products found - UI already shows this state
      } else {
        toast.error(response.message || "Failed to fetch supplier products");
        setSupplierProducts([]);
      }
    } catch (error) {
      toast.error("Failed to fetch supplier products");
      setSupplierProducts([]);
    } finally {
      setLoadingProducts(false);
    }
  };

  // Get current page products
  const getCurrentPageProducts = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return supplierProducts.slice(startIndex, endIndex);
  };

  // Handle supplier selection
  const handleSupplierChange = (supplierId) => {
    const supplier = suppliers.find((s) => s.id === supplierId);
    setSelectedSupplier(supplier);
    setCurrentPage(1);
    setCartItems([]);
  };

  // Handle search
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  // Handle pagination
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  // Add item to cart
  const addToCart = (product) => {
    // Log the product being added to cart for debugging
    console.log("Adding product to cart:", product);

    // Make sure we have a productId
    const productWithIds = {
      ...product,
      // Ensure both id and productId are set correctly
      id: product.id,
      productId: product.productId || product.id,
    };

    // Check if product is already in cart
    const existingItem = cartItems.find(
      (item) => item.id === productWithIds.id
    );

    if (existingItem) {
      // Increment quantity if already in cart
      setCartItems(
        cartItems.map((item) =>
          item.id === productWithIds.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        )
      );
    } else {
      // Add new item to cart with both id and productId
      setCartItems([...cartItems, { ...productWithIds, quantity: 1 }]);
    }

    // Log the updated cart for debugging
    console.log(
      "Updated cart:",
      existingItem
        ? cartItems.map((item) =>
            item.id === productWithIds.id
              ? { ...item, quantity: item.quantity + 1 }
              : item
          )
        : [...cartItems, { ...productWithIds, quantity: 1 }]
    );

    toast.success(`Added ${product.label} to cart`);
  };

  // Remove item from cart
  const removeFromCart = (productId) => {
    setCartItems(cartItems.filter((item) => item.id !== productId));
  };

  // Update item quantity in cart
  const updateCartItemQuantity = (productId, newQuantity) => {
    if (newQuantity < 1) return;

    const product = supplierProducts.find((p) => p.id === productId);
    if (newQuantity > product.stockQuantity) {
      toast.error(`Only ${product.stockQuantity} items available in stock`);
      return;
    }

    setCartItems(
      cartItems.map((item) =>
        item.id === productId ? { ...item, quantity: newQuantity } : item
      )
    );
  };

  // Calculate cart total
  const calculateCartTotal = () => {
    return cartItems.reduce(
      (total, item) => total + item.sellingPrice * item.quantity,
      0
    );
  };

  // Submit purchase
  const submitPurchase = async () => {
    if (!selectedSupplier || cartItems.length === 0) {
      toast.error("Please select a supplier and add items to cart");
      return;
    }

    try {
      setSubmitting(true);

      // Log cart items for debugging
      console.log("Cart items before submission:", cartItems);

      // Prepare purchase data for API call
      const purchaseData = {
        supplierId: selectedSupplier.id, // Backend expects a UUID, not a string
        notes: purchaseNotes || "",
        items: cartItems.map((item) => {
          // Log each item's ID properties for debugging
          console.log(
            `Item ${item.label} - id: ${item.id}, productId: ${item.productId}`
          );

          // Use the productId directly - it should already be a UUID
          const productId = item.productId || item.id;

          if (!productId) {
            console.error(`Missing productId for item: ${item.label}`);
            throw new Error(`Missing productId for item: ${item.label}`);
          }

          return {
            productId: productId, // Backend expects a UUID, not a string
            quantity: item.quantity,
          };
        }),
      };

      // Log the final purchase data before sending
      console.log(
        "Final purchase data:",
        JSON.stringify(purchaseData, null, 2)
      );

      // Call the real purchase service API
      const response = await purchaseService.createPurchase(purchaseData);

      console.log("Purchase response:", response);

      if (response.success) {
        // Use the response data
        setSuccessPurchaseId(response.data.id);
        setIsConfirmDialogOpen(false);
        setIsSuccessDialogOpen(true);
        setCartItems([]);
        setPurchaseNotes("");

        // Show success message
        toast.success("Purchase completed successfully");

        // Refresh the supplier products to update stock quantities
        fetchSupplierProducts();
      } else {
        // Show a more detailed error message
        const errorMessage = response.message || "Failed to create purchase";
        console.error("Purchase failed:", errorMessage);
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Unexpected error during purchase:", error);

      // Try to extract a meaningful error message
      let errorMessage = "Failed to create purchase";
      if (error.response) {
        if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (typeof error.response.data === "string") {
          errorMessage = error.response.data;
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen mx-auto space-y-4">
      {/* Page header with title and search */}
      <div className="bg-white rounded-xl shadow-sm border border-blue-gray-200 p-4 sm:p-6">
        <div className="flex flex-col gap-4">
          {/* Title Section */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div className="flex-1 min-w-0">
              <h1 className="text-xl sm:text-2xl font-bold text-blue-gray-900 flex items-center gap-2">
                <ShoppingCart className="h-5 w-5 sm:h-6 sm:w-6 text-blue-gray-700 flex-shrink-0" />
                <span className="truncate">Purchase Products</span>
              </h1>
              <p className="text-blue-gray-500 mt-1 text-sm sm:text-base">
                Purchase products from suppliers for your inventory
              </p>
            </div>
          </div>

          {/* Search and Cart Section */}
          <div className="flex flex-col sm:flex-row gap-3">
            {/* Custom Search Input */}
            <div className="flex-1 sm:max-w-sm">
              <div className="flex items-center w-full border border-blue-gray-500 rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px]">
                <Search className="ml-3 h-5 w-5 lg:h-6 lg:w-6 text-blue-gray-500 flex-shrink-0" />
                <input
                  type="search"
                  placeholder="Search products..."
                  className="flex-1 h-full text-base focus:outline-0 transition-all placeholder:text-blue-gray-500 rounded-xl pr-3 pl-2 lg:text-lg bg-transparent"
                  value={searchQuery}
                  onChange={handleSearch}
                />
              </div>
            </div>

            {/* Cart Button */}
            <button
              onMouseDown={(event) =>
                ripple.create(event, isDesktop() ? "light" : "dark")
              }
              onClick={() => setIsCartOpen(true)}
              disabled={cartItems.length === 0}
              className={`group cursor-pointer text-sm flex items-center justify-center gap-2 px-4 py-3 rounded-full font-medium transition-all duration-300 h-[53px] ${
                cartItems.length === 0
                  ? "text-blue-gray-400 bg-blue-gray-100 border border-blue-gray-200 cursor-not-allowed"
                  : "text-blue-gray-700 bg-white border border-blue-gray-300 hover:bg-blue-gray-50 hover:border-blue-gray-400"
              }`}
              type="button"
            >
              <ShoppingBag className="h-4 w-4 flex-shrink-0" />
              <span>Cart ({cartItems.length})</span>
            </button>
          </div>
        </div>
      </div>

      {/* Supplier selection */}
      <div className="bg-white rounded-xl shadow-sm border border-blue-gray-200 p-6">
        <h2 className="text-lg font-semibold text-blue-gray-900 mb-4">
          Select Supplier
        </h2>

        {loadingSuppliers ? (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin text-blue-600 mr-2" />
            <span>Loading suppliers...</span>
          </div>
        ) : (
          <Select
            value={selectedSupplier?.id || ""}
            onValueChange={handleSupplierChange}
          >
            <SelectTrigger className="w-full sm:w-80 bg-white border-blue-gray-200">
              <SelectValue placeholder="Select a supplier" />
            </SelectTrigger>
            <SelectContent>
              {suppliers.length > 0 ? (
                suppliers.map((supplier) => (
                  <SelectItem key={supplier.id} value={supplier.id}>
                    {supplier.firstName} {supplier.lastName}
                  </SelectItem>
                ))
              ) : (
                <div className="p-2 text-center text-blue-gray-500">
                  No suppliers found
                </div>
              )}
            </SelectContent>
          </Select>
        )}
      </div>

      {/* Products grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {loadingProducts ? (
          // Loading state
          <div className="col-span-3 py-12 text-center">
            <div className="flex flex-col items-center justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600 mb-2" />
              <p className="text-blue-gray-500 text-lg">Loading products...</p>
            </div>
          </div>
        ) : !selectedSupplier ? (
          // No supplier selected
          <div className="col-span-3 py-12 text-center">
            <div className="flex flex-col items-center justify-center">
              <Package className="h-12 w-12 text-blue-gray-300 mb-3" />
              <p className="text-blue-gray-500 text-lg">
                Select a supplier to view products
              </p>
            </div>
          </div>
        ) : supplierProducts.length === 0 ? (
          // No products found
          <div className="col-span-3 py-12 text-center">
            <div className="flex flex-col items-center justify-center">
              <Package className="h-12 w-12 text-blue-gray-300 mb-3" />
              <p className="text-blue-gray-500 text-lg">
                No products found for this supplier
              </p>
            </div>
          </div>
        ) : (
          // Products list
          getCurrentPageProducts().map((product) => (
            <Card
              key={product.id}
              className="overflow-hidden border-blue-gray-200 rounded-xl hover:shadow-md transition-all duration-300"
            >
              {/* Product image - Fixed height container */}
              <div className="h-48 relative bg-white flex items-center justify-center p-4 border-b border-blue-gray-100">
                <div className="w-full h-40 flex items-center justify-center">
                  {product.imageUrl ? (
                    <img
                      src={product.imageUrl}
                      alt={product.label}
                      className="max-h-full max-w-full object-contain"
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src =
                          "https://placehold.co/300x200?text=No+Image";
                      }}
                    />
                  ) : (
                    <div className="flex flex-col items-center justify-center text-blue-gray-300">
                      <Package className="h-12 w-12 mb-2" />
                      <span className="text-xs text-blue-gray-400">
                        No image available
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Product details */}
              <CardContent className="p-4">
                {/* Product title - Fixed height */}
                <div className="h-7 mb-2">
                  <h3 className="font-semibold text-lg text-blue-gray-900 line-clamp-1">
                    {product.label}
                  </h3>
                </div>

                {/* Description - Fixed height */}
                <div className="text-blue-gray-500 text-sm h-12 mb-3">
                  <p className="line-clamp-2">
                    {product.description || "No description available"}
                  </p>
                </div>

                {/* Product info - Fixed layout */}
                <div className="flex flex-col gap-2 mb-4">
                  {/* Stock */}
                  <div className="flex items-center text-sm">
                    <Package className="h-4 w-4 text-blue-gray-400 mr-2 flex-shrink-0" />
                    <span className="text-blue-gray-600">
                      Stock:{" "}
                      <span className="font-medium">
                        {product.stockQuantity}
                      </span>
                    </span>
                  </div>

                  {/* Delivery time */}
                  <div className="flex items-center text-sm">
                    <Clock className="h-4 w-4 text-blue-gray-400 mr-2 flex-shrink-0" />
                    <span className="text-blue-gray-600">
                      Delivery:{" "}
                      <span className="font-medium">
                        {product.deliveryTime} days
                      </span>
                    </span>
                  </div>

                  {/* Price */}
                  <div className="flex items-center text-sm">
                    <DollarSign className="h-4 w-4 text-blue-gray-400 mr-2 flex-shrink-0" />
                    <span className="text-blue-gray-600">
                      Price:{" "}
                      <span className="font-medium">
                        {product.sellingPrice.toFixed(2)} MAD
                      </span>
                    </span>
                  </div>
                </div>

                {/* Add to cart button */}
                <button
                  onMouseDown={(event) => ripple.create(event, "light")}
                  onClick={() => addToCart(product)}
                  disabled={product.stockQuantity === 0}
                  className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-300 ${
                    product.stockQuantity === 0
                      ? "bg-blue-gray-100 text-blue-gray-400 cursor-not-allowed"
                      : "bg-blue-gray-900 text-white hover:bg-blue-gray-800 hover:shadow-md"
                  }`}
                  type="button"
                >
                  {product.stockQuantity === 0 ? "Out of Stock" : "Add to Cart"}
                </button>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Pagination */}
      {selectedSupplier && supplierProducts.length > 0 && totalPages > 1 && (
        <div className="bg-white rounded-xl shadow-sm border border-blue-gray-200 p-4 mt-6">
          <div className="flex justify-center items-center gap-2">
            <button
              onMouseDown={(event) =>
                ripple.create(event, isDesktop() ? "light" : "dark")
              }
              disabled={currentPage === 1}
              onClick={() => handlePageChange(currentPage - 1)}
              className={`flex items-center gap-1 px-3 py-2 rounded-lg border transition-all duration-300 ${
                currentPage === 1
                  ? "border-blue-gray-200 text-blue-gray-400 cursor-not-allowed"
                  : "border-blue-gray-300 text-blue-gray-700 hover:bg-blue-gray-50 hover:border-blue-gray-400"
              }`}
              type="button"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </button>

            <div className="flex gap-2">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (page) => (
                  <button
                    key={page}
                    onMouseDown={(event) =>
                      ripple.create(event, isDesktop() ? "light" : "dark")
                    }
                    onClick={() => handlePageChange(page)}
                    className={`w-9 h-9 p-0 rounded-lg transition-all duration-300 ${
                      currentPage === page
                        ? "bg-blue-gray-900 text-white border border-blue-gray-800"
                        : "border border-blue-gray-300 text-blue-gray-700 hover:bg-blue-gray-50 hover:border-blue-gray-400"
                    }`}
                    type="button"
                  >
                    {page}
                  </button>
                )
              )}
            </div>

            <button
              onMouseDown={(event) =>
                ripple.create(event, isDesktop() ? "light" : "dark")
              }
              disabled={currentPage === totalPages}
              onClick={() => handlePageChange(currentPage + 1)}
              className={`flex items-center gap-1 px-3 py-2 rounded-lg border transition-all duration-300 ${
                currentPage === totalPages
                  ? "border-blue-gray-200 text-blue-gray-400 cursor-not-allowed"
                  : "border-blue-gray-300 text-blue-gray-700 hover:bg-blue-gray-50 hover:border-blue-gray-400"
              }`}
              type="button"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>

          <div className="text-center text-blue-gray-500 mt-4 text-sm">
            Showing page {currentPage} of {totalPages}
          </div>
        </div>
      )}

      {/* Cart dialog */}
      <Dialog open={isCartOpen} onOpenChange={setIsCartOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Shopping Cart</DialogTitle>
            <DialogDescription>
              Review your items before checkout
            </DialogDescription>
          </DialogHeader>

          {cartItems.length === 0 ? (
            <div className="py-6 text-center">
              <ShoppingBag className="h-12 w-12 text-blue-gray-300 mx-auto mb-3" />
              <p className="text-blue-gray-500">Your cart is empty</p>
            </div>
          ) : (
            <>
              <div className="max-h-[300px] overflow-y-auto pr-2">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead className="text-right">Qty</TableHead>
                      <TableHead className="text-right">Price</TableHead>
                      <TableHead className="w-[50px]"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {cartItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">
                          {item.label}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end">
                            <button
                              onMouseDown={(event) =>
                                ripple.create(event, "dark")
                              }
                              onClick={() =>
                                updateCartItemQuantity(
                                  item.id,
                                  item.quantity - 1
                                )
                              }
                              className="h-6 w-6 rounded-full p-0 border border-blue-gray-200 hover:bg-blue-gray-50 transition-all duration-300 flex items-center justify-center"
                              type="button"
                            >
                              <Minus className="h-3 w-3" />
                            </button>
                            <span className="w-8 text-center">
                              {item.quantity}
                            </span>
                            <button
                              onMouseDown={(event) =>
                                ripple.create(event, "dark")
                              }
                              onClick={() =>
                                updateCartItemQuantity(
                                  item.id,
                                  item.quantity + 1
                                )
                              }
                              className="h-6 w-6 rounded-full p-0 border border-blue-gray-200 hover:bg-blue-gray-50 transition-all duration-300 flex items-center justify-center"
                              type="button"
                            >
                              <Plus className="h-3 w-3" />
                            </button>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          {(item.sellingPrice * item.quantity).toFixed(2)} MAD
                        </TableCell>
                        <TableCell>
                          <button
                            onMouseDown={(event) =>
                              ripple.create(event, "dark")
                            }
                            onClick={() => removeFromCart(item.id)}
                            className="h-7 w-7 p-0 text-red-500 hover:bg-red-50 rounded-lg transition-all duration-300 flex items-center justify-center"
                            type="button"
                          >
                            <Trash className="h-4 w-4" />
                          </button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              <div className="border-t border-blue-gray-200 pt-4 mt-2">
                <div className="flex justify-between font-semibold text-blue-gray-900">
                  <span>Total:</span>
                  <span>{calculateCartTotal().toFixed(2)} MAD</span>
                </div>
              </div>

              <DialogFooter className="flex flex-col sm:flex-row gap-3 sm:justify-between sm:space-x-0">
                <button
                  onMouseDown={(event) =>
                    ripple.create(event, isDesktop() ? "light" : "dark")
                  }
                  onClick={() => setIsCartOpen(false)}
                  className="flex-1 py-3 px-4 text-blue-gray-700 bg-white border border-blue-gray-300 rounded-lg hover:bg-blue-gray-50 hover:border-blue-gray-400 transition-all duration-300 font-medium"
                  type="button"
                >
                  Continue Shopping
                </button>
                <button
                  onMouseDown={(event) => ripple.create(event, "light")}
                  onClick={() => {
                    setIsCartOpen(false);
                    setIsConfirmDialogOpen(true);
                  }}
                  className="flex-1 py-3 px-4 bg-blue-gray-900 text-white rounded-lg hover:bg-blue-gray-800 hover:shadow-md transition-all duration-300 font-medium"
                  type="button"
                >
                  Proceed to Checkout
                </button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* Confirm purchase dialog */}
      <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Purchase</DialogTitle>
            <DialogDescription>
              Please review your order details before confirming
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div>
              <h4 className="text-sm font-medium text-blue-gray-700 mb-1">
                Supplier
              </h4>
              <p className="text-blue-gray-900">
                {selectedSupplier?.firstName} {selectedSupplier?.lastName}
              </p>
            </div>

            <div>
              <h4 className="text-sm font-medium text-blue-gray-700 mb-1">Items</h4>
              <ul className="space-y-2">
                {cartItems.map((item) => (
                  <li key={item.id} className="flex justify-between text-sm">
                    <span className="text-blue-gray-700">
                      {item.label} (x{item.quantity})
                    </span>
                    <span className="text-blue-gray-900 font-medium">
                      {(item.sellingPrice * item.quantity).toFixed(2)} MAD
                    </span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="border-t border-blue-gray-200 pt-2">
              <div className="flex justify-between font-semibold text-blue-gray-900">
                <span>Total:</span>
                <span>{calculateCartTotal().toFixed(2)} MAD</span>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-blue-gray-700 mb-2">
                Notes (Optional)
              </h4>
              <div className="border border-blue-gray-500 rounded-xl focus-within:border-blue-gray-800 transition-all">
                <textarea
                  placeholder="Add any special instructions or notes for this purchase"
                  value={purchaseNotes}
                  onChange={(e) => setPurchaseNotes(e.target.value)}
                  className="w-full h-20 p-3 text-base focus:outline-0 transition-all placeholder:text-blue-gray-500 rounded-xl bg-transparent resize-none lg:text-lg"
                  rows={3}
                />
              </div>
            </div>
          </div>

          <DialogFooter className="flex flex-col sm:flex-row gap-3 sm:justify-between sm:space-x-0">
            <button
              onMouseDown={(event) => ripple.create(event, "dark")}
              onClick={() => setIsConfirmDialogOpen(false)}
              className="flex-1 py-3 px-4 text-blue-gray-700 bg-white border border-blue-gray-300 rounded-lg hover:bg-blue-gray-50 hover:border-blue-gray-400 transition-all duration-300 font-medium"
              type="button"
            >
              Cancel
            </button>
            <button
              onMouseDown={(event) => ripple.create(event, "light")}
              onClick={submitPurchase}
              disabled={submitting}
              className={`flex-1 py-3 px-4 rounded-lg font-medium transition-all duration-300 flex items-center justify-center gap-2 ${
                submitting
                  ? "bg-blue-gray-400 text-white cursor-not-allowed"
                  : "bg-blue-gray-900 text-white hover:bg-blue-gray-800 hover:shadow-md"
              }`}
              type="button"
            >
              {submitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Confirm Purchase"
              )}
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Success dialog */}
      <Dialog open={isSuccessDialogOpen} onOpenChange={setIsSuccessDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center text-green-600">
              <Check className="mr-2 h-5 w-5" />
              Purchase Successful
            </DialogTitle>
            <DialogDescription>
              Your purchase has been submitted successfully
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="bg-green-50 border border-green-100 rounded-lg p-4 mb-4">
              <p className="text-green-800 text-sm">
                Purchase ID:{" "}
                <span className="font-medium">{successPurchaseId}</span>
              </p>
              <p className="text-green-800 text-sm mt-1">
                Status:{" "}
                <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                  Completed
                </Badge>
              </p>
            </div>

            <p className="text-blue-gray-600 text-sm">
              Your purchase has been completed successfully.
            </p>
          </div>

          <DialogFooter>
            <button
              onMouseDown={(event) =>
                ripple.create(event, isDesktop() ? "light" : "dark")
              }
              onClick={() => setIsSuccessDialogOpen(false)}
              className="w-full py-3 px-4 bg-blue-gray-900 text-white rounded-lg hover:bg-blue-gray-800 hover:shadow-md transition-all duration-300 font-medium"
              type="button"
            >
              Done
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
