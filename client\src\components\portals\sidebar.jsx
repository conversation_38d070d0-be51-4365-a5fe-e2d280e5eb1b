// src/components/portals/Sidebar.jsx
import PropTypes from "prop-types";
import { NavLink, useNavigate } from "react-router-dom";
import { Button, IconButton, Typography } from "@material-tailwind/react";
import { setOpenSidenav, useThemeController } from "@/context";
import { LogOutIcon, XIcon } from "lucide-react";
import { useAuth } from "@/context/auth-context";

export const Sidebar = ({ userRole, routes, firstName }) => {
  const [controller, dispatch] = useThemeController();
  const { sidenavColor, sidenavType, openSidenav } = controller;
  const navigate = useNavigate();
  const { logout } = useAuth();

  const sidenavTypes = {
    dark: "bg-gradient-to-br from-gray-800 to-gray-900",
    white: "bg-white shadow-sm",
    transparent: "bg-transparent",
  };

  // Handle logout
  const handleLogout = async () => {
    await logout();
    // Navigate to login page without any state
    navigate("/auth/login", { replace: true });
  };

  return (
    <aside
      className={`overflow-hidden ${sidenavTypes[sidenavType]} ${
        openSidenav ? "translate-x-0" : "-translate-x-full"
      } fixed inset-0 z-50 min-h-dvh w-full lg:w-72 transition-transform duration-300 lg:translate-x-0 border-r border-blue-gray-100`}
    >
      <div className="overflow-y-auto h-full flex flex-col py-4">
        <div>
          <div className="flex items-center justify-between lg:justify-center mx-4 py-1.5">
            <Typography
              variant="h5"
              color={sidenavType === "dark" ? "white" : "blue-gray"}
              className="flex items-center justify-center text-center rounded-lg font-supreme px-4"
            >
              Welcome back, {firstName}!
            </Typography>
            <IconButton
              variant="text"
              color="blue-gray"
              className="grid lg:hidden"
              onClick={() => setOpenSidenav(dispatch, !openSidenav)}
            >
              <XIcon className="size-5 lg:size-6 text-blue-gray-500" />
            </IconButton>
          </div>
          <div className="mx-4 mt-1.5">
            <ul className="flex flex-col gap-y-1.5">
              {routes.map(({ icon, name, path }, index) => (
                <li key={index}>
                  <NavLink to={`/${userRole}${path}`}>
                    {({ isActive }) => (
                      <Button
                        variant={isActive ? "gradient" : "text"}
                        color={
                          isActive
                            ? sidenavColor
                            : sidenavType === "dark"
                            ? "white"
                            : "blue-gray"
                        }
                        className="flex items-center gap-4 px-4 capitalize"
                        fullWidth
                      >
                        {icon}
                        <Typography
                          color="inherit"
                          className="font-normal capitalize font-satoshi text-lg"
                        >
                          {name}
                        </Typography>
                      </Button>
                    )}
                  </NavLink>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Spacer to push logout down while still scrollable */}
        <div className="mt-auto pt-8 px-4">
          <Button
            variant="text"
            className="flex items-center gap-4 px-4 capitalize text-red-500 hover:bg-red-50/50 transition-colors"
            fullWidth
            onClick={handleLogout} // Added logout handler
          >
            <LogOutIcon className="size-5 md:size-6" />
            <Typography
              color="inherit"
              className="font-normal capitalize font-satoshi text-lg"
            >
              Log out
            </Typography>
          </Button>
        </div>
      </div>
    </aside>
  );
};

Sidebar.propTypes = {
  userRole: PropTypes.string.isRequired,
  firstName: PropTypes.string.isRequired,
  routes: PropTypes.arrayOf(PropTypes.object).isRequired,
};

export default Sidebar;
