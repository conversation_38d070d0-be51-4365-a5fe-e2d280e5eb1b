import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import {
  Package,
  TrendingUp,
  DollarSign,
  ShoppingCart,
  ArrowUpRight,
  Wallet,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { supplierProductService } from "@/services/supplier-product-service";
import { supplierAnalyticsService } from "@/services/supplier-analytics-service";
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from "recharts";

// Enhanced currency formatter for better readability with large numbers
const formatCurrency = (value) => {
  // Format with spaces as thousand separators and comma as decimal separator
  const formattedNumber = new Intl.NumberFormat("fr-MA", {
    style: "decimal",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
    useGrouping: true,
  }).format(value);

  // Return with MAD currency
  return formattedNumber + " MAD";
};

// Custom tooltip for charts
const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-4 border border-slate-200 rounded-lg shadow-md">
        <p className="font-semibold text-slate-800 mb-2">{label}</p>
        {payload.map((entry, index) => (
          <p
            key={`item-${index}`}
            style={{ color: entry.color }}
            className="text-sm font-medium flex items-center justify-between gap-3"
          >
            <span>{entry.name}:</span>
            <span className="tabular-nums">
              {entry.name.toLowerCase().includes("revenue")
                ? formatCurrency(entry.value)
                : entry.value.toLocaleString()}
            </span>
          </p>
        ))}
      </div>
    );
  }
  return null;
};

export const Dashboard = () => {
  const { userData: user } = useSelector((state) => state.user);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalProducts: 0,
    totalStock: 0,
    averagePrice: 0,
    lowStockItems: 0,
    totalValue: 0,
  });

  // Monthly data for charts - initialize with empty arrays
  const [salesData, setSalesData] = useState([]);
  const [revenueData, setRevenueData] = useState([]);

  // Fetch products and analytics data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch products for statistics
        const productsResponse = await supplierProductService.getMyProducts();

        // Fetch analytics data for charts
        const analyticsResponse =
          await supplierAnalyticsService.getMonthlyAnalytics();

        // Process products data for statistics
        if (productsResponse.success) {
          // The actual products array is in response.data
          // Ensure productData is always an array
          let productData = [];

          // Handle the case where response.data might not be an array directly
          if (Array.isArray(productsResponse.data)) {
            productData = productsResponse.data;
          } else {
            // First, try to handle the case where the data might be in response.data.data.content (paginated response)
            if (
              productsResponse.data &&
              productsResponse.data.data &&
              productsResponse.data.data.content &&
              Array.isArray(productsResponse.data.data.content)
            ) {
              productData = productsResponse.data.data.content;
            }
            // Then, try to handle the case where the data might be nested in response.data.data
            else if (
              productsResponse.data &&
              Array.isArray(productsResponse.data.data)
            ) {
              productData = productsResponse.data.data;
            } else {
              setStats({
                totalProducts: 0,
                totalStock: 0,
                averagePrice: 0,
                lowStockItems: 0,
                totalValue: 0,
              });
            }
          }

          if (productData.length > 0) {
            // Calculate statistics
            const totalProducts = productData.length;
            const totalStock = productData.reduce(
              (sum, product) => sum + (product.stockQuantity || 0),
              0
            );
            const totalValue = productData.reduce(
              (sum, product) =>
                sum +
                (product.sellingPrice || 0) * (product.stockQuantity || 0),
              0
            );
            const averagePrice =
              totalProducts > 0
                ? productData.reduce(
                    (sum, product) => sum + (product.sellingPrice || 0),
                    0
                  ) / totalProducts
                : 0;
            const lowStockItems = productData.filter(
              (product) => (product.stockQuantity || 0) < 10
            ).length;

            setStats({
              totalProducts,
              totalStock,
              averagePrice,
              lowStockItems,
              totalValue,
            });
          } else {
            setStats({
              totalProducts: 0,
              totalStock: 0,
              averagePrice: 0,
              lowStockItems: 0,
              totalValue: 0,
            });
          }
        } else {
          setStats({
            totalProducts: 0,
            totalStock: 0,
            averagePrice: 0,
            lowStockItems: 0,
            totalValue: 0,
          });
        }

        // Process analytics data for charts
        if (analyticsResponse.success && analyticsResponse.data) {
          console.log("Analytics data:", analyticsResponse.data);

          // Check if we have sales data
          if (
            analyticsResponse.data.sales &&
            Array.isArray(analyticsResponse.data.sales)
          ) {
            // Format the sales data for the chart
            const formattedSalesData = analyticsResponse.data.sales.map(
              (item) => ({
                name: item.name,
                sales: item.sales,
              })
            );
            setSalesData(formattedSalesData);
          } else {
            // Create empty sales data
            setSalesData(createEmptyMonthlyData());
          }

          // Check if we have revenue data
          if (
            analyticsResponse.data.revenue &&
            Array.isArray(analyticsResponse.data.revenue)
          ) {
            // Format the revenue data for the chart
            const formattedRevenueData = analyticsResponse.data.revenue.map(
              (item) => ({
                name: item.name,
                revenue: item.revenue,
              })
            );
            setRevenueData(formattedRevenueData);
          } else {
            // Create empty revenue data
            setRevenueData(createEmptyMonthlyData());
          }
        } else {
          // If no data is available, create empty data structures
          setSalesData(createEmptyMonthlyData());
          setRevenueData(createEmptyMonthlyData());
        }
      } catch (error) {
        console.error("Error fetching dashboard data:", error);

        // Reset stats
        setStats({
          totalProducts: 0,
          totalStock: 0,
          averagePrice: 0,
          lowStockItems: 0,
          totalValue: 0,
        });

        // Create empty data structures
        setSalesData(createEmptyMonthlyData());
        setRevenueData(createEmptyMonthlyData());
      } finally {
        setLoading(false);
      }
    };

    // Helper function to create empty monthly data
    const createEmptyMonthlyData = () => {
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();

      // Generate empty monthly data with proper month names
      return Array(12)
        .fill()
        .map((_, i) => ({
          name: new Date(currentYear, i, 1).toLocaleString("default", {
            month: "short",
          }),
          sales: 0,
          revenue: 0,
        }));
    };

    fetchData();
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
        <div>
          <h1 className="text-2xl font-bold text-slate-900">
            Supplier Dashboard
          </h1>
          <p className="text-slate-500 mt-1">
            Welcome back, {user?.firstName}! Here's an overview of your
            business.
          </p>
        </div>
        <Button
          className="mt-4 md:mt-0 bg-blue-600 hover:bg-blue-700"
          onClick={() => (window.location.href = "/supplier/products")}
        >
          <Package className="mr-2 h-4 w-4" />
          Manage Products
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card className="bg-gradient-to-br from-white to-blue-50/30 border-blue-100 hover:shadow-md transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-slate-600">
              Total Products
            </CardTitle>
            <div className="rounded-full bg-blue-50 p-2">
              <Package className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-slate-900">
              {stats.totalProducts}
            </div>
            <p className="text-xs text-slate-500 mt-1">
              Items in your inventory
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-white to-green-50/30 border-green-100 hover:shadow-md transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-slate-600">
              Total Stock
            </CardTitle>
            <div className="rounded-full bg-green-50 p-2">
              <ShoppingCart className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-slate-900">
              {stats.totalStock}
            </div>
            <p className="text-xs text-slate-500 mt-1">Units available</p>
          </CardContent>
        </Card>

        {/* <Card className="bg-gradient-to-br from-white to-amber-50/30 border-amber-100 hover:shadow-md transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-slate-600">
              Average Price
            </CardTitle>
            <div className="rounded-full bg-amber-50 p-2">
              <DollarSign className="h-4 w-4 text-amber-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col">
              <div className="text-2xl font-bold text-slate-900 tabular-nums tracking-tight">
                {formatCurrency(stats.averagePrice)}
              </div>
              <div className="h-1"></div>
              <div className="flex items-center">
                <p className="text-xs text-slate-500 mt-1">Per product</p>
              </div>
            </div>
          </CardContent>
        </Card> */}

        <Card className="bg-gradient-to-br from-white to-purple-50/30 border-purple-100 hover:shadow-md transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-slate-600">
              Inventory Value
            </CardTitle>
            <div className="rounded-full bg-purple-50 p-2">
              <Wallet className="h-4 w-4 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col">
              <div className="text-2xl font-bold text-slate-900 tabular-nums tracking-tight">
                {formatCurrency(stats.totalValue)}
              </div>
              <div className="h-1"></div>
              <div className="flex items-center">
                <p className="text-xs text-slate-500 mt-1">Total stock value</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Monthly Sales</span>
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            {!loading && (
              <ResponsiveContainer width="100%" height="100%">
                {salesData.length > 0 ? (
                  <BarChart
                    data={salesData}
                    margin={{ top: 10, right: 10, left: 0, bottom: 20 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis
                      dataKey="name"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: "#64748b" }}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: "#64748b" }}
                      tickFormatter={(value) => value.toLocaleString()}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend wrapperStyle={{ paddingTop: 10 }} />
                    <Bar
                      dataKey="sales"
                      name="Units Sold"
                      fill="#3b82f6"
                      radius={[4, 4, 0, 0]}
                      barSize={30}
                    />
                  </BarChart>
                ) : (
                  <div className="h-full w-full flex items-center justify-center">
                    <p className="text-slate-400">No sales data available</p>
                  </div>
                )}
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        <Card className="col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Monthly Revenue</span>
              <ArrowUpRight className="h-4 w-4 text-green-600" />
            </CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            {!loading && (
              <ResponsiveContainer width="100%" height="100%">
                {revenueData.length > 0 ? (
                  <AreaChart
                    data={revenueData}
                    margin={{ top: 10, right: 10, left: 0, bottom: 20 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis
                      dataKey="name"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: "#64748b" }}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: "#64748b" }}
                      tickFormatter={(value) =>
                        value >= 1000 ? `${(value / 1000).toFixed(0)}k` : value
                      }
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend wrapperStyle={{ paddingTop: 10 }} />
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      name="Revenue (MAD)"
                      stroke="#10b981"
                      fill="#10b981"
                      fillOpacity={0.2}
                      activeDot={{ r: 6 }}
                    />
                  </AreaChart>
                ) : (
                  <div className="h-full w-full flex items-center justify-center">
                    <p className="text-slate-400">No revenue data available</p>
                  </div>
                )}
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
