package com.storeflow.services;

import com.storeflow.DTOs.CatalogProductDTO;
import com.storeflow.enums.PurchaseStatus;
import com.storeflow.models.PurchaseItem;
import com.storeflow.repositories.PurchaseItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service for managing the product catalog available to clients.
 * This service aggregates purchased products by label and calculates average prices.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CatalogService {

    private final PurchaseItemRepository purchaseItemRepository;

    /**
     * Get all available products in the catalog with pagination.
     * Products are grouped by label and calculated from completed purchases.
     * Now includes products with zero available stock to show them as out-of-stock.
     *
     * @param pageable Pagination information
     * @return A page of catalog products
     */
    public Page<CatalogProductDTO> getAvailableProducts(Pageable pageable) {
        log.info("Fetching available products for catalog");

        // Get all purchase items from completed purchases (including those with zero stock)
        List<PurchaseItem> completedPurchaseItems = purchaseItemRepository.findByPurchaseStatus(PurchaseStatus.COMPLETED);

        log.info("Found {} completed purchase items", completedPurchaseItems.size());

        // Group by product label and aggregate data
        Map<String, List<PurchaseItem>> groupedByLabel = completedPurchaseItems.stream()
            .collect(Collectors.groupingBy(PurchaseItem::getProductLabel));

        // Convert to catalog products
        List<CatalogProductDTO> catalogProducts = groupedByLabel.entrySet().stream()
            .map(entry -> {
                String label = entry.getKey();
                List<PurchaseItem> items = entry.getValue();

                // Calculate average price
                BigDecimal averagePrice = items.stream()
                    .map(PurchaseItem::getUnitPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(BigDecimal.valueOf(items.size()), 2, RoundingMode.HALF_UP);

                // Calculate total available quantity
                Integer totalQuantity = items.stream()
                    .mapToInt(PurchaseItem::getAvailableQuantity)
                    .sum();

                // Get representative data (from first item)
                PurchaseItem representative = items.get(0);
                String description = representative.getProduct().getProductSuppliers().isEmpty() ?
                    "No description available" :
                    representative.getProduct().getProductSuppliers().get(0).getDescription();

                String imageUrl = representative.getProduct().getImageUrl();
                String brand = representative.getProduct().getBrand();

                // Calculate average delivery time from supplier products
                double avgDeliveryTime = items.stream()
                    .filter(item -> !item.getProduct().getProductSuppliers().isEmpty())
                    .mapToInt(item -> item.getProduct().getProductSuppliers().get(0).getDeliveryTime())
                    .average()
                    .orElse(7.0); // Default 7 days
                Integer averageDeliveryTime = (int) Math.round(avgDeliveryTime);

                return new CatalogProductDTO(
                    label,
                    description,
                    imageUrl,
                    brand,
                    averagePrice,
                    totalQuantity,
                    averageDeliveryTime
                );
            })
            .sorted((a, b) -> a.label().compareToIgnoreCase(b.label()))
            .collect(Collectors.toList());

        log.info("Generated {} catalog products", catalogProducts.size());

        // Apply pagination
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), catalogProducts.size());

        List<CatalogProductDTO> pageContent = catalogProducts.subList(start, end);

        return new PageImpl<>(pageContent, pageable, catalogProducts.size());
    }

    /**
     * Get a specific product from the catalog by label.
     * Now includes products with zero available stock to show them as out-of-stock.
     *
     * @param label The product label
     * @return The catalog product or null if not found
     */
    public CatalogProductDTO getProductByLabel(String label) {
        log.info("Fetching catalog product by label: {}", label);

        List<PurchaseItem> items = purchaseItemRepository.findByProductLabelAndPurchaseStatus(label, PurchaseStatus.COMPLETED);

        if (items.isEmpty()) {
            log.warn("No completed purchases found for product label: {}", label);
            return null;
        }

        // Calculate average price
        BigDecimal averagePrice = items.stream()
            .map(PurchaseItem::getUnitPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .divide(BigDecimal.valueOf(items.size()), 2, RoundingMode.HALF_UP);

        // Calculate total available quantity
        Integer totalQuantity = items.stream()
            .mapToInt(PurchaseItem::getAvailableQuantity)
            .sum();

        // Get representative data
        PurchaseItem representative = items.get(0);
        String description = representative.getProduct().getProductSuppliers().isEmpty() ?
            "No description available" :
            representative.getProduct().getProductSuppliers().get(0).getDescription();

        String imageUrl = representative.getProduct().getImageUrl();
        String brand = representative.getProduct().getBrand();

        // Calculate average delivery time
        double avgDeliveryTime = items.stream()
            .filter(item -> !item.getProduct().getProductSuppliers().isEmpty())
            .mapToInt(item -> item.getProduct().getProductSuppliers().get(0).getDeliveryTime())
            .average()
            .orElse(7.0);
        Integer averageDeliveryTime = (int) Math.round(avgDeliveryTime);

        return new CatalogProductDTO(
            label,
            description,
            imageUrl,
            brand,
            averagePrice,
            totalQuantity,
            averageDeliveryTime
        );
    }
}
