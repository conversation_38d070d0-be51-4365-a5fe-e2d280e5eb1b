import { authApi } from "@/context/auth-context";

/**
 * Service for supplier analytics-related API calls
 */
export const supplierAnalyticsService = {
  /**
   * Get monthly sales and revenue data for the authenticated supplier
   * @returns {Promise<Object>} - Promise resolving to API response with monthly data
   */
  getMonthlyAnalytics: async () => {
    try {
      // Call the real backend API endpoint
      const response = await authApi.get("/api/v1/analytics/supplier/monthly");

      return {
        success: response.data.success,
        message: response.data.message,
        data: response.data.data,
      };
    } catch (error) {
      return {
        success: false,
        message:
          error.response?.data?.message || "Failed to fetch analytics data",
        data: null,
      };
    }
  },

  /**
   * Get product performance analytics for the authenticated supplier
   * @returns {Promise<Object>} - Promise resolving to API response with product performance data
   */
  getProductPerformance: async () => {
    try {
      // Call the real backend API endpoint
      const response = await authApi.get("/api/v1/analytics/supplier/products");

      return {
        success: response.data.success,
        message: response.data.message,
        data: response.data.data,
      };
    } catch (error) {
      return {
        success: false,
        message:
          error.response?.data?.message ||
          "Failed to fetch product performance data",
        data: null,
      };
    }
  },
};

export default supplierAnalyticsService;
