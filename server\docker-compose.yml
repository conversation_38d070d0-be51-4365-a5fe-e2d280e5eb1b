services:
  db:
    container_name: postgres_storeflow
    image: postgres:latest
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
      PGDATA: /data/postgres
    volumes:
      - postgres_data:/data/postgres
    ports:
      - "${POSTGRES_LOCAL_PORT}:5432"
    networks:
      - storeflow_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 10s
      timeout: 5s
      retries: 5

  minio:
    container_name: minio_storeflow
    image: minio/minio:latest
    ports:
      - "${MINIO_API_PORT}:9000"
      - "${MINIO_CONSOLE_PORT}:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ACCESS_KEY}
      MINIO_ROOT_PASSWORD: ${MINIO_SECRET_KEY}
    volumes:
      - minio_data:/data
    networks:
      - storeflow_network
    command: server /data --console-address ":9001"
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

networks:
  storeflow_network:
    driver: bridge

volumes:
  postgres_data:
    name: storeflow_postgres_data
  minio_data:
    name: storeflow_minio_data