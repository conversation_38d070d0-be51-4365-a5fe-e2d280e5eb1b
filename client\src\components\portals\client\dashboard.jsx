import { useState, useEffect } from "react";
import {
  ShoppingCartIcon,
  PackageIcon,
  ClockIcon,
  CheckCircleIcon,
  EyeIcon,
} from "lucide-react";
import { orderService } from "@/services/order-service";
import { useAuth } from "@/context/auth-context";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { isDesktop, ripple } from "@/utils";
import { toast } from "sonner";

export const Dashboard = () => {
  const [stats, setStats] = useState({
    totalOrders: 0,
    pendingOrders: 0,
    completedOrders: 0,
    recentOrders: [],
  });
  const [loading, setLoading] = useState(true);

  // Get user data from Redux store (contains full profile info including firstName and profilePictureUrl)
  const userData = useSelector((state) => state.user.userData);
  const userLoading = useSelector((state) => state.user.loading);

  // Fallback to auth context for basic info if Redux data is not available
  const { user: authUser } = useAuth();
  const user = userData || authUser;

  const navigate = useNavigate();

  useEffect(() => {
    fetchDashboardData();
  }, []);

  // Debug user data
  useEffect(() => {
    console.log("Client Dashboard - Redux userData:", userData);
    console.log("Client Dashboard - Auth user:", authUser);
    console.log("Client Dashboard - Final user:", user);
  }, [userData, authUser, user]);

  // Check for registration success toast flag
  useEffect(() => {
    const showRegistrationToast = localStorage.getItem(
      "showRegistrationSuccessToast"
    );
    if (showRegistrationToast === "true") {
      // Show success toast
      toast.success("Registration successful, check your email box");
      // Clear the flag so it doesn't show again
      localStorage.removeItem("showRegistrationSuccessToast");
    }
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      // Fetch recent orders
      const response = await orderService.getMyOrders({
        page: 0,
        size: 5,
        sortBy: "orderDate",
        sortDir: "desc",
      });

      if (response.success) {
        const orders = response.data.content;
        setStats({
          totalOrders: response.data.totalElements,
          pendingOrders: orders.filter((order) => order.status === "PENDING")
            .length,
          completedOrders: orders.filter(
            (order) => order.status === "COMPLETED"
          ).length,
          recentOrders: orders,
        });
      }
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (loading || userLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Welcome Section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
        <div>
          <h1 className="text-2xl font-bold text-blue-gray-900">
            Welcome back, {user?.firstName || "Client"}!
          </h1>
          <p className="text-blue-gray-500 mt-1">
            Here's an overview of your orders and activities.
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total Orders Card */}
        <div className="bg-gradient-to-br from-white to-blue-50/30 border border-blue-100 hover:shadow-md transition-all duration-300 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-gray-600">
                Total Orders
              </p>
              <div className="text-3xl font-bold text-blue-gray-900">
                {stats.totalOrders}
              </div>
              <p className="text-xs text-blue-gray-500 mt-1">All your orders</p>
            </div>
            <div className="rounded-full bg-blue-50 p-3">
              <PackageIcon className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        {/* Pending Orders Card */}
        <div className="bg-gradient-to-br from-white to-amber-50/30 border border-amber-100 hover:shadow-md transition-all duration-300 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-gray-600">
                Pending Orders
              </p>
              <div className="text-3xl font-bold text-amber-600">
                {stats.pendingOrders}
              </div>
              <p className="text-xs text-blue-gray-500 mt-1">
                Awaiting processing
              </p>
            </div>
            <div className="rounded-full bg-amber-50 p-3">
              <ClockIcon className="h-6 w-6 text-amber-600" />
            </div>
          </div>
        </div>

        {/* Completed Orders Card */}
        <div className="bg-gradient-to-br from-white to-green-50/30 border border-green-100 hover:shadow-md transition-all duration-300 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-gray-600">
                Completed Orders
              </p>
              <div className="text-3xl font-bold text-green-600">
                {stats.completedOrders}
              </div>
              <p className="text-xs text-blue-gray-500 mt-1">
                Successfully delivered
              </p>
            </div>
            <div className="rounded-full bg-green-50 p-3">
              <CheckCircleIcon className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        {/* Quick Actions Card */}
        <div className="bg-gradient-to-br from-white to-blue-50/30 border border-blue-100 hover:shadow-md transition-all duration-300 rounded-xl p-6">
          <div className="flex flex-col items-center justify-center h-full space-y-4">
            <div className="rounded-full bg-blue-50 p-3">
              <ShoppingCartIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="text-center">
              <p className="text-sm font-medium text-blue-gray-600 mb-3">
                Quick Actions
              </p>
              <button
                onMouseDown={(event) =>
                  ripple.create(event, isDesktop() ? "light" : "dark")
                }
                onClick={() => navigate("/client/products")}
                className="group cursor-pointer text-sm text-white bg-blue-600 border border-blue-500 shadow-sm flex items-center justify-center gap-2 px-6 py-3 rounded-full font-medium transition-all duration-300 hover:bg-blue-700 hover:shadow-md active:shadow-sm w-full sm:w-auto"
                type="button"
              >
                <ShoppingCartIcon className="h-4 w-4 flex-shrink-0" />
                <span>SHOP NOW</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Orders */}
      <div className="bg-white rounded-xl shadow-sm border border-blue-gray-200 p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-lg font-semibold text-blue-gray-900">
            Recent Orders
          </h2>
          <button
            onMouseDown={(event) =>
              ripple.create(event, isDesktop() ? "light" : "dark")
            }
            onClick={() => navigate("/client/orders")}
            className="group cursor-pointer text-sm text-blue-gray-700 bg-white border border-blue-gray-300 shadow-sm flex items-center justify-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 hover:bg-blue-gray-50 hover:border-blue-gray-400"
            type="button"
          >
            <EyeIcon className="h-4 w-4 flex-shrink-0" />
            <span>VIEW ALL</span>
          </button>
        </div>

        {stats.recentOrders.length === 0 ? (
          <div className="text-center py-12">
            <div className="rounded-full bg-blue-gray-50 p-4 w-20 h-20 mx-auto mb-4 flex items-center justify-center">
              <PackageIcon className="h-10 w-10 text-blue-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-blue-gray-900 mb-2">
              No orders yet
            </h3>
            <p className="text-blue-gray-500 mb-6 max-w-sm mx-auto">
              Start shopping to see your orders here.
            </p>
            <button
              onMouseDown={(event) =>
                ripple.create(event, isDesktop() ? "light" : "dark")
              }
              onClick={() => navigate("/client/products")}
              className="group cursor-pointer text-sm text-white bg-blue-600 border border-blue-500 shadow-sm flex items-center justify-center gap-2 px-6 py-3 rounded-full font-medium transition-all duration-300 hover:bg-blue-700 hover:shadow-md active:shadow-sm mx-auto"
              type="button"
            >
              <ShoppingCartIcon className="h-4 w-4 flex-shrink-0" />
              <span>Browse Products</span>
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {stats.recentOrders.map((order) => (
              <div
                key={order.id}
                className="flex flex-col sm:flex-row sm:items-center justify-between p-4 border border-blue-gray-200 rounded-lg hover:bg-blue-gray-50 transition-all duration-300 hover:shadow-sm"
              >
                <div className="flex items-center gap-4 mb-3 sm:mb-0">
                  <div className="p-2 bg-blue-50 rounded-full flex-shrink-0">
                    <PackageIcon className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <h4 className="font-semibold text-blue-gray-900 mb-1">
                      Order #{order.id.slice(-8)}
                    </h4>
                    <p className="text-sm text-blue-gray-500">
                      {order.items.length} items • {formatDate(order.orderDate)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center justify-between sm:flex-col sm:items-end gap-2">
                  <div
                    className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                      order.status === "PENDING"
                        ? "bg-amber-100 text-amber-800"
                        : order.status === "IN_PROGRESS"
                        ? "bg-blue-100 text-blue-800"
                        : order.status === "COMPLETED"
                        ? "bg-green-100 text-green-800"
                        : "bg-blue-gray-100 text-blue-gray-800"
                    }`}
                  >
                    {order.status}
                  </div>
                  <p className="font-semibold text-blue-gray-900">
                    {order.totalAmount.toFixed(2)} MAD
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;
