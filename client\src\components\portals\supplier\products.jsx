import { useState, useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import { supplierProductService } from "@/services/supplier-product-service";
import { toast } from "sonner";
import {
  Package,
  Plus,
  Search,
  Edit,
  Trash,
  Loader2,
  Image as ImageIcon,
  Upload,
  X,
  Clock,
  TrendingUp,
  Eye,
  ArrowUpDown,
  RefreshCw,
  Sparkles,
  ShoppingBag,
  CircleDollarSign,
  FileText,
  BarChart3,
  DollarSign,
  MoreVertical,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ScrollableDialog as Dialog,
  ScrollableDialogContent as DialogContent,
  ScrollableDialogDescription as DialogDescription,
  ScrollableDialogFooter as DialogFooter,
  ScrollableDialogHeader as DialogHeader,
  ScrollableDialogTitle as DialogTitle,
} from "@/components/ui/scrollable-dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useFormik } from "formik";
import * as Yup from "yup";
import { Pagination } from "@/components/ui/pagination";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export const Products = () => {
  const { userData: user } = useSelector((state) => state.user);
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [activeProductTab, setActiveProductTab] = useState("details");
  const [loadingAction, setLoadingAction] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [sortBy, setSortBy] = useState(null);
  const [sortOrder, setSortOrder] = useState("asc");
  const [productStats, setProductStats] = useState({
    totalProducts: 0,
    totalValue: 0,
    lowStock: 0,
    highMargin: 0,
  });
  const itemsPerPage = 6;

  // Image upload related state
  const fileInputRef = useRef(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Product verification
  const verifyProductData = (data) => {
    if (!Array.isArray(data)) {
      console.error("Product data is not an array", data);
      return [];
    }

    return data.map((product) => ({
      ...product,
      productId:
        product.productId ||
        product.id ||
        Math.random().toString(36).substring(2, 15),
      label: product.label || "Untitled Product",
      description: product.description || "",
      stockQuantity: product.stockQuantity || 0,
      purchasePrice: product.purchasePrice || 0,
      sellingPrice: product.sellingPrice || 0,
      deliveryTime: product.deliveryTime || 1,
      margin:
        product.margin ||
        calculateMargin(product.purchasePrice || 0, product.sellingPrice || 0),
      imageUrl: product.imageUrl || null,
    }));
  };

  // Calculate margin if not provided
  const calculateMargin = (purchasePrice, sellingPrice) => {
    if (
      !purchasePrice ||
      purchasePrice === 0 ||
      !sellingPrice ||
      sellingPrice === 0
    )
      return 0;
    // Calculate margin using the same formula as the backend: (sellingPrice - purchasePrice) / sellingPrice * 100
    return (
      Math.round(((sellingPrice - purchasePrice) / sellingPrice) * 100 * 10) /
      10
    );
  };

  // Calculate product statistics
  const calculateProductStats = (productList) => {
    if (!Array.isArray(productList)) return;

    const stats = {
      totalProducts: productList.length,
      totalValue: productList.reduce(
        (sum, product) => sum + product.sellingPrice * product.stockQuantity,
        0
      ),
      lowStock: productList.filter((product) => product.stockQuantity < 5)
        .length,
      highMargin: productList.filter((product) => product.margin > 30).length,
    };

    setProductStats(stats);
  };

  // Fetch products
  const fetchProducts = async () => {
    try {
      setLoading(true);
      setIsRefreshing(true);

      // Check if user is authenticated and has supplier role
      if (!user) {
        console.error("No user data found");
        toast.error("Please log in to view your products");
        setProducts([]);
        setTotalPages(1);
        setLoading(false);
        setIsRefreshing(false);
        return;
      }

      if (user.role !== "SUPPLIER") {
        console.error("User is not a supplier:", user.role);
        toast.error("You don't have permission to access supplier products");
        setProducts([]);
        setTotalPages(1);
        setLoading(false);
        setIsRefreshing(false);
        return;
      }

      // Call the service to get products
      const response = await supplierProductService.getMyProducts();

      // The service now always returns a structured response
      if (response && response.success) {
        // Ensure productData is always an array and verify the data
        const productData = verifyProductData(
          Array.isArray(response.data) ? response.data : []
        );

        // Filter products by search query if provided
        const filteredProducts = searchQuery
          ? productData.filter(
              (product) =>
                product.label
                  ?.toLowerCase()
                  .includes(searchQuery.toLowerCase()) ||
                product.description
                  ?.toLowerCase()
                  .includes(searchQuery.toLowerCase())
            )
          : productData;

        // Sort products if sortBy is set
        const sortedProducts = sortBy
          ? [...filteredProducts].sort((a, b) => {
              const valueA = a[sortBy] || 0;
              const valueB = b[sortBy] || 0;

              if (typeof valueA === "string" && typeof valueB === "string") {
                return sortOrder === "asc"
                  ? valueA.localeCompare(valueB)
                  : valueB.localeCompare(valueA);
              }

              return sortOrder === "asc" ? valueA - valueB : valueB - valueA;
            })
          : filteredProducts;

        // Calculate product statistics
        calculateProductStats(sortedProducts);

        // Always set the products array
        setProducts(sortedProducts);

        // Check if we have pagination info from the backend
        if (response.pagination) {
          // Use the pagination info from the backend
          setTotalPages(response.pagination.totalPages || 1);
        } else {
          // Calculate pagination locally
          setTotalPages(
            Math.max(1, Math.ceil(sortedProducts.length / itemsPerPage))
          );
        }

        // No toast for successful fetch - removed as requested
      } else {
        // Set empty array for products
        setProducts([]);
        setTotalPages(1);

        // Show error message from the service
        if (response?.message) {
          toast.error(response.message);

          // If authentication error, redirect to login
          if (
            response.message.includes("session") ||
            response.message.includes("token") ||
            response.message.includes("log in")
          ) {
            setTimeout(() => {
              window.location.href = "/auth/login";
            }, 2000);
          }
        } else {
          toast.error("Failed to fetch products");
        }
      }
    } catch (error) {
      // This should rarely happen now since the service handles errors
      console.error("Unexpected error in fetchProducts:", error);
      toast.error("An unexpected error occurred while fetching products");

      // Set empty products array on error
      setProducts([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  // Load products when component mounts or when search/sort changes
  useEffect(() => {
    fetchProducts();
  }, [searchQuery, sortBy, sortOrder]);

  // Handle search
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle sort
  const handleSort = (field) => {
    if (sortBy === field) {
      // Toggle order if the same field is clicked
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      // Set new field and default to ascending
      setSortBy(field);
      setSortOrder("asc");
    }
    setCurrentPage(1); // Reset to first page when sorting
  };

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchProducts();
  };

  // Image upload handlers
  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    const validTypes = ["image/jpeg", "image/png", "image/jpg"];
    if (!validTypes.includes(file.type)) {
      toast.error("Please select a valid image file (JPEG, JPG, or PNG)");
      return;
    }

    // Validate file size (max 8MB)
    const maxSize = 8 * 1024 * 1024; // 8MB in bytes
    if (file.size > maxSize) {
      toast.error("Image size should be less than 8MB");
      return;
    }

    setSelectedImage(file);

    // Create preview
    const reader = new FileReader();
    reader.onloadend = () => {
      setImagePreview(reader.result);
    };
    reader.readAsDataURL(file);

    // Reset progress
    setUploadProgress(0);

    // Update the image field in the active formik form
    if (isAddDialogOpen) {
      addProductFormik.setFieldValue("image", file);
      // Clear any validation errors
      addProductFormik.setFieldError("image", undefined);
    } else if (isEditDialogOpen) {
      editProductFormik.setFieldValue("image", file);
      // Clear any validation errors
      editProductFormik.setFieldError("image", undefined);
    }
  };

  const handleRemoveImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setUploadProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }

    // Clear the image field in the active formik form
    if (isAddDialogOpen) {
      addProductFormik.setFieldValue("image", null);
      addProductFormik.setFieldValue("imageUrl", "");
    } else if (isEditDialogOpen) {
      editProductFormik.setFieldValue("image", null);
      // Don't clear imageUrl for edit form as it might have an existing image
    }
  };

  const uploadProductImage = async (file) => {
    if (!file) {
      console.warn("No file provided for upload");
      return null;
    }

    // Simulate upload progress
    const progressInterval = setInterval(() => {
      setUploadProgress((prev) => {
        const newProgress = prev + Math.random() * 15;
        return newProgress >= 100 ? 100 : newProgress;
      });
    }, 200);

    try {
      const response = await supplierProductService.uploadProductImage(file);

      if (!response || !response.success) {
        console.error(
          "Failed to upload image:",
          response?.message || "Unknown error"
        );
        toast.error(response?.message || "Failed to get image URL from server");
        return null;
      }

      setUploadProgress(100);
      // Return the image URL from the response
      return response.data || response;
    } catch (error) {
      console.error("Error uploading image:", error);
      toast.error("Failed to upload image. Please try again.");
      return null;
    } finally {
      clearInterval(progressInterval);
    }
  };

  // Get current page products
  const getCurrentPageProducts = () => {
    // Safety check to ensure products is an array
    if (!Array.isArray(products)) {
      console.warn("Products is not an array:", products);
      return [];
    }

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return products.slice(startIndex, endIndex);
  };

  // Product validation schema
  const ProductSchema = Yup.object().shape({
    // Fields that will be sent to the backend
    label: Yup.string().required("Product name is required"),
    brand: Yup.string().required("Brand is required"),
    description: Yup.string().required("Description is required"),
    stockQuantity: Yup.number()
      .required("Stock quantity is required")
      .min(1, "Stock must be at least 1"),
    purchasePrice: Yup.number()
      .required("Purchase price is required")
      .min(0, "Price cannot be negative"),
    sellingPrice: Yup.number()
      .required("Selling price is required")
      .min(0, "Price cannot be negative"),
    deliveryTime: Yup.number()
      .required("Delivery time is required")
      .min(1, "Delivery time must be at least 1 day"),
    imageUrl: Yup.string(),

    // Frontend-only field for image upload validation
    // This field is NOT sent to the backend
    image: Yup.mixed().test(
      "image-or-url-required",
      "Product image is required",
      function (value) {
        // If either image or imageUrl is provided, validation passes
        const { imageUrl } = this.parent;
        return (
          (value !== null && value !== undefined) ||
          (imageUrl && imageUrl.length > 0)
        );
      }
    ),
  });

  // Add product form
  const addProductFormik = useFormik({
    initialValues: {
      label: "",
      brand: "",
      description: "",
      stockQuantity: "",
      purchasePrice: "",
      sellingPrice: "",
      deliveryTime: "",
      imageUrl: "",
      image: null,
    },
    validationSchema: ProductSchema,
    onSubmit: async (values) => {
      try {
        setLoadingAction(true);

        // Upload image first if one is selected
        let imageUrl = null;
        if (selectedImage) {
          imageUrl = await uploadProductImage(selectedImage);
          if (!imageUrl) {
            toast.error(
              "Failed to upload image, but continuing with product creation"
            );
          }
        }

        // Combine brand and label
        const combinedLabel = values.brand
          ? `${values.brand} - ${values.label}`
          : values.label;

        // Create a new object with only the fields that the backend expects
        // Exclude the 'image' field which is only used for frontend validation
        const productData = {
          label: combinedLabel,
          brand: values.brand,
          description: values.description,
          stockQuantity: Number(values.stockQuantity),
          purchasePrice: Number(values.purchasePrice),
          sellingPrice: Number(values.sellingPrice),
          deliveryTime: Number(values.deliveryTime),
          imageUrl: imageUrl || values.imageUrl || null,
        };

        const response = await supplierProductService.addProduct(productData);

        if (response && response.success) {
          toast.success("Product added successfully");
          setIsAddDialogOpen(false);
          addProductFormik.resetForm();
          setSelectedImage(null);
          setImagePreview(null);
          setUploadProgress(0);

          // Fetch products with a longer delay to ensure the backend has processed the new product
          setTimeout(() => {
            fetchProducts();
          }, 1000);
        } else {
          toast.error(response?.message || "Failed to add product");

          // If authentication error, redirect to login
          if (
            response?.message &&
            (response.message.includes("session") ||
              response.message.includes("token") ||
              response.message.includes("log in"))
          ) {
            setTimeout(() => {
              window.location.href = "/auth/login";
            }, 2000);
          }
        }
      } catch (error) {
        console.error("Error adding product:", error);
        toast.error(error.response?.data?.message || "Failed to add product");
      } finally {
        setLoadingAction(false);
      }
    },
  });

  // Edit product form
  const editProductFormik = useFormik({
    initialValues: {
      id: "",
      label: "",
      brand: "",
      description: "",
      stockQuantity: "",
      purchasePrice: "",
      sellingPrice: "",
      deliveryTime: "",
      imageUrl: "",
      image: null,
    },
    validationSchema: ProductSchema,
    onSubmit: async (values) => {
      try {
        setLoadingAction(true);

        // Upload image first if one is selected
        let updatedImageUrl = values.imageUrl;
        if (selectedImage) {
          const uploadedImageUrl = await uploadProductImage(selectedImage);
          if (uploadedImageUrl) {
            updatedImageUrl = uploadedImageUrl;
          } else {
            toast.error("Failed to upload image, keeping existing image");
          }
        }

        // Combine brand and label
        const combinedLabel = values.brand
          ? `${values.brand} - ${values.label}`
          : values.label;

        // Create a new object with only the fields that the backend expects
        // Exclude the 'image' field which is only used for frontend validation
        const productData = {
          label: combinedLabel,
          brand: values.brand,
          description: values.description,
          stockQuantity: Number(values.stockQuantity),
          purchasePrice: Number(values.purchasePrice),
          sellingPrice: Number(values.sellingPrice),
          deliveryTime: Number(values.deliveryTime),
          imageUrl: updatedImageUrl || null,
        };

        const response = await supplierProductService.updateProduct(
          values.id,
          productData
        );

        if (response && response.success) {
          toast.success("Product updated successfully");
          setIsEditDialogOpen(false);
          setSelectedImage(null);
          setImagePreview(null);
          setUploadProgress(0);

          // Fetch products with a longer delay to ensure the backend has processed the update
          setTimeout(() => {
            fetchProducts();
          }, 1000);
        } else {
          toast.error(response?.message || "Failed to update product");

          // If authentication error, redirect to login
          if (
            response?.message &&
            (response.message.includes("session") ||
              response.message.includes("token") ||
              response.message.includes("log in"))
          ) {
            setTimeout(() => {
              window.location.href = "/auth/login";
            }, 2000);
          }
        }
      } catch (error) {
        console.error("Error updating product:", error);
        toast.error(
          error.response?.data?.message || "Failed to update product"
        );
      } finally {
        setLoadingAction(false);
      }
    },
  });

  // Handle edit product
  const handleEditProduct = (product) => {
    // Extract brand from label if it exists (format: "Brand - Product Name")
    let brand = "";
    let label = product.label;

    if (product.label && product.label.includes(" - ")) {
      const parts = product.label.split(" - ");
      brand = parts[0];
      label = parts.slice(1).join(" - ");
    }

    // Use productId instead of id - this is the correct property from the API
    editProductFormik.setValues({
      id: product.productId, // Use productId instead of id
      label: label,
      brand: brand,
      description: product.description,
      stockQuantity: product.stockQuantity.toString(),
      purchasePrice: product.purchasePrice.toString(),
      sellingPrice: product.sellingPrice.toString(),
      deliveryTime: product.deliveryTime.toString(),
      imageUrl: product.imageUrl || "",
      image: product.imageUrl ? "existing" : null, // Mark that we have an existing image
    });

    // Explicitly set the image field to satisfy validation
    if (product.imageUrl) {
      editProductFormik.setFieldValue("image", "existing");
    }
    setSelectedProduct(product);
    setImagePreview(product.imageUrl || null);
    setIsEditDialogOpen(true);
  };

  // Handle view product details
  const handleViewProductDetails = (product) => {
    setSelectedProduct(product);
    setActiveProductTab("details"); // Reset to details tab
    setIsDetailsDialogOpen(true);
  };

  // Handle delete product
  const handleDeleteProduct = (product) => {
    setSelectedProduct(product);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete product
  const confirmDeleteProduct = async () => {
    if (selectedProduct) {
      try {
        setLoadingAction(true);
        // Use productId instead of id - this is the correct property from the API
        const response = await supplierProductService.deleteProduct(
          selectedProduct.productId
        );

        if (response && response.success) {
          toast.success("Product deleted successfully");
          setIsDeleteDialogOpen(false);
          fetchProducts();
        } else {
          toast.error(response?.message || "Failed to delete product");

          // If authentication error, redirect to login
          if (
            response?.message &&
            (response.message.includes("session") ||
              response.message.includes("token") ||
              response.message.includes("log in"))
          ) {
            setTimeout(() => {
              window.location.href = "/auth/login";
            }, 2000);
          }
        }
      } catch (error) {
        console.error("Error deleting product:", error);
        toast.error("An unexpected error occurred while deleting the product");
      } finally {
        setLoadingAction(false);
      }
    }
  };

  // Calculate grid columns based on current screen width
  const getGridColumns = () => {
    if (typeof window !== "undefined") {
      const width = window.innerWidth;
      if (width < 768) return "grid-cols-1";
      if (width < 1024) return "grid-cols-2";
      return "grid-cols-3";
    }
    return "grid-cols-3";
  };

  // Determine stock status color
  const getStockStatusColor = (quantity) => {
    if (quantity <= 0) return "text-red-600 bg-red-50";
    if (quantity < 5) return "text-amber-600 bg-amber-50";
    if (quantity < 10) return "text-blue-600 bg-blue-50";
    return "text-green-600 bg-green-50";
  };

  // Determine margin status color
  const getMarginStatusColor = (margin) => {
    if (margin < 10) return "text-red-600 bg-red-50";
    if (margin < 20) return "text-amber-600 bg-amber-50";
    if (margin < 30) return "text-blue-600 bg-blue-50";
    return "text-green-600 bg-green-50";
  };

  // Format currency with better spacing and readability
  const formatCurrency = (amount) => {
    // Format with spaces as thousand separators and period as decimal separator
    const formattedNumber = Number(amount).toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

    // Replace commas with spaces for better readability
    return formattedNumber.replace(/,/g, " ") + " MAD";
  };

  // Product Loading Skeleton
  const ProductSkeleton = () => (
    <Card className="overflow-hidden border border-blue-gray-100 rounded-xl animate-pulse">
      {/* Image area skeleton */}
      <div className="w-full h-48 bg-blue-gray-50"></div>

      <div className="p-5">
        {/* Title skeleton */}
        <div className="mb-3">
          <div className="h-3 bg-blue-gray-100 rounded w-1/3 mb-2"></div>
          <div className="h-5 bg-blue-gray-100 rounded w-4/5"></div>
        </div>

        {/* Description skeleton */}
        <div className="h-10 space-y-1">
          <div className="h-3 bg-blue-gray-100 rounded w-full"></div>
          <div className="h-3 bg-blue-gray-100 rounded w-5/6"></div>
        </div>

        {/* Stats row skeleton */}
        <div className="flex items-center justify-between my-4">
          <div className="h-4 bg-blue-gray-100 rounded w-1/3"></div>
          <div className="h-6 bg-blue-gray-100 rounded-full w-1/4"></div>
        </div>

        {/* Price row skeleton */}
        <div className="border-t border-blue-gray-50 pt-4">
          <div className="flex justify-between">
            <div className="space-y-2">
              <div className="h-3 bg-blue-gray-100 rounded w-16"></div>
              <div className="h-4 bg-blue-gray-100 rounded w-24"></div>
            </div>
            <div className="w-24 h-9 bg-blue-gray-100 rounded"></div>
          </div>
        </div>
      </div>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Header with Dashboard Stats */}
      <div className="bg-white rounded-xl shadow-sm border border-blue-gray-100 p-5">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-5">
          <div>
            <h1 className="text-2xl font-bold text-blue-gray-800 flex items-center gap-2">
              <Package className="h-6 w-6 text-blue-gray-700" />
              Products Management
            </h1>
            <p className="text-blue-gray-600 mt-1">
              Manage your product inventory
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto mt-4 md:mt-0">
            {/* Add Product Button */}
            <Button
              className="bg-blue-gray-800 hover:bg-blue-gray-700 text-white flex items-center gap-1.5 shadow-sm"
              onClick={() => setIsAddDialogOpen(true)}
            >
              <Plus className="h-4 w-4" />
              Add Product
            </Button>

            {/* Refresh Button */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleRefresh}
                    disabled={isRefreshing}
                    className="h-10 w-10 border-blue-gray-200"
                  >
                    <RefreshCw
                      className={`h-4 w-4 text-blue-gray-700 ${
                        isRefreshing ? "animate-spin" : ""
                      }`}
                    />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Refresh products</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Sort Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="flex items-center gap-1.5 h-10 border-blue-gray-200"
                >
                  <ArrowUpDown className="h-4 w-4 text-blue-gray-700" />
                  <span className="text-blue-gray-700">Sort</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="border-blue-gray-100">
                <DropdownMenuLabel className="text-blue-gray-700">
                  Sort Products
                </DropdownMenuLabel>
                <DropdownMenuSeparator className="bg-blue-gray-100" />
                <DropdownMenuItem
                  onClick={() => handleSort("label")}
                  className="text-blue-gray-700 focus:text-blue-gray-700 focus:bg-blue-gray-50"
                >
                  By Name{" "}
                  {sortBy === "label" && (sortOrder === "asc" ? "↑" : "↓")}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleSort("sellingPrice")}
                  className="text-blue-gray-700 focus:text-blue-gray-700 focus:bg-blue-gray-50"
                >
                  By Price{" "}
                  {sortBy === "sellingPrice" &&
                    (sortOrder === "asc" ? "↑" : "↓")}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleSort("stockQuantity")}
                  className="text-blue-gray-700 focus:text-blue-gray-700 focus:bg-blue-gray-50"
                >
                  By Stock{" "}
                  {sortBy === "stockQuantity" &&
                    (sortOrder === "asc" ? "↑" : "↓")}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleSort("margin")}
                  className="text-blue-gray-700 focus:text-blue-gray-700 focus:bg-blue-gray-50"
                >
                  By Margin{" "}
                  {sortBy === "margin" && (sortOrder === "asc" ? "↑" : "↓")}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Search input */}
            <div className="relative w-full sm:w-64">
              <Input
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={handleSearch}
                className="pl-9 w-full h-10 border-blue-gray-200 focus-visible:ring-blue-gray-400/30"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-blue-gray-500" />
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="bg-gradient-to-br from-white to-blue-gray-50/80 border-blue-gray-100 hover-lift">
            <CardContent className="p-4 flex items-center">
              <div className="rounded-full bg-blue-gray-100 p-3 mr-4">
                <Package className="h-6 w-6 text-blue-gray-700" />
              </div>
              <div>
                <p className="text-sm font-medium text-blue-gray-600">
                  Total Products
                </p>
                <h3 className="text-2xl font-bold text-blue-gray-800">
                  {productStats.totalProducts}
                </h3>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-white to-blue-gray-50/80 border-blue-gray-100 hover-lift">
            <CardContent className="p-4 flex items-center">
              <div className="rounded-full bg-blue-gray-100 p-3 mr-4">
                <CircleDollarSign className="h-6 w-6 text-blue-gray-700" />
              </div>
              <div>
                <p className="text-sm font-medium text-blue-gray-600">
                  Inventory Value
                </p>
                <h3 className="text-2xl font-bold text-blue-gray-800">
                  {formatCurrency(productStats.totalValue).split(".")[0]}
                </h3>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-white to-blue-gray-50/80 border-blue-gray-100 hover-lift">
            <CardContent className="p-4 flex items-center">
              <div className="rounded-full bg-blue-gray-100 p-3 mr-4">
                <ShoppingBag className="h-6 w-6 text-blue-gray-700" />
              </div>
              <div>
                <p className="text-sm font-medium text-blue-gray-600">
                  Low Stock Items
                </p>
                <h3 className="text-2xl font-bold text-blue-gray-800">
                  {productStats.lowStock}
                </h3>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-white to-blue-gray-50/80 border-blue-gray-100 hover-lift">
            <CardContent className="p-4 flex items-center">
              <div className="rounded-full bg-blue-gray-100 p-3 mr-4">
                <BarChart3 className="h-6 w-6 text-blue-gray-700" />
              </div>
              <div>
                <p className="text-sm font-medium text-blue-gray-600">
                  High Margin
                </p>
                <h3 className="text-2xl font-bold text-blue-gray-800">
                  {productStats.highMargin}
                </h3>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Products Grid */}
      <div className={`grid ${getGridColumns()} gap-4`}>
        {loading ? (
          // Loading state with skeletons
          Array(itemsPerPage)
            .fill(0)
            .map((_, index) => <ProductSkeleton key={index} />)
        ) : getCurrentPageProducts().length > 0 ? (
          // Products list
          getCurrentPageProducts().map((product) => (
            <Card
              key={product.productId}
              className="overflow-hidden border border-blue-gray-100 rounded-xl hover:shadow-md transition-all duration-300 group hover-lift"
            >
              <div className="relative">
                {/* Stock Indicator Badge */}
                <div className="absolute top-3 left-3 z-10">
                  <Badge
                    className={`px-2 py-1 text-xs font-medium bg-white border border-blue-gray-100 text-blue-gray-700 shadow-sm`}
                  >
                    {product.stockQuantity <= 0
                      ? "Out of Stock"
                      : product.stockQuantity < 5
                      ? "Low Stock"
                      : `${product.stockQuantity} in stock`}
                  </Badge>
                </div>

                {/* Product Image - Large header image with white background */}
                <div className="w-full h-48 bg-white flex items-center justify-center overflow-hidden border-b border-blue-gray-50">
                  {product.imageUrl ? (
                    <img
                      src={product.imageUrl}
                      alt={product.label}
                      className="h-full w-full object-contain p-4"
                    />
                  ) : (
                    <div className="flex flex-col items-center justify-center">
                      <ImageIcon className="h-16 w-16 text-blue-gray-200" />
                      <p className="text-xs text-blue-gray-400 mt-2">
                        No image available
                      </p>
                    </div>
                  )}
                </div>
              </div>

              <div className="p-5">
                {/* Product Header */}
                <div className="mb-3">
                  {/* Product Title with Brand */}
                  {product.label && product.label.includes(" - ") ? (
                    <>
                      <div className="text-sm font-semibold text-blue-gray-600 mb-1">
                        {product.label.split(" - ")[0]}
                      </div>
                      <h3 className="text-lg font-bold text-blue-gray-800 group-hover:text-blue-gray-700 transition-colors truncate">
                        {product.label.split(" - ").slice(1).join(" - ")}
                      </h3>
                    </>
                  ) : (
                    <h3 className="text-lg font-bold text-blue-gray-800 group-hover:text-blue-gray-700 transition-colors truncate">
                      {product.label}
                    </h3>
                  )}
                </div>

                {/* Product Description - Truncated */}
                <p className="text-sm text-blue-gray-600 line-clamp-2 mb-4 h-10">
                  {product.description}
                </p>

                {/* Stats Row */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-1.5">
                    <Clock className="h-4 w-4 text-blue-gray-500" />
                    <span className="text-xs font-medium text-blue-gray-700">
                      {product.deliveryTime}{" "}
                      {product.deliveryTime === 1 ? "day" : "days"} delivery
                    </span>
                  </div>
                  <div className="flex items-center gap-1.5 bg-blue-gray-50 text-blue-gray-700 rounded-full py-1 px-2.5">
                    <TrendingUp className="h-3.5 w-3.5" />
                    <span className="text-xs font-semibold">
                      {product.margin.toFixed(1)}% margin
                    </span>
                  </div>
                </div>

                {/* Price Row - Redesigned with prices stacked vertically */}
                <div className="relative border-t border-blue-gray-50 pt-4">
                  {/* Prices in a vertical layout for better space management */}
                  <div className="flex flex-col gap-3 pr-12">
                    <div>
                      <div className="text-xs text-blue-gray-500">Purchase</div>
                      <div className="flex items-baseline">
                        <div className="font-medium text-blue-gray-800 tabular-nums">
                          {new Intl.NumberFormat("fr-MA", {
                            style: "decimal",
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                            useGrouping: true,
                          }).format(product.purchasePrice)}
                        </div>
                        <div className="text-blue-gray-500 text-xs ml-1.5">
                          MAD
                        </div>
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-blue-gray-500">Selling</div>
                      <div className="flex items-baseline">
                        <div className="font-semibold text-blue-gray-800 tabular-nums">
                          {new Intl.NumberFormat("fr-MA", {
                            style: "decimal",
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                            useGrouping: true,
                          }).format(product.sellingPrice)}
                        </div>
                        <div className="text-blue-gray-500 text-xs ml-1.5">
                          MAD
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Actions Dropdown - Positioned absolutely to avoid layout issues */}
                  <div className="absolute right-0 top-4">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 rounded-full hover:bg-blue-gray-50 cursor-pointer"
                        >
                          <MoreVertical className="h-4 w-4 text-blue-gray-700" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        align="end"
                        className="border-blue-gray-100"
                      >
                        <DropdownMenuItem
                          onClick={() => handleViewProductDetails(product)}
                          className="text-blue-gray-700 focus:text-blue-gray-700 focus:bg-blue-gray-50 cursor-pointer"
                        >
                          <Eye className="h-4 w-4 mr-2" /> View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleEditProduct(product)}
                          className="text-blue-gray-700 focus:text-blue-gray-700 focus:bg-blue-gray-50 cursor-pointer"
                        >
                          <Edit className="h-4 w-4 mr-2" /> Edit Product
                        </DropdownMenuItem>
                        <DropdownMenuSeparator className="bg-blue-gray-100" />
                        <DropdownMenuItem
                          onClick={() => handleDeleteProduct(product)}
                          className="text-red-600 focus:text-red-600 focus:bg-red-50 cursor-pointer"
                        >
                          <Trash className="h-4 w-4 mr-2" /> Delete Product
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            </Card>
          ))
        ) : (
          // Empty state
          <div className="col-span-1 md:col-span-2 lg:col-span-3 py-12 text-center">
            <div className="bg-white p-8 rounded-xl shadow-sm border border-blue-gray-100 max-w-md mx-auto">
              <div className="bg-blue-gray-50/60 rounded-full p-4 w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <Package className="h-10 w-10 text-blue-gray-700" />
              </div>
              <h3 className="text-xl font-medium text-blue-gray-800 mb-2">
                {searchQuery ? "No matching products" : "No products found"}
              </h3>
              <p className="text-blue-gray-600 mb-6">
                {searchQuery
                  ? "No products match your search criteria. Try a different search term or clear the search."
                  : "You haven't added any products to your inventory yet. Get started by adding your first product."}
              </p>
              {searchQuery ? (
                <Button
                  variant="outline"
                  className="mr-2 border-blue-gray-200 text-blue-gray-700 hover:bg-blue-gray-50"
                  onClick={() => setSearchQuery("")}
                >
                  Clear Search
                </Button>
              ) : (
                <Button
                  className="bg-blue-gray-800 hover:bg-blue-gray-700 text-white px-6 py-2 shadow-sm"
                  onClick={() => setIsAddDialogOpen(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Your First Product
                </Button>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Pagination */}
      {!loading && products.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-blue-gray-100 p-4 mt-6">
          <div className="flex justify-center">
            <Pagination
              currentPage={currentPage}
              totalPages={Math.max(1, totalPages)}
              onPageChange={handlePageChange}
            />
          </div>

          <div className="text-center text-blue-gray-500 mt-4 text-sm">
            Showing{" "}
            {Math.min(products.length, (currentPage - 1) * itemsPerPage + 1)} to{" "}
            {Math.min(products.length, currentPage * itemsPerPage)} of{" "}
            {products.length} products
          </div>
        </div>
      )}

      {/* Add Product Dialog */}
      <Dialog
        open={isAddDialogOpen}
        onOpenChange={(open) => {
          setIsAddDialogOpen(open);
          if (!open) {
            setSelectedImage(null);
            setImagePreview(null);
            setUploadProgress(0);
            addProductFormik.resetForm();
          } else {
            // Initialize form with empty values when opening
            addProductFormik.resetForm();
            // Ensure image fields are properly initialized
            addProductFormik.setFieldValue("image", null);
            addProductFormik.setFieldValue("imageUrl", "");
            // Clear any validation errors
            addProductFormik.setErrors({});
            addProductFormik.setTouched({});
          }
        }}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center text-blue-gray-800">
              <Sparkles className="h-5 w-5 text-blue-gray-700 mr-2" />
              Add New Product
            </DialogTitle>
            <DialogDescription className="text-blue-gray-600">
              Enter the details for your new product.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={addProductFormik.handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="brand" className="flex items-center">
                  Brand
                  <span className="text-red-500 -ml-1">*</span>
                </Label>
                <Input
                  id="brand"
                  name="brand"
                  placeholder="Enter product brand"
                  value={addProductFormik.values.brand}
                  onChange={addProductFormik.handleChange}
                  onBlur={addProductFormik.handleBlur}
                  className={
                    addProductFormik.touched.brand &&
                    addProductFormik.errors.brand
                      ? "border-red-300"
                      : ""
                  }
                />
                {addProductFormik.touched.brand &&
                  addProductFormik.errors.brand && (
                    <p className="text-sm text-red-500">
                      {addProductFormik.errors.brand}
                    </p>
                  )}
              </div>

              <div className="grid gap-2">
                <Label htmlFor="label" className="flex items-center">
                  Product Name
                  <span className="text-red-500 -ml-1">*</span>
                </Label>
                <Input
                  id="label"
                  name="label"
                  placeholder="Enter product name"
                  value={addProductFormik.values.label}
                  onChange={addProductFormik.handleChange}
                  onBlur={addProductFormik.handleBlur}
                  className={
                    addProductFormik.touched.label &&
                    addProductFormik.errors.label
                      ? "border-red-300"
                      : ""
                  }
                />
                {addProductFormik.touched.label &&
                  addProductFormik.errors.label && (
                    <p className="text-sm text-red-500">
                      {addProductFormik.errors.label}
                    </p>
                  )}
              </div>

              <div className="grid gap-2">
                <Label htmlFor="description" className="flex items-center">
                  Description
                  <span className="text-red-500 -ml-1">*</span>
                </Label>
                <Textarea
                  id="description"
                  name="description"
                  placeholder="Enter product description"
                  value={addProductFormik.values.description}
                  onChange={addProductFormik.handleChange}
                  onBlur={addProductFormik.handleBlur}
                  className={
                    addProductFormik.touched.description &&
                    addProductFormik.errors.description
                      ? "border-red-300"
                      : ""
                  }
                />
                {addProductFormik.touched.description &&
                  addProductFormik.errors.description && (
                    <p className="text-sm text-red-500">
                      {addProductFormik.errors.description}
                    </p>
                  )}
              </div>

              {/* Product Image Upload */}
              <div className="grid gap-2">
                <Label className="flex items-center">
                  Product Image
                  <span className="text-red-500 -ml-1">*</span>
                </Label>
                <div className="flex flex-col gap-4">
                  {/* Image Preview */}
                  <div
                    className={`border-2 border-dashed rounded-lg p-4 text-center cursor-pointer hover:bg-blue-50/50 transition-colors ${
                      imagePreview
                        ? "border-blue-200"
                        : addProductFormik.touched.image && !imagePreview
                        ? "border-red-300 bg-red-50/30"
                        : "border-blue-200"
                    }`}
                    onClick={handleImageClick}
                    style={{ maxHeight: "200px", overflow: "hidden" }}
                  >
                    {imagePreview ? (
                      <div className="relative">
                        <img
                          src={imagePreview}
                          alt="Product preview"
                          className="h-32 mx-auto object-contain rounded-md"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="icon"
                          className="absolute top-2 right-2 h-8 w-8 rounded-full bg-red-500 hover:bg-red-600"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveImage();
                          }}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center py-2">
                        <div className="bg-blue-100 rounded-full p-3 mb-2">
                          <Upload className="h-6 w-6 text-blue-600" />
                        </div>
                        <p className="text-sm text-blue-700">
                          Click to upload product image
                        </p>
                        <p className="text-xs text-blue-500">
                          JPEG, JPG or PNG (max 8MB)
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Error message for required image */}
                  {addProductFormik.touched.image &&
                    addProductFormik.errors.image &&
                    !imagePreview && (
                      <p className="text-sm text-red-500">
                        {addProductFormik.errors.image}
                      </p>
                    )}

                  {/* Progress Bar (only visible during upload) */}
                  {uploadProgress > 0 && uploadProgress < 100 && (
                    <div className="w-full bg-blue-100 rounded-full h-2.5">
                      <div
                        className="bg-blue-600 h-2.5 rounded-full"
                        style={{ width: `${uploadProgress}%` }}
                      ></div>
                    </div>
                  )}

                  {/* Hidden file input */}
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/jpeg,image/png,image/jpg"
                    className="hidden"
                    onChange={handleImageChange}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="stockQuantity" className="flex items-center">
                    Stock Quantity
                    <span className="text-red-500 -ml-1">*</span>
                  </Label>
                  <Input
                    id="stockQuantity"
                    name="stockQuantity"
                    type="number"
                    placeholder="0"
                    value={addProductFormik.values.stockQuantity}
                    onChange={addProductFormik.handleChange}
                    onBlur={addProductFormik.handleBlur}
                    className={
                      addProductFormik.touched.stockQuantity &&
                      addProductFormik.errors.stockQuantity
                        ? "border-red-300"
                        : ""
                    }
                  />
                  {addProductFormik.touched.stockQuantity &&
                    addProductFormik.errors.stockQuantity && (
                      <p className="text-sm text-red-500">
                        {addProductFormik.errors.stockQuantity}
                      </p>
                    )}
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="deliveryTime" className="flex items-center">
                    Delivery Time (days)
                    <span className="text-red-500 -ml-1">*</span>
                  </Label>
                  <Input
                    id="deliveryTime"
                    name="deliveryTime"
                    type="number"
                    placeholder="1"
                    value={addProductFormik.values.deliveryTime}
                    onChange={addProductFormik.handleChange}
                    onBlur={addProductFormik.handleBlur}
                    className={
                      addProductFormik.touched.deliveryTime &&
                      addProductFormik.errors.deliveryTime
                        ? "border-red-300"
                        : ""
                    }
                  />
                  {addProductFormik.touched.deliveryTime &&
                    addProductFormik.errors.deliveryTime && (
                      <p className="text-sm text-red-500">
                        {addProductFormik.errors.deliveryTime}
                      </p>
                    )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="purchasePrice" className="flex items-center">
                    Purchase Price (MAD)
                    <span className="text-red-500 -ml-1">*</span>
                  </Label>
                  <Input
                    id="purchasePrice"
                    name="purchasePrice"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={addProductFormik.values.purchasePrice}
                    onChange={addProductFormik.handleChange}
                    onBlur={addProductFormik.handleBlur}
                    className={
                      addProductFormik.touched.purchasePrice &&
                      addProductFormik.errors.purchasePrice
                        ? "border-red-300"
                        : ""
                    }
                  />
                  {addProductFormik.touched.purchasePrice &&
                    addProductFormik.errors.purchasePrice && (
                      <p className="text-sm text-red-500">
                        {addProductFormik.errors.purchasePrice}
                      </p>
                    )}
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="sellingPrice" className="flex items-center">
                    Selling Price (MAD)
                    <span className="text-red-500 -ml-1">*</span>
                  </Label>
                  <Input
                    id="sellingPrice"
                    name="sellingPrice"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={addProductFormik.values.sellingPrice}
                    onChange={addProductFormik.handleChange}
                    onBlur={addProductFormik.handleBlur}
                    className={
                      addProductFormik.touched.sellingPrice &&
                      addProductFormik.errors.sellingPrice
                        ? "border-red-300"
                        : ""
                    }
                  />
                  {addProductFormik.touched.sellingPrice &&
                    addProductFormik.errors.sellingPrice && (
                      <p className="text-sm text-red-500">
                        {addProductFormik.errors.sellingPrice}
                      </p>
                    )}
                </div>
              </div>

              {/* Calculated Margin Preview */}
              {addProductFormik.values.purchasePrice > 0 &&
                addProductFormik.values.sellingPrice > 0 && (
                  <div className="bg-blue-gray-50/70 p-3 rounded-lg border border-blue-gray-100">
                    <div className="flex items-center">
                      <TrendingUp className="h-4 w-4 text-blue-gray-700 mr-2" />
                      <span className="text-sm font-medium text-blue-gray-700">
                        Calculated Margin:{" "}
                      </span>
                      <span className="ml-1 text-sm font-bold text-blue-gray-800">
                        {(
                          ((addProductFormik.values.sellingPrice -
                            addProductFormik.values.purchasePrice) /
                            addProductFormik.values.sellingPrice) *
                          100
                        ).toFixed(2)}
                        %
                      </span>
                    </div>
                  </div>
                )}
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsAddDialogOpen(false);
                  setSelectedImage(null);
                  setImagePreview(null);
                  setUploadProgress(0);
                  addProductFormik.resetForm();
                }}
                className="border-blue-gray-200 text-blue-gray-700 hover:bg-blue-gray-50"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-blue-gray-800 hover:bg-blue-gray-700 text-white"
                disabled={loadingAction}
              >
                {loadingAction ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Product
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Product Dialog */}
      <Dialog
        open={isEditDialogOpen}
        onOpenChange={(open) => {
          setIsEditDialogOpen(open);
          if (!open) {
            setSelectedImage(null);
            setImagePreview(null);
            setUploadProgress(0);
            editProductFormik.resetForm();
          } else {
            // Clear any validation errors when opening the dialog
            // (but don't reset the form as it will be populated with product data)
            editProductFormik.setErrors({});
            editProductFormik.setTouched({});
          }
        }}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center text-blue-gray-800">
              <Edit className="h-5 w-5 text-blue-gray-700 mr-2" />
              Edit Product
            </DialogTitle>
            <DialogDescription className="text-blue-gray-600">
              Update the details for your product.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={editProductFormik.handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="brand" className="flex items-center">
                  Brand
                  <span className="text-red-500 ml-1">*</span>
                </Label>
                <Input
                  id="brand"
                  name="brand"
                  placeholder="Enter product brand"
                  value={editProductFormik.values.brand}
                  onChange={editProductFormik.handleChange}
                  onBlur={editProductFormik.handleBlur}
                  className={
                    editProductFormik.touched.brand &&
                    editProductFormik.errors.brand
                      ? "border-red-300"
                      : ""
                  }
                />
                {editProductFormik.touched.brand &&
                  editProductFormik.errors.brand && (
                    <p className="text-sm text-red-500">
                      {editProductFormik.errors.brand}
                    </p>
                  )}
              </div>

              <div className="grid gap-2">
                <Label htmlFor="label" className="flex items-center">
                  Product Name
                  <span className="text-red-500 ml-1">*</span>
                </Label>
                <Input
                  id="label"
                  name="label"
                  placeholder="Enter product name"
                  value={editProductFormik.values.label}
                  onChange={editProductFormik.handleChange}
                  onBlur={editProductFormik.handleBlur}
                  className={
                    editProductFormik.touched.label &&
                    editProductFormik.errors.label
                      ? "border-red-300"
                      : ""
                  }
                />
                {editProductFormik.touched.label &&
                  editProductFormik.errors.label && (
                    <p className="text-sm text-red-500">
                      {editProductFormik.errors.label}
                    </p>
                  )}
              </div>

              <div className="grid gap-2">
                <Label htmlFor="description" className="flex items-center">
                  Description
                  <span className="text-red-500 ml-1">*</span>
                </Label>
                <Textarea
                  id="description"
                  name="description"
                  placeholder="Enter product description"
                  value={editProductFormik.values.description}
                  onChange={editProductFormik.handleChange}
                  onBlur={editProductFormik.handleBlur}
                  className={
                    editProductFormik.touched.description &&
                    editProductFormik.errors.description
                      ? "border-red-300"
                      : ""
                  }
                />
                {editProductFormik.touched.description &&
                  editProductFormik.errors.description && (
                    <p className="text-sm text-red-500">
                      {editProductFormik.errors.description}
                    </p>
                  )}
              </div>

              {/* Product Image Upload */}
              <div className="grid gap-2">
                <Label className="flex items-center">
                  Product Image
                  <span className="text-red-500 ml-1">*</span>
                </Label>
                <div className="flex flex-col gap-4">
                  {/* Image Preview */}
                  <div
                    className={`border-2 border-dashed rounded-lg p-4 text-center cursor-pointer hover:bg-blue-50/50 transition-colors ${
                      imagePreview
                        ? "border-blue-200"
                        : editProductFormik.touched.image && !imagePreview
                        ? "border-red-300 bg-red-50/30"
                        : "border-blue-200"
                    }`}
                    onClick={handleImageClick}
                    style={{ maxHeight: "200px", overflow: "hidden" }}
                  >
                    {imagePreview ? (
                      <div className="relative">
                        <img
                          src={imagePreview}
                          alt="Product preview"
                          className="h-32 mx-auto object-contain rounded-md"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="icon"
                          className="absolute top-2 right-2 h-8 w-8 rounded-full bg-red-500 hover:bg-red-600"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveImage();
                          }}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center py-2">
                        <div className="bg-blue-100 rounded-full p-3 mb-2">
                          <Upload className="h-6 w-6 text-blue-600" />
                        </div>
                        <p className="text-sm text-blue-700">
                          Click to upload product image
                        </p>
                        <p className="text-xs text-blue-500">
                          JPEG, JPG or PNG (max 8MB)
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Error message for required image */}
                  {editProductFormik.touched.image &&
                    editProductFormik.errors.image &&
                    !imagePreview && (
                      <p className="text-sm text-red-500">
                        {editProductFormik.errors.image}
                      </p>
                    )}

                  {/* Progress Bar (only visible during upload) */}
                  {uploadProgress > 0 && uploadProgress < 100 && (
                    <div className="w-full bg-blue-100 rounded-full h-2.5">
                      <div
                        className="bg-blue-600 h-2.5 rounded-full"
                        style={{ width: `${uploadProgress}%` }}
                      ></div>
                    </div>
                  )}

                  {/* Hidden file input */}
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/jpeg,image/png,image/jpg"
                    className="hidden"
                    onChange={handleImageChange}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="stockQuantity" className="flex items-center">
                    Stock Quantity
                    <span className="text-red-500 -ml-1">*</span>
                  </Label>
                  <Input
                    id="stockQuantity"
                    name="stockQuantity"
                    type="number"
                    placeholder="0"
                    value={editProductFormik.values.stockQuantity}
                    onChange={editProductFormik.handleChange}
                    onBlur={editProductFormik.handleBlur}
                    className={
                      editProductFormik.touched.stockQuantity &&
                      editProductFormik.errors.stockQuantity
                        ? "border-red-300"
                        : ""
                    }
                  />
                  {editProductFormik.touched.stockQuantity &&
                    editProductFormik.errors.stockQuantity && (
                      <p className="text-sm text-red-500">
                        {editProductFormik.errors.stockQuantity}
                      </p>
                    )}
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="deliveryTime" className="flex items-center">
                    Delivery Time (days)
                    <span className="text-red-500 -ml-1">*</span>
                  </Label>
                  <Input
                    id="deliveryTime"
                    name="deliveryTime"
                    type="number"
                    placeholder="1"
                    value={editProductFormik.values.deliveryTime}
                    onChange={editProductFormik.handleChange}
                    onBlur={editProductFormik.handleBlur}
                    className={
                      editProductFormik.touched.deliveryTime &&
                      editProductFormik.errors.deliveryTime
                        ? "border-red-300"
                        : ""
                    }
                  />
                  {editProductFormik.touched.deliveryTime &&
                    editProductFormik.errors.deliveryTime && (
                      <p className="text-sm text-red-500">
                        {editProductFormik.errors.deliveryTime}
                      </p>
                    )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="purchasePrice" className="flex items-center">
                    Purchase Price (MAD)
                    <span className="text-red-500 -ml-1">*</span>
                  </Label>
                  <Input
                    id="purchasePrice"
                    name="purchasePrice"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={editProductFormik.values.purchasePrice}
                    onChange={editProductFormik.handleChange}
                    onBlur={editProductFormik.handleBlur}
                    className={
                      editProductFormik.touched.purchasePrice &&
                      editProductFormik.errors.purchasePrice
                        ? "border-red-300"
                        : ""
                    }
                  />
                  {editProductFormik.touched.purchasePrice &&
                    editProductFormik.errors.purchasePrice && (
                      <p className="text-sm text-red-500">
                        {editProductFormik.errors.purchasePrice}
                      </p>
                    )}
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="sellingPrice" className="flex items-center">
                    Selling Price (MAD)
                    <span className="text-red-500 -ml-1">*</span>
                  </Label>
                  <Input
                    id="sellingPrice"
                    name="sellingPrice"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={editProductFormik.values.sellingPrice}
                    onChange={editProductFormik.handleChange}
                    onBlur={editProductFormik.handleBlur}
                    className={
                      editProductFormik.touched.sellingPrice &&
                      editProductFormik.errors.sellingPrice
                        ? "border-red-300"
                        : ""
                    }
                  />
                  {editProductFormik.touched.sellingPrice &&
                    editProductFormik.errors.sellingPrice && (
                      <p className="text-sm text-red-500">
                        {editProductFormik.errors.sellingPrice}
                      </p>
                    )}
                </div>
              </div>

              {/* Calculated Margin Preview */}
              {editProductFormik.values.purchasePrice > 0 &&
                editProductFormik.values.sellingPrice > 0 && (
                  <div className="bg-blue-gray-50/70 p-3 rounded-lg border border-blue-gray-100">
                    <div className="flex items-center">
                      <TrendingUp className="h-4 w-4 text-blue-gray-700 mr-2" />
                      <span className="text-sm font-medium text-blue-gray-700">
                        Calculated Margin:{" "}
                      </span>
                      <span className="ml-1 text-sm font-bold text-blue-gray-800">
                        {(
                          ((editProductFormik.values.sellingPrice -
                            editProductFormik.values.purchasePrice) /
                            editProductFormik.values.sellingPrice) *
                          100
                        ).toFixed(2)}
                        %
                      </span>
                    </div>
                  </div>
                )}
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsEditDialogOpen(false)}
                className="border-blue-gray-200 text-blue-gray-700 hover:bg-blue-gray-50"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-blue-gray-800 hover:bg-blue-gray-700 text-white"
                disabled={loadingAction}
              >
                {loadingAction ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <Edit className="h-4 w-4 mr-2" />
                    Update Product
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Product Dialog - Using AlertDialog for destructive actions */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center text-red-600">
              <Trash className="h-5 w-5 mr-2" />
              Delete Product
            </AlertDialogTitle>
            <AlertDialogDescription className="text-blue-gray-600">
              Are you sure you want to delete this product? This action cannot
              be undone and will permanently remove the product from your
              inventory.
            </AlertDialogDescription>
          </AlertDialogHeader>

          {selectedProduct && (
            <div className="my-4 p-4 border border-blue-gray-100 rounded-lg bg-blue-gray-50/50">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-white rounded-md border border-blue-gray-100 flex items-center justify-center mr-3 overflow-hidden">
                  {selectedProduct.imageUrl ? (
                    <img
                      src={selectedProduct.imageUrl}
                      alt={selectedProduct.label}
                      className="h-full w-full object-contain"
                    />
                  ) : (
                    <ImageIcon className="h-6 w-6 text-blue-gray-200" />
                  )}
                </div>
                <div>
                  <h4 className="font-medium text-blue-gray-800">
                    {selectedProduct.label}
                  </h4>
                  <p className="text-xs text-blue-gray-500">
                    {selectedProduct.stockQuantity} in stock •{" "}
                    {formatCurrency(selectedProduct.sellingPrice)}
                  </p>
                </div>
              </div>
            </div>
          )}

          <AlertDialogFooter>
            <AlertDialogCancel className="border-blue-gray-200 text-blue-gray-700 hover:bg-blue-gray-50">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700 text-white"
              onClick={confirmDeleteProduct}
              disabled={loadingAction}
            >
              {loadingAction ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash className="h-4 w-4 mr-2" />
                  Delete Product
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Product Details Dialog */}
      {selectedProduct && (
        <Dialog
          open={isDetailsDialogOpen}
          onOpenChange={setIsDetailsDialogOpen}
        >
          <DialogContent className="sm:max-w-3xl p-0 overflow-hidden border-slate-200/80 shadow-lg rounded-xl">
            <div className="flex flex-col h-full">
              {/* Elegant header with product title */}
              <div className="bg-gradient-to-r from-blue-100/80 via-blue-50 to-slate-50 py-3 px-6 border-b border-slate-100 animate-gradient backdrop-blur-sm">
                <div className="flex items-center gap-3">
                  <div className="bg-blue-600/10 p-2 rounded-full shadow-sm ring-1 ring-blue-600/20 transition-all duration-300 hover:shadow-md hover:bg-blue-600/15 group">
                    <Package className="h-5 w-5 text-blue-600 group-hover:scale-110 transition-transform duration-300" />
                  </div>

                  <div className="flex-1">
                    {selectedProduct.label &&
                    selectedProduct.label.includes(" - ") ? (
                      <div className="flex flex-col">
                        <Badge
                          variant="secondary"
                          className="bg-blue-100 text-blue-700 hover:bg-blue-200 px-2.5 py-0.5 text-xs font-medium shadow-sm mb-1 w-fit"
                        >
                          {selectedProduct.label.split(" - ")[0]}
                        </Badge>
                        <DialogTitle className="text-xl font-bold text-slate-800 m-0 leading-tight">
                          {selectedProduct.label
                            .split(" - ")
                            .slice(1)
                            .join(" - ")}
                        </DialogTitle>
                      </div>
                    ) : (
                      <DialogTitle className="text-xl font-bold text-slate-800 m-0">
                        {selectedProduct.label}
                      </DialogTitle>
                    )}
                  </div>
                </div>
              </div>

              {/* Content area with premium style tabs */}
              <div className="flex-1 overflow-auto p-6 bg-gradient-to-b from-white to-slate-50/30">
                {/* Custom premium tabs */}
                <div className="mx-auto mb-6">
                  <div className="premium-tabs-container bg-gradient-to-r from-slate-50/90 to-blue-gray-50/80 backdrop-blur-sm hover:shadow-md transition-all duration-300">
                    <div className="text-lg grid grid-cols-3 gap-1">
                      <button
                        onClick={() => setActiveProductTab("details")}
                        className={`premium-tab-button ${
                          activeProductTab === "details" ? "active" : ""
                        }`}
                      >
                        <Package className="tab-icon text-blue-600" />
                        <span className="tab-text">Details</span>
                      </button>

                      <button
                        onClick={() => setActiveProductTab("pricing")}
                        className={`premium-tab-button ${
                          activeProductTab === "pricing" ? "active" : ""
                        }`}
                      >
                        <DollarSign className="tab-icon text-green-600" />
                        <span className="tab-text">Pricing</span>
                      </button>

                      <button
                        onClick={() => setActiveProductTab("inventory")}
                        className={`premium-tab-button ${
                          activeProductTab === "inventory" ? "active" : ""
                        }`}
                      >
                        <ShoppingBag className="tab-icon text-amber-600" />
                        <span className="tab-text">Inventory</span>
                      </button>
                    </div>
                  </div>
                </div>

                <Tabs
                  value={activeProductTab}
                  defaultValue="details"
                  className="hidden-tabs"
                  onValueChange={(value) => setActiveProductTab(value)}
                >
                  <TabsList>
                    <TabsTrigger id="details-tab" value="details">
                      Details
                    </TabsTrigger>
                    <TabsTrigger id="pricing-tab" value="pricing">
                      Pricing
                    </TabsTrigger>
                    <TabsTrigger id="inventory-tab" value="inventory">
                      Inventory
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="details" className="space-y-6">
                    {/* Product Header with Image */}
                    <div className="flex flex-col md:flex-row gap-6">
                      <div className="w-full md:w-2/5 bg-white rounded-xl border border-slate-200 p-6 flex items-center justify-center h-64 shadow-sm hover:shadow-md transition-all duration-300 group">
                        {selectedProduct.imageUrl ? (
                          <div className="relative w-full h-full flex items-center justify-center overflow-hidden">
                            <img
                              src={selectedProduct.imageUrl}
                              alt={selectedProduct.label}
                              className="max-h-full max-w-full object-contain transition-transform duration-500 group-hover:scale-105"
                            />
                          </div>
                        ) : (
                          <div className="flex flex-col items-center justify-center transition-all duration-300 group-hover:opacity-80">
                            <div className="bg-slate-50 p-4 rounded-full mb-2">
                              <ImageIcon className="h-20 w-20 text-slate-300" />
                            </div>
                            <p className="text-sm text-slate-400 mt-3 font-medium">
                              No image available
                            </p>
                          </div>
                        )}
                      </div>

                      <div className="w-full md:w-3/5">
                        <div className="flex flex-wrap gap-3 mb-6">
                          <Badge
                            className={`${getStockStatusColor(
                              selectedProduct.stockQuantity
                            )} px-3 py-1 text-sm`}
                          >
                            {selectedProduct.stockQuantity <= 0
                              ? "Out of Stock"
                              : selectedProduct.stockQuantity < 5
                              ? "Low Stock"
                              : `${selectedProduct.stockQuantity} in stock`}
                          </Badge>

                          <Badge
                            className={`${getMarginStatusColor(
                              selectedProduct.margin
                            )} px-3 py-1 text-sm`}
                          >
                            {selectedProduct.margin.toFixed(1)}% margin
                          </Badge>

                          <Badge className="bg-amber-100 text-amber-700 hover:bg-amber-100 px-3 py-1 text-sm">
                            {selectedProduct.deliveryTime}{" "}
                            {selectedProduct.deliveryTime === 1
                              ? "day"
                              : "days"}{" "}
                            delivery
                          </Badge>
                        </div>

                        {/* Price summary */}
                        <div className="grid grid-cols-2 gap-4 mb-6">
                          <div className="bg-slate-50 rounded-lg p-4">
                            <div className="text-sm text-slate-500 mb-1">
                              Purchase Price
                            </div>
                            <div className="text-xl font-bold">
                              {formatCurrency(selectedProduct.purchasePrice)}
                            </div>
                          </div>
                          <div className="bg-slate-50 rounded-lg p-4">
                            <div className="text-sm text-slate-500 mb-1">
                              Selling Price
                            </div>
                            <div className="text-xl font-bold text-blue-700">
                              {formatCurrency(selectedProduct.sellingPrice)}
                            </div>
                          </div>
                        </div>

                        {/* Product ID */}
                        <p className="text-sm text-slate-500">
                          Product ID: {selectedProduct.productId}
                        </p>
                      </div>
                    </div>

                    {/* Product Description */}
                    <div>
                      <h3 className="text-lg font-medium text-slate-900 mb-3 flex items-center">
                        <FileText className="h-5 w-5 mr-2 text-slate-500" />
                        Description
                      </h3>
                      <div className="text-base text-slate-700 bg-white p-5 rounded-lg border border-slate-200 leading-relaxed shadow-sm hover:shadow-md transition-all duration-300 hover:border-slate-300">
                        {selectedProduct.description || (
                          <span className="text-slate-400 italic">
                            No description provided
                          </span>
                        )}
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="pricing" className="space-y-4">
                    {/* Pricing Information */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card className="shadow-sm overflow-hidden hover:shadow-md transition-all duration-300 group border border-slate-200 hover:border-slate-300">
                        <div className="bg-gradient-to-r from-slate-50 to-white px-6 py-3 border-b border-slate-100 group-hover:from-slate-100 group-hover:to-slate-50 transition-all duration-300">
                          <CardTitle className="text-base text-slate-700 flex items-center">
                            <DollarSign className="h-4 w-4 mr-2 text-slate-500" />
                            Purchase Price
                          </CardTitle>
                        </div>
                        <CardContent className="pt-6 pb-5">
                          <div className="flex flex-col items-start">
                            <span className="text-sm text-slate-500 mb-1 font-medium">
                              Cost per unit
                            </span>
                            <p className="text-3xl font-bold text-slate-900 tracking-tight group-hover:scale-105 transition-transform duration-300">
                              {formatCurrency(selectedProduct.purchasePrice)}
                            </p>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="shadow-sm overflow-hidden hover:shadow-md transition-all duration-300 group border border-slate-200 hover:border-blue-200">
                        <div className="bg-gradient-to-r from-blue-50 to-white px-6 py-3 border-b border-blue-100 group-hover:from-blue-100 group-hover:to-blue-50 transition-all duration-300">
                          <CardTitle className="text-base text-blue-700 flex items-center">
                            <CircleDollarSign className="h-4 w-4 mr-2 text-blue-500" />
                            Selling Price
                          </CardTitle>
                        </div>
                        <CardContent className="pt-6 pb-5">
                          <div className="flex flex-col items-start">
                            <span className="text-sm text-slate-500 mb-1 font-medium">
                              Revenue per unit
                            </span>
                            <p className="text-3xl font-bold text-blue-700 tracking-tight group-hover:scale-105 transition-transform duration-300">
                              {formatCurrency(selectedProduct.sellingPrice)}
                            </p>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="shadow-sm overflow-hidden hover:shadow-md transition-all duration-300 group border border-slate-200 hover:border-green-200">
                        <div className="bg-gradient-to-r from-green-50 to-white px-6 py-3 border-b border-green-100 group-hover:from-green-100 group-hover:to-green-50 transition-all duration-300">
                          <CardTitle className="text-base text-green-700 flex items-center">
                            <TrendingUp className="h-4 w-4 mr-2 text-green-500" />
                            Profit Margin
                          </CardTitle>
                        </div>
                        <CardContent className="pt-6 pb-5">
                          <div className="flex flex-col">
                            <div className="flex items-center mb-3">
                              <p className="text-3xl font-bold text-green-600 mr-2 tracking-tight group-hover:scale-105 transition-transform duration-300">
                                {Math.round(selectedProduct.margin)}%
                              </p>
                              <div className="bg-green-100 p-1 rounded-full">
                                <TrendingUp className="h-5 w-5 text-green-600" />
                              </div>
                            </div>
                            <div className="flex flex-col">
                              <span className="text-sm text-slate-500 font-medium">
                                Profit per unit
                              </span>
                              <span className="text-lg font-semibold text-green-600 mt-1">
                                {formatCurrency(
                                  selectedProduct.sellingPrice -
                                    selectedProduct.purchasePrice
                                )}
                              </span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Price Breakdown */}
                    <Card className="shadow-sm overflow-hidden hover:shadow-md transition-all duration-300 group border border-slate-200 hover:border-slate-300">
                      <div className="bg-gradient-to-r from-slate-50 to-white px-6 py-4 border-b border-slate-100 group-hover:from-slate-100 group-hover:to-slate-50 transition-all duration-300">
                        <CardTitle className="text-lg text-slate-800 flex items-center">
                          <BarChart3 className="h-5 w-5 mr-2 text-slate-500" />
                          Price Breakdown
                        </CardTitle>
                      </div>
                      <CardContent className="pt-5 pb-6">
                        <div className="space-y-4">
                          <div className="flex justify-between items-center py-3 border-b border-slate-100 hover:bg-slate-50/50 px-2 rounded transition-colors duration-200">
                            <div className="flex items-center">
                              <span className="text-base text-slate-700 font-medium">
                                Purchase Price
                              </span>
                              <span className="ml-2 text-xs text-slate-500 bg-slate-100 px-2 py-1 rounded-full shadow-sm">
                                Cost
                              </span>
                            </div>
                            <span className="text-base font-medium text-slate-900">
                              {formatCurrency(selectedProduct.purchasePrice)}
                            </span>
                          </div>

                          <div className="flex justify-between items-center py-3 border-b border-slate-100 hover:bg-slate-50/50 px-2 rounded transition-colors duration-200">
                            <div className="flex items-center">
                              <span className="text-base text-slate-700 font-medium">
                                Markup
                              </span>
                              <span className="ml-2 text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full shadow-sm">
                                Profit
                              </span>
                            </div>
                            <span className="text-base font-medium text-green-600">
                              +{" "}
                              {formatCurrency(
                                selectedProduct.sellingPrice -
                                  selectedProduct.purchasePrice
                              )}
                            </span>
                          </div>

                          <div className="flex justify-between items-center py-4 bg-gradient-to-r from-blue-50 to-blue-50/30 px-4 rounded-lg mt-6 shadow-sm group-hover:from-blue-100/80 group-hover:to-blue-50/50 transition-all duration-300">
                            <span className="text-lg font-semibold text-slate-800 flex items-center">
                              <CircleDollarSign className="h-5 w-5 mr-2 text-blue-600" />
                              Selling Price
                            </span>
                            <span className="text-lg font-bold text-blue-700 group-hover:scale-105 transition-transform duration-300">
                              {formatCurrency(selectedProduct.sellingPrice)}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="inventory" className="space-y-6">
                    <Card className="shadow-sm">
                      <CardHeader>
                        <CardTitle className="text-lg">
                          Inventory Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-5">
                          <div className="flex justify-between items-center">
                            <span className="text-base text-slate-600">
                              Current Stock
                            </span>
                            <Badge
                              className={`${getStockStatusColor(
                                selectedProduct.stockQuantity
                              )} px-3 py-1 text-sm`}
                            >
                              {selectedProduct.stockQuantity} units
                            </Badge>
                          </div>

                          <div className="flex justify-between items-center">
                            <span className="text-base text-slate-600">
                              Delivery Time
                            </span>
                            <Badge className="bg-amber-100 text-amber-700 hover:bg-amber-100 px-3 py-1 text-sm">
                              {selectedProduct.deliveryTime}{" "}
                              {selectedProduct.deliveryTime === 1
                                ? "day"
                                : "days"}
                            </Badge>
                          </div>

                          <div className="flex justify-between items-center py-2 border-t border-slate-100">
                            <span className="text-base text-slate-600">
                              Total Value
                            </span>
                            <span className="text-base font-bold text-slate-900">
                              {formatCurrency(
                                selectedProduct.sellingPrice *
                                  selectedProduct.stockQuantity
                              )}
                            </span>
                          </div>

                          <div className="flex justify-between items-center">
                            <span className="text-base text-slate-600">
                              Cost of Inventory
                            </span>
                            <span className="text-base font-medium text-slate-900">
                              {formatCurrency(
                                selectedProduct.purchasePrice *
                                  selectedProduct.stockQuantity
                              )}
                            </span>
                          </div>

                          <div className="flex justify-between items-center">
                            <span className="text-base text-slate-600">
                              Potential Profit
                            </span>
                            <span className="text-base font-bold text-green-600">
                              {formatCurrency(
                                (selectedProduct.sellingPrice -
                                  selectedProduct.purchasePrice) *
                                  selectedProduct.stockQuantity
                              )}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Reorder Information */}
                    <Card className="shadow-sm border-blue-gray-100">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base text-blue-gray-800">
                          Reorder Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <span className="text-base text-blue-gray-600">
                              Suggested Reorder Point
                            </span>
                            <span className="text-base font-medium text-blue-gray-800">
                              5 units
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-base text-blue-gray-600">
                              Recommended Order Quantity
                            </span>
                            <span className="text-base font-medium text-blue-gray-800">
                              {Math.max(10, selectedProduct.stockQuantity * 2)}{" "}
                              units
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </div>

              {/* Dialog Footer */}
              <DialogFooter className="p-5 border-t border-blue-gray-100 bg-gradient-to-r from-blue-gray-50/60 to-white flex gap-3 shadow-inner">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDetailsDialogOpen(false)}
                  className="px-5 py-2 text-base hover:bg-blue-gray-50 transition-all duration-300 border-blue-gray-200 hover:border-blue-gray-300 text-blue-gray-700"
                >
                  <X className="h-4 w-4 mr-2" />
                  Close
                </Button>
                <Button
                  type="button"
                  className="bg-blue-gray-800 hover:bg-blue-gray-700 px-5 py-2 text-base shadow-sm hover:shadow-md transition-all duration-300 group"
                  onClick={() => {
                    setIsDetailsDialogOpen(false);
                    handleEditProduct(selectedProduct);
                  }}
                >
                  <Edit className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
                  Edit Product
                </Button>
              </DialogFooter>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default Products;
