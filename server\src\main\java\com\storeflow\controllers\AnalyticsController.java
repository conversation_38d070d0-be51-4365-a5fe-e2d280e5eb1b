package com.storeflow.controllers;

import com.storeflow.DTOs.MonthlyAnalyticsDTO;
import com.storeflow.responses.ApiResponse;
import com.storeflow.services.AnalyticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;
import java.util.Map;

/**
 * Controller for analytics-related endpoints
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/analytics")
public class AnalyticsController {

    private final AnalyticsService analyticsService;

    /**
     * Get monthly sales and revenue data for the authenticated supplier
     *
     * @param principal The authenticated user
     * @return Monthly analytics data
     */
    @GetMapping("/supplier/monthly")
    @PreAuthorize("hasRole('SUPPLIER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSupplierMonthlyAnalytics(Principal principal) {
        log.info("Received request to get monthly analytics for supplier");

        Map<String, Object> analyticsData = analyticsService.getSupplierMonthlyAnalytics(principal);

        return ResponseEntity.ok(new ApiResponse<>(
            true,
            "Monthly analytics data retrieved successfully",
            analyticsData
        ));
    }
}
