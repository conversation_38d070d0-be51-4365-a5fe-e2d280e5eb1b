package com.storeflow.services;

import com.storeflow.exception.*;
import com.storeflow.mappers.UserMapper;
import com.storeflow.requests.AuthenticationRequest;
import com.storeflow.responses.LoginResponse;
import com.storeflow.responses.RegisterRequest;
import com.storeflow.responses.RegistrationResponse;
import com.storeflow.enums.Role;
import com.storeflow.enums.TokenType;
import com.storeflow.models.Client;
import com.storeflow.models.Token;
import com.storeflow.models.User;
import com.storeflow.repositories.ClientRepository;
import com.storeflow.repositories.TokenRepository;
import com.storeflow.repositories.UserRepository;
import com.storeflow.utils.PhoneNumberFormatter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.Principal;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthenticationService {
    private final JwtService jwtService;
    private final UserMapper userMapper;
    private final FileService fileService;
    private final UserService userService;
    private final UserRepository userRepository;
    private final TokenRepository tokenRepository;
    private final PasswordEncoder passwordEncoder;
    private final ClientRepository clientRepository;
    private final PhoneNumberFormatter phoneNumberFormatter;
    private final AuthenticationManager authenticationManager;

    @Transactional
    public RegistrationResponse register(RegisterRequest request) {
        // Check if email already exists
        if (userRepository.existsByEmail(request.email())) {
            log.warn("Registration attempt with existing email: {}", request.email());
            throw new DuplicateResourceException("Email already exists");
        }

        // Check if phone number already exists
        String formattedPhoneNumber = phoneNumberFormatter.formatToInternational(request.phoneNumber());
        if (userRepository.existsByPhoneNumber(formattedPhoneNumber)) {
            log.warn("Registration attempt with existing phone number: {}", formattedPhoneNumber);
            throw new DuplicateResourceException("Phone number already exists");
        }

        // Check if password and confirmation password match
        if (!Objects.equals(request.password(), request.confirmationPassword())) {
            log.warn("Passwords do not match");
            throw new PasswordMismatchException("New password and confirmation do not match");
        }

        // Create new client with default profile picture -- base user
        User user = User.builder()
            .firstName(request.firstName().trim())
            .lastName(request.lastName().trim())
            .email(request.email())
            .phoneNumber(formattedPhoneNumber)
            .password(passwordEncoder.encode(request.password()))
            .role(Role.CLIENT)
            .profilePictureUrl(fileService.getDefaultProfilePictureUrl())
            .build();

        userRepository.save(user);

        Client client = Client.builder()
            .user(user)
            .build();

        clientRepository.save(client);

        log.info("New client registered successfully: {}", request.email());

        return RegistrationResponse.builder()
            .success(true)
            .message("Registration successful. Please login with your credentials.")
            .build();
    }

    public LoginResponse login(AuthenticationRequest request) {
        try {
            // This will throw an exception if authentication fails
            authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                    request.getEmail(),
                    request.getPassword()
                )
            );

            // If we get here, authentication was successful
            User user = userRepository.findByEmail(request.getEmail())
                .orElseThrow(() -> new AuthenticationFailedException("User not found"));

            log.info("User authenticated successfully: {}", request.getEmail());
            String jwtToken = jwtService.generateToken(user);
            String refreshToken = jwtService.generateRefreshToken(user);
            revokeAllUserTokens(user);
            saveUserToken(user, jwtToken);

            return LoginResponse.builder()
                .accessToken(jwtToken)
                .refreshToken(refreshToken)
                .build();

        } catch (BadCredentialsException e) {
            log.warn("Authentication failed - bad credentials for email: {}", request.getEmail());
            throw new AuthenticationFailedException("Invalid email or password", e);
        } catch (DisabledException e) {
            log.warn("Authentication failed - account disabled: {}", request.getEmail());
            throw new AuthenticationFailedException("Account is disabled", e);
        } catch (AuthenticationException e) {
            log.warn("Authentication failed: {} for email: {}", e.getMessage(), request.getEmail());
            throw new AuthenticationFailedException("Authentication failed", e);
        }
    }

    public LoginResponse refreshToken(String refreshToken) {
        if (refreshToken == null || refreshToken.isEmpty()) {
            throw new TokenRefreshException("Refresh token is missing");
        }

        // Extract user email from token
        String userEmail = jwtService.extractUsername(refreshToken);
        if (userEmail == null) {
            throw new TokenRefreshException("Invalid refresh token");
        }

        // Find user by email
        User user = userRepository.findByEmail(userEmail)
            .orElseThrow(() -> new TokenRefreshException("User not found for refresh token"));

        // Verify refresh token is valid
        if (!jwtService.isTokenValid(refreshToken, user)) {
            throw new TokenRefreshException("Refresh token is invalid or expired");
        }

        // Generate new tokens - both access AND refresh
        String accessToken = jwtService.generateToken(user);
        String newRefreshToken = jwtService.generateRefreshToken(user);

        // Revoke existing tokens
        revokeAllUserTokens(user);

        // Save new access token
        saveUserToken(user, accessToken);

        // Return response with new refresh token
        return LoginResponse.builder()
            .accessToken(accessToken)
            .refreshToken(newRefreshToken) // New refresh token each time
            .build();
    }

    /**
     * -- PUBLIC METHODS FOR TOKEN MANAGEMENT --
     */
    public void revokeAllUserTokens(User user) {
        List<Token> validUserTokens = tokenRepository.findAllValidTokensByUser(user.getId());
        if (validUserTokens.isEmpty()) return;
        validUserTokens.forEach(token -> {
            token.setExpired(true);
            token.setRevoked(true);
        });
        tokenRepository.saveAll(validUserTokens);
    }

    public void saveUserToken(User user, String jwtToken) {
        Token token = Token.builder()
            .user(user)
            .token(jwtToken)
            .tokenType(TokenType.BEARER)
            .revoked(false)
            .expired(false)
            .build();
        tokenRepository.save(token);
    }
}
