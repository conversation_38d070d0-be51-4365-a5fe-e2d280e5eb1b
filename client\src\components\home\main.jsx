export const Main = () => {
  return (
    <main id="main" className="relative flex flex-col justify-center min-h-dvh">
      <div className="absolute top-0 h-full w-full bg-[url('/img/background.png')] bg-cover bg-center"></div>
      <div className="absolute top-0 h-full w-full bg-black/70 bg-cover bg-center text-white"></div>
      <header className="container relative mx-auto">
        <div className="flex flex-wrap items-center">
          <div className="mx-auto w-full text-center">
            <p className="mb-3 font-satoshi font-black text-center text-white text-title px-4 lg:px-0">
              <span className="text-crimson">S</span>tore
              <span className="text-crimson">F</span>low
            </p>
            <p className="text-white opacity-80 text-subtitle px-5 lg:px-0 font-normal lg:font-light">
              Discover the innovative StoreFlow services, designed to simplify
              shopping, optimize orders and improve the user experience
              efficiency.
            </p>
          </div>
        </div>
      </header>
    </main>
  );
};

export default Main;
