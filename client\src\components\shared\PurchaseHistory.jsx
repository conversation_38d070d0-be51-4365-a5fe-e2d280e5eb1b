import { useState, useEffect } from "react";
import { toast } from "sonner";
import {
  ShoppingCart,
  Search,
  FileText,
  Calendar,
  ChevronLeft,
  ChevronRight,
  Loader2,
  Package,
  Filter,
  X,
  Eye,
} from "lucide-react";
import { purchaseService, supplierService } from "@/services";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { format } from "date-fns";
import { ripple, isDesktop } from "@/utils";

/**
 * PurchaseHistory component for viewing purchase history
 * This component can be used by both admin and employee roles
 * @param {boolean} showOnlyUserPurchases - If true, shows only current user's purchases (for employees)
 */
export const PurchaseHistory = ({ showOnlyUserPurchases = false }) => {
  // State for purchases data
  const [purchases, setPurchases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [searchQuery, setSearchQuery] = useState("");

  // State for filters
  const [suppliers, setSuppliers] = useState([]);
  const [selectedSupplier, setSelectedSupplier] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [isFilterDialogOpen, setIsFilterDialogOpen] = useState(false);

  // State for purchase details
  const [selectedPurchase, setSelectedPurchase] = useState(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);

  // Fetch purchases on component mount and when filters change
  useEffect(() => {
    fetchPurchases();
    fetchSuppliers();
  }, [currentPage, pageSize, selectedSupplier, selectedStatus]);

  // Fetch all purchases with pagination and filters
  const fetchPurchases = async () => {
    try {
      setLoading(true);

      // Prepare query parameters
      const params = {
        page: currentPage,
        size: pageSize,
        sortBy: "purchaseDate",
        sortDir: "desc",
      };

      // Add supplier filter if selected
      let response;
      if (selectedSupplier && selectedSupplier !== "all") {
        response = await purchaseService.getPurchasesBySupplier(
          selectedSupplier,
          params
        );
      } else {
        // Use appropriate endpoint based on user role
        if (showOnlyUserPurchases) {
          response = await purchaseService.getMyPurchases(params);
        } else {
          response = await purchaseService.getAllPurchases(params);
        }
      }

      if (response.success && response.data) {
        // Filter by status if selected
        let filteredPurchases = response.data.content;
        if (selectedStatus && selectedStatus !== "all") {
          filteredPurchases = filteredPurchases.filter(
            (purchase) => purchase.status === selectedStatus
          );
        }

        setPurchases(filteredPurchases);
        setTotalPages(response.data.totalPages);
        setTotalElements(response.data.totalElements);
      } else {
        toast.error(response.message || "Failed to fetch purchases");
        setPurchases([]);
      }
    } catch (error) {
      toast.error("Failed to fetch purchases");
      setPurchases([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch all suppliers for filtering
  const fetchSuppliers = async () => {
    try {
      const response = await supplierService.getAllSuppliers({
        page: 0,
        size: 100, // Get all suppliers
      });

      if (response.success && response.data) {
        setSuppliers(response.data.content);
      }
    } catch (error) {
      // Handle error silently
    }
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  // Handle search
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    // Reset to first page when searching
    setCurrentPage(0);
  };

  // Handle view purchase details
  const handleViewDetails = async (purchaseId) => {
    try {
      setLoading(true);
      const response = await purchaseService.getPurchaseById(purchaseId);

      if (response.success && response.data) {
        // The backend now includes imageUrl and brand directly in the PurchaseItemDTO
        // No need for complex mapping, but we'll ensure the properties exist
        const updatedItems = response.data.items.map((item) => {
          return {
            ...item,
            // Ensure imageUrl and brand are defined (even if null)
            imageUrl: item.imageUrl || null,
            brand: item.brand || null,
          };
        });

        setSelectedPurchase({
          ...response.data,
          items: updatedItems,
        });
        setIsDetailsDialogOpen(true);
      } else {
        toast.error(response.message || "Failed to fetch purchase details");
      }
    } catch (error) {
      toast.error("Failed to fetch purchase details");
    } finally {
      setLoading(false);
    }
  };

  // Handle filter reset
  const handleResetFilters = () => {
    setSelectedSupplier("all");
    setSelectedStatus("all");
    setIsFilterDialogOpen(false);
    setCurrentPage(0);
  };

  // Format purchase status for display
  const formatStatus = (status) => {
    switch (status) {
      case "PENDING":
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case "COMPLETED":
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case "CANCELLED":
        return <Badge className="bg-red-100 text-red-800">Cancelled</Badge>;
      default:
        return <Badge className="bg-blue-gray-100 text-blue-gray-800">{status}</Badge>;
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return format(date, "MMM dd, yyyy HH:mm");
    } catch (error) {
      return dateString;
    }
  };

  return (
    <div className="space-y-4">
      {/* Page header with title and search */}
      <div className="bg-white rounded-xl shadow-sm border border-blue-gray-200 p-4 sm:p-6">
        <div className="flex flex-col gap-4">
          {/* Title Section */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div className="flex-1 min-w-0">
              <h1 className="text-xl sm:text-2xl font-bold text-blue-gray-900 flex items-center gap-2">
                <ShoppingCart className="h-5 w-5 sm:h-6 sm:w-6 text-blue-gray-700 flex-shrink-0" />
                <span className="truncate">Purchase History</span>
              </h1>
              <p className="text-blue-gray-500 mt-1 text-sm sm:text-base">
                View and manage purchases from suppliers
              </p>
            </div>
          </div>

          {/* Search and Filter Section */}
          <div className="flex flex-col sm:flex-row gap-3">
            {/* Custom Search Input */}
            <div className="flex-1 sm:max-w-sm">
              <div className="flex items-center w-full border border-blue-gray-500 rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px]">
                <Search className="ml-3 h-5 w-5 lg:h-6 lg:w-6 text-blue-gray-500 flex-shrink-0" />
                <input
                  type="search"
                  placeholder="Search purchases..."
                  className="flex-1 h-full text-base focus:outline-0 transition-all placeholder:text-blue-gray-500 rounded-xl pr-3 pl-2 lg:text-lg bg-transparent"
                  value={searchQuery}
                  onChange={handleSearch}
                />
              </div>
            </div>

            {/* Filter Button */}
            <button
              onMouseDown={(event) =>
                ripple.create(event, isDesktop() ? "light" : "dark")
              }
              onClick={() => setIsFilterDialogOpen(true)}
              className="group cursor-pointer text-sm text-blue-gray-700 bg-white border border-blue-gray-300 flex items-center justify-center gap-2 px-4 py-3 rounded-full font-medium transition-all duration-300 hover:bg-blue-gray-50 hover:border-blue-gray-400 h-[53px]"
              type="button"
            >
              <Filter className="h-4 w-4 flex-shrink-0" />
              <span>Filter</span>
            </button>
          </div>
        </div>
      </div>

      {/* Purchases table */}
      <Card className="border border-blue-gray-200">
        <CardHeader className="pb-3">
          <CardTitle>Purchases</CardTitle>
          <CardDescription>
            {totalElements} total purchases found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600 mr-2" />
              <span>Loading purchases...</span>
            </div>
          ) : purchases.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8">
              <Package className="h-12 w-12 text-blue-gray-300 mb-3" />
              <p className="text-blue-gray-500 text-lg">No purchases found</p>
              {(selectedSupplier !== "all" || selectedStatus !== "all") && (
                <button
                  onMouseDown={(event) =>
                    ripple.create(event, isDesktop() ? "light" : "dark")
                  }
                  onClick={handleResetFilters}
                  className="mt-2 text-blue-gray-600 hover:text-blue-gray-800 underline transition-all duration-300"
                  type="button"
                >
                  Reset filters
                </button>
              )}
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {purchases.map((purchase) => (
                  <Card
                    key={purchase.id}
                    className="overflow-hidden border-blue-gray-200 rounded-xl hover:shadow-md transition-all duration-300 cursor-pointer"
                    onClick={() => handleViewDetails(purchase.id)}
                  >
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-base font-medium">
                          Purchase #{purchase.id.substring(0, 6)}
                        </CardTitle>
                        {formatStatus(purchase.status)}
                      </div>
                      <CardDescription>
                        {formatDate(purchase.purchaseDate)}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="pb-0">
                      <div className="flex flex-col gap-2">
                        <div className="flex items-center gap-2">
                          <ShoppingCart className="h-4 w-4 text-blue-gray-400" />
                          <span className="text-blue-gray-700">
                            {purchase.supplierName}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-blue-gray-400" />
                          <span className="text-blue-gray-700 font-medium">
                            {purchase.totalAmount.toFixed(2)} MAD
                          </span>
                        </div>
                        <div className="flex justify-end">
                          <button
                            onMouseDown={(event) =>
                              ripple.create(event, "dark")
                            }
                            className="text-blue-gray-600 hover:text-blue-gray-800 p-2 rounded-lg hover:bg-blue-gray-50 transition-all duration-300 flex items-center gap-1 text-sm font-medium"
                            type="button"
                          >
                            <Eye className="h-4 w-4" />
                            View Details
                          </button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="bg-white rounded-xl shadow-sm border border-blue-gray-200 p-4 mt-6">
                  <div className="flex justify-center items-center gap-2">
                    <button
                      onMouseDown={(event) =>
                        ripple.create(event, isDesktop() ? "light" : "dark")
                      }
                      disabled={currentPage === 0}
                      onClick={() => handlePageChange(currentPage - 1)}
                      className={`flex items-center gap-1 px-3 py-2 rounded-lg border transition-all duration-300 ${
                        currentPage === 0
                          ? "border-blue-gray-200 text-blue-gray-400 cursor-not-allowed"
                          : "border-blue-gray-300 text-blue-gray-700 hover:bg-blue-gray-50 hover:border-blue-gray-400"
                      }`}
                      type="button"
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </button>

                    <div className="flex gap-2">
                      {Array.from(
                        { length: Math.min(totalPages, 5) },
                        (_, i) => {
                          let pageNum;
                          if (totalPages <= 5) {
                            pageNum = i;
                          } else if (currentPage < 3) {
                            pageNum = i;
                          } else if (currentPage >= totalPages - 3) {
                            pageNum = totalPages - 5 + i;
                          } else {
                            pageNum = currentPage - 2 + i;
                          }

                          return (
                            <button
                              key={pageNum}
                              onMouseDown={(event) =>
                                ripple.create(
                                  event,
                                  isDesktop() ? "light" : "dark"
                                )
                              }
                              onClick={() => handlePageChange(pageNum)}
                              className={`w-9 h-9 p-0 rounded-lg transition-all duration-300 ${
                                currentPage === pageNum
                                  ? "bg-blue-gray-900 text-white border border-blue-gray-800"
                                  : "border border-blue-gray-300 text-blue-gray-700 hover:bg-blue-gray-50 hover:border-blue-gray-400"
                              }`}
                              type="button"
                            >
                              {pageNum + 1}
                            </button>
                          );
                        }
                      )}
                    </div>

                    <button
                      onMouseDown={(event) =>
                        ripple.create(event, isDesktop() ? "light" : "dark")
                      }
                      disabled={currentPage === totalPages - 1}
                      onClick={() => handlePageChange(currentPage + 1)}
                      className={`flex items-center gap-1 px-3 py-2 rounded-lg border transition-all duration-300 ${
                        currentPage === totalPages - 1
                          ? "border-blue-gray-200 text-blue-gray-400 cursor-not-allowed"
                          : "border-blue-gray-300 text-blue-gray-700 hover:bg-blue-gray-50 hover:border-blue-gray-400"
                      }`}
                      type="button"
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </button>
                  </div>

                  <div className="text-center text-blue-gray-500 mt-4 text-sm">
                    Showing page {currentPage + 1} of {totalPages}
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Filter dialog */}
      <Dialog open={isFilterDialogOpen} onOpenChange={setIsFilterDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Filter Purchases</DialogTitle>
            <DialogDescription>
              Filter purchases by supplier and status
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-blue-gray-700">
                Supplier
              </label>
              <Select
                value={selectedSupplier}
                onValueChange={setSelectedSupplier}
              >
                <SelectTrigger className="w-full bg-white border-blue-gray-200">
                  <SelectValue placeholder="All Suppliers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Suppliers</SelectItem>
                  {suppliers.map((supplier) => (
                    <SelectItem key={supplier.id} value={supplier.id}>
                      {supplier.firstName} {supplier.lastName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-blue-gray-700">
                Status
              </label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-full bg-white border-blue-gray-200">
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter className="flex flex-col sm:flex-row gap-3 sm:justify-between sm:space-x-0">
            <button
              onMouseDown={(event) =>
                ripple.create(event, isDesktop() ? "light" : "dark")
              }
              onClick={handleResetFilters}
              className="flex-1 py-3 px-4 text-blue-gray-700 bg-white border border-blue-gray-300 rounded-lg hover:bg-blue-gray-50 hover:border-blue-gray-400 transition-all duration-300 font-medium"
              type="button"
            >
              Reset Filters
            </button>
            <button
              onMouseDown={(event) =>
                ripple.create(event, isDesktop() ? "light" : "dark")
              }
              onClick={() => setIsFilterDialogOpen(false)}
              className="flex-1 py-3 px-4 bg-blue-gray-900 text-white rounded-lg hover:bg-blue-gray-800 hover:shadow-md transition-all duration-300 font-medium"
              type="button"
            >
              Apply Filters
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Purchase details dialog */}
      {selectedPurchase && (
        <Dialog
          open={isDetailsDialogOpen}
          onOpenChange={setIsDetailsDialogOpen}
        >
          <DialogContent className="sm:max-w-xl max-h-[90vh]">
            <div className="overflow-y-auto pr-1 -mr-1 max-h-[calc(90vh-7rem)] scrollbar-thin scrollbar-thumb-blue-400 hover:scrollbar-thumb-blue-500 scrollbar-track-transparent scrollbar-thumb-rounded-md">
              <DialogHeader>
                <DialogTitle>Purchase Details</DialogTitle>
                <DialogDescription>
                  Purchase ID:{" "}
                  <span className="font-mono bg-blue-gray-100 px-1 py-0.5 rounded text-xs">
                    {selectedPurchase.id}
                  </span>
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-6 py-4">
                <div className="space-y-6">
                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <p className="text-sm font-medium text-blue-gray-700">
                        Supplier
                      </p>
                      <p className="text-blue-gray-900 mt-1">
                        {selectedPurchase.supplierName}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-blue-gray-700">Date</p>
                      <p className="text-blue-gray-900 mt-1">
                        {formatDate(selectedPurchase.purchaseDate)}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <p className="text-sm font-medium text-blue-gray-700">
                        Status
                      </p>
                      <div className="mt-1">
                        {formatStatus(selectedPurchase.status)}
                      </div>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-blue-gray-700">
                        Total
                      </p>
                      <p className="text-blue-gray-900 font-semibold mt-1">
                        {selectedPurchase.totalAmount.toFixed(2)} MAD
                      </p>
                    </div>
                  </div>
                </div>

                {selectedPurchase.notes && (
                  <div>
                    <p className="text-sm font-medium text-blue-gray-700">Notes</p>
                    <p className="text-blue-gray-900">{selectedPurchase.notes}</p>
                  </div>
                )}

                <div>
                  <p className="text-sm font-medium text-blue-gray-700 mb-3">
                    Items
                  </p>
                  <div className="space-y-4">
                    {selectedPurchase.items.map((item) => (
                      <Card
                        key={item.id}
                        className="overflow-hidden border-blue-gray-200"
                      >
                        <div className="flex flex-col sm:flex-row">
                          {/* Product Image */}
                          <div className="w-full sm:w-1/4 h-32 bg-white flex items-center justify-center p-2 border-b sm:border-b-0 sm:border-r border-blue-gray-100">
                            {item.imageUrl ? (
                              <img
                                src={item.imageUrl}
                                alt={item.productLabel}
                                className="max-h-full max-w-full object-contain"
                                onError={(e) => {
                                  e.target.onerror = null;
                                  // If image fails to load, show the no image icon
                                  e.target.style.display = "none";
                                  // Replace with a no-image placeholder
                                  e.target.parentNode.innerHTML = `
                                    <div class="flex flex-col items-center justify-center text-blue-gray-300">
                                      <svg class="h-16 w-16 text-blue-gray-200" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect width="24" height="24" fill="white" />
                                        <path d="M19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                        <path d="M8.5 10C9.32843 10 10 9.32843 10 8.5C10 7.67157 9.32843 7 8.5 7C7.67157 7 7 7.67157 7 8.5C7 9.32843 7.67157 10 8.5 10Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                        <path d="M21 15L16 10L5 21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                      </svg>
                                      <span class="text-xs text-blue-gray-400 mt-1">No image</span>
                                    </div>
                                  `;
                                }}
                              />
                            ) : (
                              <div className="flex flex-col items-center justify-center text-blue-gray-300">
                                <svg
                                  className="h-16 w-16 text-blue-gray-200"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <rect width="24" height="24" fill="white" />
                                  <path
                                    d="M19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3Z"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                  <path
                                    d="M8.5 10C9.32843 10 10 9.32843 10 8.5C10 7.67157 9.32843 7 8.5 7C7.67157 7 7 7.67157 7 8.5C7 9.32843 7.67157 10 8.5 10Z"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                  <path
                                    d="M21 15L16 10L5 21"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                </svg>
                                <span className="text-xs text-blue-gray-400 mt-1">
                                  No image
                                </span>
                              </div>
                            )}
                          </div>

                          {/* Product Details */}
                          <div className="w-full sm:w-3/4 p-4">
                            <div className="flex flex-col h-full justify-between">
                              <div>
                                <h4 className="font-medium text-blue-gray-900 mb-1">
                                  {item.brand && `${item.brand} - `}
                                  {item.productLabel}
                                </h4>
                              </div>
                              <div className="mt-4">
                                <div className="flex flex-col space-y-2">
                                  <div className="flex justify-between">
                                    <span className="text-sm text-blue-gray-500">
                                      Quantity:
                                    </span>
                                    <span className="text-sm font-medium">
                                      {item.quantity}
                                    </span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-sm text-blue-gray-500">
                                      Unit Price:
                                    </span>
                                    <span className="text-sm font-medium">
                                      {item.unitPrice.toFixed(2)} MAD
                                    </span>
                                  </div>
                                  <div className="flex justify-between pt-2 border-t border-blue-gray-100 mt-2">
                                    <span className="text-sm font-medium"></span>
                                    <span className="text-sm font-semibold text-blue-gray-900">
                                      {item.totalPrice.toFixed(2)} MAD
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter>
              <button
                onMouseDown={(event) => ripple.create(event, "light")}
                onClick={() => setIsDetailsDialogOpen(false)}
                className="w-full sm:w-auto py-3 px-6 bg-blue-gray-900 text-white rounded-lg hover:bg-blue-gray-800 hover:shadow-md transition-all duration-300 font-medium"
                type="button"
              >
                Close
              </button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default PurchaseHistory;
