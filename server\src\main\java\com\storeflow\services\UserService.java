package com.storeflow.services;

import com.storeflow.mappers.SupplierMapper;
import com.storeflow.repositories.SupplierRepository;
import com.storeflow.requests.ChangePasswordRequest;
import com.storeflow.requests.ProfileUpdateRequest;
import com.storeflow.DTOs.UserDTO;
import com.storeflow.enums.Role;
import com.storeflow.exception.*;
import com.storeflow.mappers.UserMapper;
import com.storeflow.models.User;
import com.storeflow.repositories.UserRepository;
import com.storeflow.utils.PhoneNumberFormatter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.security.Principal;
import java.util.Objects;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {
    private final UserMapper userMapper;
    private final FileService fileService;
    private final UserRepository userRepository;
    private final SupplierMapper supplierMapper;
    private final PasswordEncoder passwordEncoder;
    private final SupplierRepository supplierRepository;
    private final PhoneNumberFormatter phoneNumberFormatter;

    /**
     * Check if the user is authenticated
     *
     * @param principal The authenticated user principal
     * @return true if the user is authenticated, false otherwise
     */
    public boolean isUserAuthenticated(Principal principal) {
        log.info("Checking if user '{}' is authenticated", principal.getName());
        User user = getUserEntityFromPrincipal(principal);
        return user != null;
    }

    /**
     * Get the current authenticated user details
     *
     * @param principal The authenticated user
     * @return The authenticated user role
     */
    public Role getAuthenticatedUserRole(Principal principal) {
        log.info("User role request received for: {}", principal.getName());
        return getUserEntityFromPrincipal(principal).getRole();
    }

    /**
     * Get the current authenticated user profile information with role-specific details
     *
     * @param principal The authenticated user
     * @return UserDTO or SupplierDTO based on the user's role
     * @throws ResourceNotFoundException If the user or supplier details cannot be found
     */
    public Object getAuthenticatedUserProfileInfo(Principal principal) {
        User user = getUserEntityFromPrincipal(principal);
        log.debug("Retrieving profile information for user: {} with role: {}", user.getId(), user.getRole());

        if (user.getRole() == Role.SUPPLIER) {
            log.debug("User is a Supplier, retrieving supplier-specific information");
            return supplierRepository.findById(user.getId())
                .map(supplier -> {
                    log.debug("Supplier found with localization: {}", supplier.getLocalization());
                    return supplierMapper.toSupplierDTO(supplier);
                })
                .orElseThrow(() -> {
                    log.error("User with ID {} has SUPPLIER role but no supplier record found.", user.getId());
                    return new ResourceNotFoundException("Supplier details not found.");
                });
        } else {
            log.debug("User has role: {}, returning standard user information", user.getRole());
            return userMapper.toUserDTO(user);
        }
    }

    /**
     * Get the count of users with CLIENT role
     *
     * @return The number of clients registered in the system
     */
    public long getClientCount() {
        log.debug("Retrieving client count");
        long count = userRepository.countByRole(Role.CLIENT);
        log.debug("Found {} clients in the system", count);
        return count;
    }

    /**
     * Updates the personal information of the authenticated user
     *
     * @param updatedDetails The updated user details
     * @param principal The authenticated principal
     * @return The UUID of the updated user
     */
    @Transactional
    public UUID updatePersonalInfo(ProfileUpdateRequest updatedDetails, Principal principal) {
        // Get the authenticated user
        User user = getUserEntityFromPrincipal(principal);

        // Update base User fields
        updateBaseUserFields(user, updatedDetails);

        // If the user is a Supplier, update Supplier-specific fields
        if (user.getRole() == Role.SUPPLIER) {
            updateSupplierFields(user.getId(), updatedDetails);
        }

        // Save the updated user
        userRepository.save(user);

        return user.getId();
    }

    /**
     * Updates the profile picture of the authenticated user
     *
     * @param file The new profile picture file
     * @param principal The authenticated principal
     * @return The UUID of the updated user
     */
    @Transactional
    public String updateProfilePicture(MultipartFile file, Principal principal) {
        log.info("Updating profile picture for authenticated user");

        // Get the authenticated user
        User user = getUserEntityFromPrincipal(principal);

        // Validate the file before proceeding
        if (file == null || file.isEmpty()) {
            throw new FileUploadException("Cannot upload empty file");
        }

        // Get the current profile picture URL
        String currentPictureUrl = user.getProfilePictureUrl();

        try {
            // Extract just the filename from the URL
            String filename = currentPictureUrl.substring(currentPictureUrl.lastIndexOf('/') + 1);
            log.debug("Deleting old profile picture: {}", filename);
            fileService.deleteFile(fileService.getMinioConfig().getProfileBucket(), filename);
        } catch (Exception e) {
            log.error("Failed to delete old profile picture for user: {}", user.getId(), e);
            throw new FileDeletionException("Unable to delete previous profile picture. Update aborted.", e);
        }

        // Upload new profile picture
        try {
            String profilePictureUrl = fileService.uploadProfilePicture(file);
            user.setProfilePictureUrl(profilePictureUrl);
        } catch (FileStorageException e) {
            throw new FileUploadException("Failed to upload new profile picture", e);
        }

        // Save the updated user
        userRepository.save(user);
        log.info("Successfully updated profile picture for user: {}", user.getId());

        return user.getProfilePictureUrl();
    }

    /**
     * Resets the profile picture to the default for the authenticated user
     *
     * @param principal The authenticated principal
     * @return The UUID of the updated user
     */
    @Transactional
    public String resetToDefaultProfilePicture(Principal principal) {
        log.info("Resetting to default profile picture for authenticated user");

        // Get the authenticated user
        User user = getUserEntityFromPrincipal(principal);

        // Get the current profile picture URL
        String currentPictureUrl = user.getProfilePictureUrl();

        // Only delete if it's NOT already a default picture
        if (currentPictureUrl != null && fileService.isNotDefaultProfilePicture(currentPictureUrl)) {
            try {
                // Extract just the filename from the URL
                String filename = currentPictureUrl.substring(currentPictureUrl.lastIndexOf('/') + 1);
                log.info("Deleting old profile picture: {}", filename);
                fileService.deleteFile(fileService.getMinioConfig().getProfileBucket(), filename);
            } catch (Exception e) {
                log.error("Failed to delete profile picture for user: {}", user.getId(), e);
                throw new FileDeletionException("Unable to delete profile picture", e);
            }

            // Set a new default profile picture
            String defaultProfileUrl = fileService.getDefaultProfilePictureUrl();
            user.setProfilePictureUrl(defaultProfileUrl);

            // Save the updated user
            userRepository.save(user);
            log.info("Successfully reset to default profile picture for user: {}", user.getId());
        } else {
            log.info("User already has a default profile picture, no action needed");
        }

        return user.getProfilePictureUrl();
    }

    @Transactional
    public void changePassword(ChangePasswordRequest request, Principal connectedUser) {
        // Get the authenticated user using the helper method
        User user = getUserEntityFromPrincipal(connectedUser);

        // Validate current password
        if (!passwordEncoder.matches(request.currentPassword(), user.getPassword())) {
            log.warn("Failed password change attempt - incorrect current password for user: {}", user.getEmail());
            throw new InvalidCredentialsException("Current password is incorrect");
        }

        // Validate new password matches confirmation
        if (!request.newPassword().equals(request.confirmationPassword())) {
            throw new PasswordMismatchException("New password and confirmation do not match");
        }

        // Validate new password is different from old password
        if (passwordEncoder.matches(request.newPassword(), user.getPassword())) {
            throw new InvalidPasswordException("New password must be different from current password");
        }

        // Update password
        user.setPassword(passwordEncoder.encode(request.newPassword()));
        userRepository.save(user);

        log.info("Password changed successfully for user: {}", user.getEmail());
    }

    /**
     * Helper method to extract UserDTO from Principal
     *
     * @param principal The authenticated principal
     * @return The UserDTO associated with the principal
     * @throws AuthenticationFailedException If authentication cannot be processed
     * @throws ResourceNotFoundException If the user cannot be found
     */
    public UserDTO getUserDTOFromPrincipal(Principal principal) {
        User user = getUserEntityFromPrincipal(principal);
        return userMapper.toUserDTO(user);
    }

    /**
     * Helper method to extract User entity from Principal
     * This should only be used by internal services that need the actual entity
     *
     * @param principal The authenticated principal
     * @return The User entity associated with the principal
     * @throws AuthenticationFailedException If authentication cannot be processed
     * @throws ResourceNotFoundException If the user cannot be found
     */
    private User getUserEntityFromPrincipal(Principal principal) {
        try {
            if (principal instanceof UsernamePasswordAuthenticationToken authToken &&
                authToken.getPrincipal() instanceof User authenticatedUser) {
                return authenticatedUser;
            } else {
                // Fallback to email lookup if the principal casting doesn't work
                String email = principal.getName();
                return userRepository.findByEmail(email)
                    .orElseThrow(() -> new ResourceNotFoundException("User not found"));
            }
        } catch (ClassCastException e) {
            log.error("Authentication principal could not be cast to User", e);
            throw new AuthenticationFailedException("Authentication error", e);
        }
    }

    /**
     * Helper method to extract User ID from Principal
     *
     * @param principal The authenticated principal
     * @return The UUID of the user associated with the principal
     * @throws AuthenticationFailedException If authentication cannot be processed
     * @throws ResourceNotFoundException If the user cannot be found
     */
    public UUID getUserIdFromPrincipal(Principal principal) {
        return getUserEntityFromPrincipal(principal).getId();
    }

    /**
     * Updates the base User entity fields
     */
    private void updateBaseUserFields(User user, ProfileUpdateRequest updatedDetails) {
        // Update first name if provided
        final String updatedFirstName = updatedDetails.firstName();
        if (updatedFirstName != null
            && !updatedFirstName.trim().isEmpty()
            && !Objects.equals(user.getFirstName(), updatedFirstName.trim())) {
            user.setFirstName(updatedFirstName.trim());
        }

        // Update last name if provided
        final String updatedLastName = updatedDetails.lastName();
        if (updatedLastName != null
            && !updatedLastName.trim().isEmpty()
            && !Objects.equals(user.getLastName(), updatedLastName.trim())) {
            user.setLastName(updatedLastName.trim());
        }

        // Update email if provided and not already in use
        final String updatedEmail = updatedDetails.email();
        if (updatedEmail != null
            && !updatedEmail.isEmpty()
            && !Objects.equals(user.getEmail(), updatedEmail)) {
            if (userRepository.existsByEmail(updatedEmail)) {
                throw new DuplicateResourceException("Email already taken");
            }

            user.setEmail(updatedEmail);
        }

        // Update phone number if provided and not already in use
        final String formattedUpdatedPhoneNumber = phoneNumberFormatter.formatToInternational(updatedDetails.phoneNumber());
        if (formattedUpdatedPhoneNumber != null
            && !formattedUpdatedPhoneNumber.isEmpty()
            && !Objects.equals(user.getPhoneNumber(), formattedUpdatedPhoneNumber)) {

            if (userRepository.existsByPhoneNumber(formattedUpdatedPhoneNumber)) {
                throw new DuplicateResourceException("Phone number already taken");
            }

            user.setPhoneNumber(formattedUpdatedPhoneNumber);
        }
    }

    /**
     * Updates Supplier-specific fields
     */
    private void updateSupplierFields(UUID userId, ProfileUpdateRequest updatedDetails) {
        log.debug("Updating supplier-specific fields for user ID: {}", userId);

        supplierRepository.findById(userId).ifPresent(supplier -> {

            // Update localization if provided
            if (
                updatedDetails.localization() != null
                    && !updatedDetails.localization().trim().isEmpty()
                    && !Objects.equals(supplier.getLocalization(), updatedDetails.localization().trim())
            ) {
                supplier.setLocalization(updatedDetails.localization().trim());
            }

            supplierRepository.save(supplier);
            log.debug("Supplier-specific fields updated successfully");
        });
    }
}
