package com.storeflow.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = PhoneNumberValidator.class)
@Documented
public @interface PhoneNumber {
    String message() default "Phone number must be valid ";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
    boolean requireCountryCode() default true;
}
