package com.storeflow.DTOs;

import com.storeflow.enums.PurchaseStatus;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * DTO for Purchase entity.
 */
public record PurchaseDTO(
    UUID id,
    UUID supplierId,
    String supplierName,
    String supplierEmail,
    LocalDateTime purchaseDate,
    PurchaseStatus status,
    BigDecimal totalAmount,
    String notes,
    List<PurchaseItemDTO> items,
    LocalDateTime createdDate,
    LocalDateTime lastModifiedDate,
    String createdBy,
    String lastModifiedBy
) {
}
