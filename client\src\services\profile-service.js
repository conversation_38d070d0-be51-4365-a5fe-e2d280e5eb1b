import { authApi } from "@/context/auth-context";

export const profileService = {
  // Update profile information
  updateProfile: async (profileData) => {
    try {
      const response = await authApi.patch(
        "/api/v1/users/profile",
        profileData
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Update profile picture
  updateProfilePicture: async (imageFile) => {
    try {
      const formData = new FormData();
      formData.append("image", imageFile);

      const response = await authApi.patch(
        "/api/v1/users/profile-picture",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Reset to default profile picture
  resetProfilePicture: async () => {
    try {
      const response = await authApi.delete("/api/v1/users/profile-picture");
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Change password
  changePassword: async (passwordData) => {
    try {
      const response = await authApi.post(
        "/api/v1/users/change-password",
        passwordData
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

export default profileService;
