// src/pages/portals/Admin.jsx
import { Configurator, Navbar, Sidebar } from "@/components/portals";
import { routes } from "@/routes/admin";
import { Navigate, Route, Routes, useNavigate } from "react-router-dom";
import { useEffect } from "react";
import { useAuth } from "@/context/auth-context";
import { useSelector } from "react-redux";

export const Admin = () => {
  const { getUserRole, isAuthenticated, loading } = useAuth();
  const user = useSelector((state) => state.user.userData);
  const navigate = useNavigate();

  // Redirect if not authenticated or not an admin
  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated()) {
        navigate("/auth/login", { replace: true });
        return;
      }

      const role = getUserRole()?.toLowerCase();
      if (role !== "admin") {
        // Redirect to appropriate dashboard based on role
        switch (role) {
          case "client":
            navigate("/client/dashboard", { replace: true });
            break;
          case "supplier":
            navigate("/supplier/dashboard", { replace: true });
            break;
          case "employee":
            navigate("/employee/dashboard", { replace: true });
            break;
          default:
            navigate("/", { replace: true });
        }
      }
    }
  }, [isAuthenticated, getUserRole, loading, navigate]);

  // Show loading when checking auth
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Get user info for display
  const firstName = user?.firstName || user?.email?.split("@")[0] || "Admin";
  const fullName =
    user?.firstName && user?.lastName
      ? `${user.firstName} ${user.lastName}`
      : firstName;
  const email = user?.email || "";
  const profilePicture = user?.profilePictureUrl || "/img/default-profile.jpg";

  return (
    <main className="min-h-dvh bg-blue-gray-50/50">
      <Sidebar userRole="admin" routes={routes} firstName={firstName} />
      <div className="p-4 lg:ml-72">
        <Navbar
          userRole="admin"
          profilePicture={profilePicture}
          fullName={fullName}
          email={email}
        />
        <Configurator />
        <Routes>
          {routes.map(({ path, element }, index) => {
            return <Route key={index} path={path} element={element} />;
          })}
          {/* Catch-all route for undefined paths */}
          <Route
            path="*"
            element={<Navigate to="/admin/dashboard" replace />}
          />
        </Routes>
      </div>
    </main>
  );
};

export default Admin;
