package com.storeflow.DTOs.requests;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;
import java.util.UUID;

/**
 * Request DTO for creating a new purchase.
 */
public record CreatePurchaseRequest(
    @NotNull(message = "Supplier ID is required")
    UUID supplierId,
    
    @Size(max = 1000, message = "Notes cannot exceed 1000 characters")
    String notes,
    
    @NotEmpty(message = "At least one item is required")
    @Valid
    List<CreatePurchaseItemRequest> items
) {
}
