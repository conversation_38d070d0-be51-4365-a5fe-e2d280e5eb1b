package com.storeflow.services;

import com.storeflow.models.SupplierProduct;
import com.storeflow.repositories.SupplierProductRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

/**
 * Service for managing supplier products.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplierProductService {
    private final SupplierProductRepository supplierProductRepository;

    /**
     * Get a supplier product by supplier ID and product ID.
     *
     * @param supplierId The ID of the supplier
     * @param productId The ID of the product
     * @return The supplier product, or null if not found
     */
    @Transactional(readOnly = true)
    public SupplierProduct getSupplierProductBySupplierAndProduct(UUID supplierId, UUID productId) {
        log.debug("Getting supplier product for supplier {} and product {}", supplierId, productId);
        Optional<SupplierProduct> supplierProduct = supplierProductRepository.findBySupplierIdAndProductId(supplierId, productId);
        return supplierProduct.orElse(null);
    }

    /**
     * Save a supplier product.
     *
     * @param supplierProduct The supplier product to save
     * @return The saved supplier product
     */
    @Transactional
    public SupplierProduct saveSupplierProduct(SupplierProduct supplierProduct) {
        log.debug("Saving supplier product for supplier {} and product {}", 
            supplierProduct.getSupplier().getId(), supplierProduct.getProduct().getId());
        return supplierProductRepository.save(supplierProduct);
    }
}
