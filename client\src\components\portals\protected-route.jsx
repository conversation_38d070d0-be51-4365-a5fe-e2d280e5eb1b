// src/components/ProtectedRoute.jsx
import { Navigate, Outlet, useLocation } from "react-router-dom";
import { useAuth } from "@/context/auth-context";

export const ProtectedRoute = ({
  allowedRoles = [],
  redirectPath = "/auth/login",
}) => {
  const { isAuthenticated, getUserRole, loading } = useAuth();
  const location = useLocation();

  // Show loading indicator while checking authentication status
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Check if user is authenticated
  if (!isAuthenticated()) {
    // Redirect to login page with the current location
    return <Navigate to={redirectPath} state={{ from: location }} replace />;
  }

  // If no specific roles required, or user role matches allowed roles
  const userRole = getUserRole();
  if (allowedRoles.length === 0 || allowedRoles.includes(userRole)) {
    return <Outlet />;
  }

  // If user doesn't have required role, redirect based on their role
  let redirectTo = "/";

  switch (userRole) {
    case "admin":
      redirectTo = "/admin/dashboard";
      break;
    case "client":
      redirectTo = "/client/dashboard";
      break;
    case "supplier":
      redirectTo = "/supplier/dashboard";
      break;
    case "employee":
      redirectTo = "/employee/dashboard";
      break;
    default:
      redirectTo = "/";
  }

  return <Navigate to={redirectTo} replace />;
};

export default ProtectedRoute;
