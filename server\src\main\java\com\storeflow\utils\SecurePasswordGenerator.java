package com.storeflow.utils;

import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Utility class to generate secure random passwords that match the pattern:
 * ^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[!"#$%&'()*+,-./:;<=>?@[\]^_`{|}~]).{8,}$
 * The pattern requires at least:
 * - One digit
 * - One lowercase letter
 * - One uppercase letter
 * - One special character from !"#$%&'()*+,-./:;<=>?@[\]^_`{|}~
 * - Minimum length of 8 characters
 */
@Component
public class SecurePasswordGenerator {

    private static final String LOWERCASE_CHARS = "abcdefghijklmnopqrstuvwxyz";
    private static final String UPPERCASE_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String DIGIT_CHARS = "0123456789";
    private static final String SPECIAL_CHARS = "!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~";

    private final SecureRandom random;

    public SecurePasswordGenerator() {
        this.random = new SecureRandom();
    }

    /**
     * Generates a secure random password of exactly 8 characters that meets the required pattern.
     *
     * @return A secure random password string
     */
    public String generateSecurePassword() {
        // Generate one character from each required character class
        char lowercase = LOWERCASE_CHARS.charAt(random.nextInt(LOWERCASE_CHARS.length()));
        char uppercase = UPPERCASE_CHARS.charAt(random.nextInt(UPPERCASE_CHARS.length()));
        char digit = DIGIT_CHARS.charAt(random.nextInt(DIGIT_CHARS.length()));
        char special = SPECIAL_CHARS.charAt(random.nextInt(SPECIAL_CHARS.length()));

        // Create a list of all possible characters
        String allChars = LOWERCASE_CHARS + UPPERCASE_CHARS + DIGIT_CHARS + SPECIAL_CHARS;

        // Generate remaining characters to reach length 8
        StringBuilder remainingChars = new StringBuilder();
        for (int i = 0; i < 4; i++) {
            remainingChars.append(allChars.charAt(random.nextInt(allChars.length())));
        }

        // Combine all generated characters
        List<Character> passwordChars = new ArrayList<>();
        passwordChars.add(lowercase);
        passwordChars.add(uppercase);
        passwordChars.add(digit);
        passwordChars.add(special);

        for (char c : remainingChars.toString().toCharArray()) {
            passwordChars.add(c);
        }

        // Shuffle to randomize the position of each character type
        Collections.shuffle(passwordChars, random);

        // Convert to string
        StringBuilder password = new StringBuilder(8);
        for (char c : passwordChars) {
            password.append(c);
        }

        return password.toString();
    }
}
