package com.storeflow.requests;

import com.storeflow.validation.PhoneNumber;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

public record ProfileUpdateRequest(
    @Size(min = 2, max = 50, message = "First name must be between 2 and 50 characters.")
    @Pattern(regexp = "^[a-zA-Z\\s-']+$", message = "First name can only contain letters, spaces, hyphens, and apostrophes.")
    String firstName,

    @Size(min = 2, max = 50, message = "Last name must be between 2 and 50 characters.")
    @Pattern(regexp = "^[a-zA-Z\\s-']+$", message = "Last name can only contain letters, spaces, hyphens, and apostrophes.")
    String lastName,

    @Email(message = "Email must be valid.")
    @Size(max = 100, message = "Email cannot exceed 100 characters.")
    String email,

    @PhoneNumber
    String phoneNumber,

    @Size(min = 2, max = 50, message = "Localization must be between 2 and 50 characters.")
    @Pattern(regexp = "^[a-zA-Z\\s-,']+$", message = "Localization can only contain letters, spaces, hyphens, commas, and apostrophes.")
    String localization
) {
}
