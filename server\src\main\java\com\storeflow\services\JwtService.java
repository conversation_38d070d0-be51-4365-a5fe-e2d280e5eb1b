package com.storeflow.services;

import com.storeflow.exception.TokenAuthenticationException;
import com.storeflow.exception.TokenProcessingException;
import com.storeflow.models.User;
import com.storeflow.repositories.TokenRepository;
import io.jsonwebtoken.*;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SecurityException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Service
@RequiredArgsConstructor
public class JwtService {
    @Value("${application.security.jwt.secret-key}")
    private String secretKey;

    @Value("${application.security.jwt.expiration}")
    private long jwtExpiration;

    @Value("${application.security.jwt.refresh-token.expiration}")
    private long refreshExpiration;

    private final TokenRepository tokenRepository;

    /**
     * Extract username (email) from JWT token
     */
    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    /**
     * Extract any claim from JWT token using a resolver function
     */
    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    /**
     * Generate token with default claims
     */
    public String generateToken(UserDetails userDetails) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("role", extractRoleFromUserDetails(userDetails));
        return generateToken(claims, userDetails);
    }

    /**
     * Generate token with extra claims
     */
    public String generateToken(
        Map<String, Object> extraClaims,
        UserDetails userDetails
    ) {
        return buildToken(extraClaims, userDetails, jwtExpiration);
    }

    /**
     * Generate refresh token (similar to access token but with longer expiration)
     */
    public String generateRefreshToken(
        UserDetails userDetails
    ) {
        Map<String, Object> claims = new HashMap<>();

        // Add a unique token identifier for revocation purposes
        claims.put("token_id", java.util.UUID.randomUUID().toString());
        claims.put("role", extractRoleFromUserDetails(userDetails));

        return buildToken(claims, userDetails, refreshExpiration);
    }

    /**
     * Extracts the role name from a UserDetails object
     * Gets it directly from User.role when possible
     */
    private String extractRoleFromUserDetails(UserDetails userDetails) {
        if (userDetails instanceof User) {
            User user = (User) userDetails;
            return user.getRole().name().toLowerCase();
        }
        return "user"; // Default role
    }

    /**
     * Creates a standardized JWT builder with consistent header parameters
     */
    private JwtBuilder createStandardJwtBuilder() {
        return Jwts.builder()
            .setHeaderParam("alg", SignatureAlgorithm.HS256.getValue())
            .setHeaderParam("typ", "JWT");
    }

    /**
     * Build token with specified claims, user details, and expiration
     */
    private String buildToken(
        Map<String, Object> extraClaims,
        UserDetails userDetails,
        long expiration
    ) {
        return createStandardJwtBuilder()
            .setClaims(extraClaims)
            .setSubject(userDetails.getUsername())
            .setIssuedAt(new Date(System.currentTimeMillis()))
            .setExpiration(new Date(System.currentTimeMillis() + expiration))
            .signWith(getSignInKey(), SignatureAlgorithm.HS256)
            .compact();
    }

    /**
     * Check if a token is valid for the given user
     */
    public boolean isTokenValid(String token, UserDetails userDetails) {
        final String username = extractUsername(token);
        return (username.equals(userDetails.getUsername()))
            && !isTokenExpired(token);
    }

    /**
     * Check if token is expired
     */
    private boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    /**
     * Extract expiration date from token
     */
    private Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    /**
     * Extract all claims from token
     */
    private Claims extractAllClaims(String token) {
        try {
            return Jwts
                .parserBuilder()
                .setSigningKey(getSignInKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
        } catch (ExpiredJwtException e) {
            throw new TokenAuthenticationException("Your session has expired. Please log in again.", e);
        } catch (SecurityException | MalformedJwtException e) {
            throw new TokenProcessingException("Invalid authentication token.", e);
        } catch (UnsupportedJwtException e) {
            throw new TokenProcessingException("Unsupported authentication token format.", e);
        } catch (IllegalArgumentException e) {
            throw new TokenProcessingException("Authentication token is missing.", e);
        } catch (Exception e) {
            throw new TokenAuthenticationException("Authentication failed: " + e.getMessage(), e);
        }
    }

    /**
     * Extract user role from token
     */
    public String extractRole(String token) {
        try {
            return extractClaim(token, claims -> claims.get("role", String.class));
        } catch (Exception e) {
            return "user"; // Default role if extraction fails
        }
    }

    /**
     * Extract token ID (for revocation purposes)
     */
    public String extractTokenId(String token) {
        return extractClaim(token, claims -> claims.get("token_id", String.class));
    }

    /**
     * Get signing key from secret
     */
    private Key getSignInKey() {
        byte[] keyBytes = Decoders.BASE64.decode(secretKey);
        return Keys.hmacShaKeyFor(keyBytes);
    }
}
