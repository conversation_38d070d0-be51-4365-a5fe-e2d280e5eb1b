spring.application.name=storeflow
spring.datasource.url=***************************:${POSTGRES_PORT}/${POSTGRES_DB}
spring.datasource.username=${POSTGRES_USER}
spring.datasource.password=${POSTGRES_PASSWORD}
spring.datasource.driver-class-name=org.postgresql.Driver

spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.show-sql=true

application.security.jwt.secret-key=${SECRET_KEY}
# Set access token to expire in 1 minute (just for testing the refresh token mechanism)
application.security.jwt.expiration=60000
# Set refresh token to expire in 7 days
application.security.jwt.refresh-token.expiration=604800000
# Token cleanup configuration (1 day)
application.security.token.cleanup.retention-days=1

# MinIO Configuration
minio.endpoint=${MINIO_URL}
minio.accessKey=${MINIO_ACCESS_KEY}
minio.secretKey=${MINIO_SECRET_KEY}
minio.profile-bucket=profile-pictures
minio.product-bucket=product-pictures
minio.max-file-size=8388608

# Multipart file configuration
spring.servlet.multipart.max-file-size=8MB
spring.servlet.multipart.max-request-size=8MB

# Email Configuration
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=${EMAIL_USERNAME}
spring.mail.password=${EMAIL_PASSWORD}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.ssl.trust=smtp.gmail.com

# Application Configuration
application.frontend.url=${FRONTEND_URL:http://localhost:5173}
application.email.from=${EMAIL_FROM:<EMAIL>}

# OAuth2 Configuration
spring.security.oauth2.client.registration.google.client-id=${GOOGLE_CLIENT_ID}
spring.security.oauth2.client.registration.google.client-secret=${GOOGLE_CLIENT_SECRET}
spring.security.oauth2.client.registration.google.scope=openid,profile,email
spring.security.oauth2.client.registration.google.redirect-uri={baseUrl}/api/v1/auth/oauth2/callback/google
spring.security.oauth2.client.registration.google.client-name=Google

spring.security.oauth2.client.provider.google.authorization-uri=https://accounts.google.com/o/oauth2/auth
spring.security.oauth2.client.provider.google.token-uri=https://oauth2.googleapis.com/token
spring.security.oauth2.client.provider.google.user-info-uri=https://www.googleapis.com/oauth2/v3/userinfo
spring.security.oauth2.client.provider.google.user-name-attribute=sub
