package com.storeflow.DTOs;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

public record SupplierProductDTO(
    UUID productId,
    String label,
    String description,
    String brand,
    String imageUrl,
    Integer stockQuantity,
    BigDecimal purchasePrice,
    BigDecimal sellingPrice,
    BigDecimal margin,
    Integer deliveryTime,
    LocalDateTime createdDate,
    LocalDateTime lastModifiedDate
) {
}
