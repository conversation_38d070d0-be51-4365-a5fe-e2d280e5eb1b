package com.storeflow.models;

import jakarta.persistence.*;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Entity representing an item in a purchase from a supplier.
 */
@Entity
@Getter
@Setter
@Builder
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
@Table(
    name = "purchase_items",
    indexes = {
        @Index(name = "purchase_item_primary_key", columnList = "id", unique = true),
        @Index(name = "purchase_item_purchase_index", columnList = "purchase_id"),
        @Index(name = "purchase_item_product_index", columnList = "product_id")
    }
)
public class PurchaseItem {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @ManyToOne
    @JoinColumn(
        name = "purchase_id",
        foreignKey = @ForeignKey(name = "purchase_item_purchase_fk"),
        nullable = false
    )
    private Purchase purchase;

    @ManyToOne
    @JoinColumn(
        name = "supplier_id",
        foreignKey = @ForeignKey(name = "purchase_item_supplier_fk"),
        nullable = false
    )
    private Supplier supplier;

    @ManyToOne
    @JoinColumn(
        name = "product_id",
        foreignKey = @ForeignKey(name = "purchase_item_product_fk"),
        nullable = false
    )
    private Product product;

    @Column(nullable = false)
    private String productLabel;

    @Column(nullable = false)
    private Integer quantity;

    @Column(name = "consumed_quantity", nullable = false)
    @Builder.Default
    private Integer consumedQuantity = 0;

    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal unitPrice;

    @CreatedDate
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdDate;

    @LastModifiedDate
    @Column(insertable = false)
    private LocalDateTime lastModifiedDate;

    /**
     * Calculate the total price for this item
     * @return The total price (unit price * quantity)
     */
    @Transient
    public BigDecimal getTotalPrice() {
        return unitPrice.multiply(BigDecimal.valueOf(quantity));
    }

    /**
     * Get the available quantity (original quantity minus consumed quantity).
     * @return The available quantity for client orders
     */
    @Transient
    public Integer getAvailableQuantity() {
        return quantity - consumedQuantity;
    }

    /**
     * Consume a certain quantity for client orders.
     * @param quantityToConsume The quantity to consume
     * @return true if successful, false if insufficient available quantity
     */
    public boolean consumeQuantity(Integer quantityToConsume) {
        if (quantityToConsume <= 0 || getAvailableQuantity() < quantityToConsume) {
            return false;
        }
        this.consumedQuantity += quantityToConsume;
        return true;
    }

    /**
     * Check if this purchase item has available stock.
     * @return true if available quantity > 0
     */
    @Transient
    public boolean hasAvailableStock() {
        return getAvailableQuantity() > 0;
    }
}
