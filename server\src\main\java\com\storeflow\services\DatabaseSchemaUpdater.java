package com.storeflow.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service to handle database schema updates that Hibernate's auto-update can't handle.
 * This runs after the main application context is loaded but before other CommandLineRunners.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Order(1) // Run before other CommandLineRunners like SystemBootstrapper
public class DatabaseSchemaUpdater implements CommandLineRunner {

    private final JdbcTemplate jdbcTemplate;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Starting database schema updates for supplier deletion fix...");
        
        try {
            updatePurchasesTable();
            updatePurchaseItemsTable();
            log.info("Database schema updates completed successfully");
        } catch (Exception e) {
            log.error("Failed to update database schema", e);
            // Don't throw exception to prevent application startup failure
            // The application can still work, but supplier deletion might fail
        }
    }

    /**
     * Update the purchases table to support supplier deletion
     */
    private void updatePurchasesTable() {
        log.info("Updating purchases table schema...");
        
        // Add supplier_name column if it doesn't exist
        try {
            jdbcTemplate.execute("""
                DO $$
                BEGIN
                    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                                   WHERE table_name = 'purchases' AND column_name = 'supplier_name') THEN
                        ALTER TABLE purchases ADD COLUMN supplier_name VARCHAR(255);
                        RAISE NOTICE 'Added supplier_name column to purchases table';
                    END IF;
                END $$;
                """);
        } catch (Exception e) {
            log.warn("Could not add supplier_name column: {}", e.getMessage());
        }

        // Add supplier_email column if it doesn't exist
        try {
            jdbcTemplate.execute("""
                DO $$
                BEGIN
                    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                                   WHERE table_name = 'purchases' AND column_name = 'supplier_email') THEN
                        ALTER TABLE purchases ADD COLUMN supplier_email VARCHAR(255);
                        RAISE NOTICE 'Added supplier_email column to purchases table';
                    END IF;
                END $$;
                """);
        } catch (Exception e) {
            log.warn("Could not add supplier_email column: {}", e.getMessage());
        }

        // Populate supplier information for existing records
        try {
            int updatedRows = jdbcTemplate.update("""
                UPDATE purchases 
                SET supplier_name = CONCAT(u.first_name, ' ', u.last_name),
                    supplier_email = u.email
                FROM suppliers s
                JOIN users u ON s.user_id = u.id
                WHERE purchases.supplier_id = s.id
                  AND (purchases.supplier_name IS NULL OR purchases.supplier_email IS NULL)
                """);
            if (updatedRows > 0) {
                log.info("Populated supplier information for {} existing purchase records", updatedRows);
            }
        } catch (Exception e) {
            log.warn("Could not populate supplier information: {}", e.getMessage());
        }

        // Drop NOT NULL constraint on supplier_id
        try {
            jdbcTemplate.execute("ALTER TABLE purchases ALTER COLUMN supplier_id DROP NOT NULL");
            log.info("Dropped NOT NULL constraint on purchases.supplier_id");
        } catch (Exception e) {
            log.warn("Could not drop NOT NULL constraint on purchases.supplier_id: {}", e.getMessage());
        }
    }

    /**
     * Update the purchase_items table to support supplier deletion
     */
    private void updatePurchaseItemsTable() {
        log.info("Updating purchase_items table schema...");
        
        // Drop NOT NULL constraint on supplier_id
        try {
            jdbcTemplate.execute("ALTER TABLE purchase_items ALTER COLUMN supplier_id DROP NOT NULL");
            log.info("Dropped NOT NULL constraint on purchase_items.supplier_id");
        } catch (Exception e) {
            log.warn("Could not drop NOT NULL constraint on purchase_items.supplier_id: {}", e.getMessage());
        }
    }
}
