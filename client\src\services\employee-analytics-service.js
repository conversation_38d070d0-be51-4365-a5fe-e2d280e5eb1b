import { authApi } from "@/context/auth-context";
import { purchaseService } from "@/services/purchase-service";

/**
 * Service for employee analytics-related API calls
 */
export const employeeAnalyticsService = {
  /**
   * Get employee dashboard statistics
   * @returns {Promise<Object>} - Promise resolving to API response with employee statistics
   */
  getEmployeeDashboardStats: async () => {
    try {
      // Fetch employee's own purchases data to calculate statistics
      const purchasesResponse = await purchaseService.getMyPurchases({
        page: 0,
        size: 100, // Get enough data for analysis
        sortBy: "purchaseDate",
        sortDir: "desc",
      });

      // Fetch recent orders data
      const ordersResponse = await authApi.get("/api/v1/orders", {
        params: {
          page: 0,
          size: 10, // Get recent orders
          sortBy: "orderDate",
          sortDir: "desc",
        },
      });

      let stats = {
        totalPurchases: 0,
        totalSpent: 0,
        productsPurchased: 0,
        activeSuppliers: 0,
        recentActivity: [],
      };

      // Process purchases data if available
      if (purchasesResponse.success && purchasesResponse.data?.content) {
        const purchases = purchasesResponse.data.content;

        // Calculate statistics
        stats.totalPurchases = purchases.length;
        stats.totalSpent = purchases.reduce(
          (sum, purchase) => sum + (purchase.totalAmount || 0),
          0
        );

        // Count total products purchased (sum of all item quantities)
        stats.productsPurchased = purchases.reduce((sum, purchase) => {
          return (
            sum +
            (purchase.items?.reduce(
              (itemSum, item) => itemSum + (item.quantity || 0),
              0
            ) || 0)
          );
        }, 0);

        // Count unique suppliers
        const uniqueSuppliers = new Set(
          purchases.map((purchase) => purchase.supplierId).filter(Boolean)
        );
        stats.activeSuppliers = uniqueSuppliers.size;

        // Create recent activity from purchases
        const recentPurchases = purchases.slice(0, 5).map((purchase) => ({
          type: "purchase",
          title: `You purchased ${purchase.items?.length || 0} items from ${
            purchase.supplierName || "Supplier"
          }`,
          description: `Total: ${
            purchase.totalAmount?.toFixed(2) || "0.00"
          } MAD`,
          date: purchase.purchaseDate,
          icon: "shopping-cart",
        }));

        stats.recentActivity = [...recentPurchases];
      }

      // Add recent orders to activity if available
      if (ordersResponse.data.success && ordersResponse.data.data?.content) {
        const orders = ordersResponse.data.data.content.slice(0, 3);
        const orderActivities = orders.map((order) => ({
          type: "order",
          title: `Order #${order.id.slice(
            -8
          )} was ${order.status.toLowerCase()}`,
          description: `Client: ${order.clientName || "Unknown"} - ${
            order.totalAmount?.toFixed(2) || "0.00"
          } MAD`,
          date: order.orderDate,
          icon: "package",
        }));

        stats.recentActivity = [...stats.recentActivity, ...orderActivities];
      }

      // Sort recent activity by date (newest first)
      stats.recentActivity.sort((a, b) => new Date(b.date) - new Date(a.date));

      // Keep only the 6 most recent activities
      stats.recentActivity = stats.recentActivity.slice(0, 6);

      return {
        success: true,
        message: "Employee dashboard statistics retrieved successfully",
        data: stats,
      };
    } catch (error) {
      console.error("Error fetching employee dashboard statistics:", error);
      return {
        success: false,
        message:
          error.response?.data?.message ||
          "Failed to fetch employee dashboard statistics",
        data: {
          totalPurchases: 0,
          totalSpent: 0,
          productsPurchased: 0,
          activeSuppliers: 0,
          recentActivity: [],
        },
      };
    }
  },

  /**
   * Get monthly purchase analytics for employee
   * @returns {Promise<Object>} - Promise resolving to API response with monthly data
   */
  getMonthlyPurchaseAnalytics: async () => {
    try {
      // Fetch employee's own purchases for analysis
      const response = await purchaseService.getMyPurchases({
        page: 0,
        size: 100,
        sortBy: "purchaseDate",
        sortDir: "desc",
      });

      if (!response.success || !response.data?.content) {
        return {
          success: false,
          message: "No purchase data available",
          data: null,
        };
      }

      const purchases = response.data.content;

      // Create monthly data for the last 12 months
      const monthlyData = {};
      const currentDate = new Date();

      // Initialize last 12 months with zero values
      for (let i = 11; i >= 0; i--) {
        const date = new Date(
          currentDate.getFullYear(),
          currentDate.getMonth() - i,
          1
        );
        const monthKey = `${date.getFullYear()}-${String(
          date.getMonth() + 1
        ).padStart(2, "0")}`;
        monthlyData[monthKey] = {
          month: date.toLocaleDateString("en-US", {
            month: "short",
            year: "numeric",
          }),
          purchases: 0,
          amount: 0,
        };
      }

      // Process purchases and group by month
      purchases.forEach((purchase) => {
        if (purchase.purchaseDate) {
          const purchaseDate = new Date(purchase.purchaseDate);
          const monthKey = `${purchaseDate.getFullYear()}-${String(
            purchaseDate.getMonth() + 1
          ).padStart(2, "0")}`;

          if (monthlyData[monthKey]) {
            monthlyData[monthKey].purchases += 1;
            monthlyData[monthKey].amount += purchase.totalAmount || 0;
          }
        }
      });

      // Convert to array format
      const monthlyArray = Object.values(monthlyData);

      return {
        success: true,
        message: "Monthly purchase analytics retrieved successfully",
        data: monthlyArray,
      };
    } catch (error) {
      console.error("Error fetching monthly purchase analytics:", error);
      return {
        success: false,
        message:
          error.response?.data?.message ||
          "Failed to fetch monthly purchase analytics",
        data: null,
      };
    }
  },
};

export default employeeAnalyticsService;
