import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:8080";

/**
 * Service for supplier product-related API calls
 */
export const supplierProductService = {
  /**
   * Get all products for the authenticated supplier
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  getMyProducts: async () => {
    try {
      // Get the current auth token directly from localStorage
      const token = localStorage.getItem("accessToken");
      if (!token) {
        return {
          success: false,
          message: "Authentication token not found. Please log in again.",
          data: [],
        };
      }

      // Create a direct axios request with the token
      // This bypasses any potential issues with the authApi interceptors
      const response = await axios.get(
        `${API_URL}/api/v1/products/my-inventory`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      // Handle empty response
      if (!response.data) {
        return {
          success: false,
          message: "No response data received",
          data: [],
        };
      }

      // Check if the response has a paginated structure (data.content)
      if (response.data.data && response.data.data.content) {
        // Extract pagination information
        const paginationInfo = {
          totalPages: response.data.data.totalPages || 1,
          totalElements: response.data.data.totalElements || 0,
          currentPage: response.data.data.number || 0,
          pageSize: response.data.data.size || 10,
        };

        // Return the content array along with pagination info
        return {
          success: response.data.success || false,
          message: response.data.message || "Products retrieved",
          data: response.data.data.content || [],
          pagination: paginationInfo,
        };
      }

      // If not paginated, return the data as is
      return {
        success: response.data.success || false,
        message: response.data.message || "Products retrieved",
        data: response.data.data || [],
      };
    } catch (error) {
      console.error("Error fetching supplier products:", error);

      // Log more details about the error
      if (error.response) {
        console.error("Error response status:", error.response.status);
        console.error("Error response data:", error.response.data);

        // Handle specific error codes
        if (error.response.status === 401) {
          return {
            success: false,
            message: "Your session has expired. Please log in again.",
            data: [],
          };
        } else if (error.response.status === 403) {
          return {
            success: false,
            message: "You don't have permission to access supplier products.",
            data: [],
          };
        }

        // Return a structured error response
        return {
          success: false,
          message:
            error.response.data?.message ||
            "Failed to fetch products. Please try again later.",
          data: [],
        };
      } else if (error.request) {
        console.error("No response received from server");
        return {
          success: false,
          message: "Network error. Please check your connection and try again.",
          data: [],
        };
      } else {
        console.error("Error setting up request:", error.message);
        return {
          success: false,
          message: "An unexpected error occurred. Please try again later.",
          data: [],
        };
      }
    }
  },

  /**
   * Get a specific product by ID for the authenticated supplier
   * @param {string} productId - Product ID
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  getMyProduct: async (productId) => {
    try {
      // Get the current auth token directly from localStorage
      const token = localStorage.getItem("accessToken");
      if (!token) {
        console.error("No authentication token found");
        return {
          success: false,
          message: "Authentication token not found. Please log in again.",
          data: null,
        };
      }

      const response = await axios.get(
        `${API_URL}/api/v1/products/my-inventory/${productId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      // The backend returns a structured response with success, message, and data fields
      return {
        success: response.data.success || false,
        message: response.data.message || "Product retrieved",
        data: response.data.data || null,
      };
    } catch (error) {
      return {
        success: false,
        message:
          error.response?.data?.message || "Failed to fetch product details",
        data: null,
      };
    }
  },

  /**
   * Add a new product for the authenticated supplier
   * @param {Object} productData - Product data
   * @param {string} productData.label - Product label/name
   * @param {string} productData.description - Product description
   * @param {number} productData.stockQuantity - Stock quantity
   * @param {number} productData.purchasePrice - Purchase price
   * @param {number} productData.sellingPrice - Selling price
   * @param {number} productData.deliveryTime - Delivery time in days
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  addProduct: async (productData) => {
    try {
      // Get the current auth token directly from localStorage
      const token = localStorage.getItem("accessToken");
      if (!token) {
        return {
          success: false,
          message: "Authentication token not found. Please log in again.",
          data: null,
        };
      }

      const response = await axios.post(
        `${API_URL}/api/v1/products`,
        productData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      // The backend returns a structured response with success, message, and data fields
      return {
        success: response.data.success || false,
        message: response.data.message || "Product added successfully",
        data: response.data.data || null,
      };
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || "Failed to add product",
        data: null,
      };
    }
  },

  /**
   * Update an existing product for the authenticated supplier
   * @param {string} productId - Product ID
   * @param {Object} productData - Product data to update
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  updateProduct: async (productId, productData) => {
    try {
      // Get the current auth token directly from localStorage
      const token = localStorage.getItem("accessToken");
      if (!token) {
        return {
          success: false,
          message: "Authentication token not found. Please log in again.",
          data: null,
        };
      }

      const response = await axios.patch(
        `${API_URL}/api/v1/products/${productId}`,
        productData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      // The backend returns a structured response with success, message, and data fields
      return {
        success: response.data.success || false,
        message: response.data.message || "Product updated successfully",
        data: response.data.data || null,
      };
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || "Failed to update product",
        data: null,
      };
    }
  },

  /**
   * Delete a product for the authenticated supplier
   * @param {string} productId - Product ID
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  deleteProduct: async (productId) => {
    try {
      // Validate productId
      if (!productId) {
        return {
          success: false,
          message: "Product ID is required for deletion",
          data: null,
        };
      }

      // Get the current auth token directly from localStorage
      const token = localStorage.getItem("accessToken");
      if (!token) {
        return {
          success: false,
          message: "Authentication token not found. Please log in again.",
          data: null,
        };
      }

      const response = await axios.delete(
        `${API_URL}/api/v1/products/${productId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      // The backend returns a structured response with success, message, and data fields
      return {
        success: response.data.success || false,
        message: response.data.message || "Product deleted successfully",
        data: response.data.data || null,
      };
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || "Failed to delete product",
        data: null,
      };
    }
  },

  /**
   * Get products for a specific supplier
   * @param {string} supplierId - Supplier ID
   * @param {Object} params - Query parameters
   * @param {string} params.query - Search query
   * @param {number} params.page - Page number (0-based)
   * @param {number} params.size - Page size
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  getSupplierProducts: async (supplierId, params = {}) => {
    try {
      // Get the current auth token directly from localStorage
      const token = localStorage.getItem("accessToken");
      if (!token) {
        return {
          success: false,
          message: "Authentication token not found. Please log in again.",
          data: null,
        };
      }

      const { query = "", page = 0, size = 10 } = params;

      // Call the real API endpoint to get products for the supplier
      // The endpoint is defined in ProductController.java as "/api/v1/products/supplier/{supplierId}"
      const response = await axios.get(
        `${API_URL}/api/v1/products/supplier/${supplierId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          params: {
            query,
            page,
            size,
          },
        }
      );

      // Handle empty response
      if (!response.data) {
        return {
          success: false,
          message: "No response data received",
          data: { content: [] },
        };
      }

      // Log the response data for debugging
      console.log(
        "Supplier products API response:",
        JSON.stringify(response.data, null, 2)
      );

      // If we have data, log the first product to see its structure
      if (
        response.data.data &&
        response.data.data.content &&
        response.data.data.content.length > 0
      ) {
        console.log(
          "First product structure:",
          JSON.stringify(response.data.data.content[0], null, 2)
        );
      }

      // Return the structured response
      return {
        success: response.data.success || false,
        message: response.data.message || "Products retrieved",
        data: response.data.data || { content: [] },
      };
    } catch (error) {
      console.error("Error fetching supplier products:", error);

      // Log more details about the error
      if (error.response) {
        console.error("Error response status:", error.response.status);
        console.error("Error response data:", error.response.data);

        // Handle specific error codes
        if (error.response.status === 401) {
          return {
            success: false,
            message: "Your session has expired. Please log in again.",
            data: { content: [] },
          };
        } else if (error.response.status === 403) {
          return {
            success: false,
            message: "You don't have permission to access supplier products.",
            data: { content: [] },
          };
        } else if (error.response.status === 404) {
          console.error("API endpoint not found");
          return {
            success: false,
            message: "API endpoint not found. Please contact support.",
            data: { content: [] },
          };
        }

        // Return a structured error response
        return {
          success: false,
          message:
            error.response.data?.message ||
            "Failed to fetch supplier products. Please try again later.",
          data: { content: [] },
        };
      } else if (error.request) {
        console.error("No response received from server");
        return {
          success: false,
          message: "Network error. Please check your connection and try again.",
          data: { content: [] },
        };
      } else {
        console.error("Error setting up request:", error.message);
        return {
          success: false,
          message: "An unexpected error occurred. Please try again later.",
          data: { content: [] },
        };
      }
    }
  },

  /**
   * Upload a product image
   * @param {File} imageFile - Image file to upload
   * @returns {Promise<Object>} - Promise resolving to response with image URL
   */
  uploadProductImage: async (imageFile) => {
    try {
      // Get the current auth token directly from localStorage
      const token = localStorage.getItem("accessToken");
      if (!token) {
        return {
          success: false,
          message: "Authentication token not found. Please log in again.",
          data: null,
        };
      }

      const formData = new FormData();
      formData.append("file", imageFile);

      const response = await axios.post(
        `${API_URL}/api/files/product-pictures`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "multipart/form-data",
          },
        }
      );

      // The FileController returns a direct URL string, not a structured response
      if (response.data) {
        // Check if the response is a string (direct URL)
        if (typeof response.data === "string") {
          return {
            success: true,
            message: "Image uploaded successfully",
            data: response.data,
          };
        }

        // For backward compatibility, also check if it's a structured response
        if (response.data.success && response.data.data) {
          return response.data;
        }
      }
      return {
        success: false,
        message: "Invalid response format from server",
        data: null,
      };
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || "Failed to upload image",
        data: null,
      };
    }
  },
};

export default supplierProductService;
