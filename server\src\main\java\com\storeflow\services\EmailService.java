package com.storeflow.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmailService {

    private final JavaMailSender mailSender;

    @Value("${application.email.from}")
    private String fromEmail;

    @Value("${application.frontend.url}")
    private String frontendUrl;

    public void sendPasswordResetCode(String toEmail, String verificationCode, String userName) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(toEmail);
            message.setSubject("Your StoreFlow Password Reset Code");

            String emailBody = String.format(
                "Hello %s,\n\n" +
                "Your password reset verification code is:\n\n" +
                "%s\n\n" +
                "This code will expire in 10 minutes.\n\n" +
                "If you didn't request this, please ignore this email.\n\n" +
                "StoreFlow Team",
                userName, verificationCode
            );

            message.setText(emailBody);

            mailSender.send(message);
            log.info("Password reset code sent successfully to: {}", toEmail);

        } catch (Exception e) {
            log.error("Failed to send password reset code to: {}", toEmail, e);
            throw new RuntimeException("Failed to send password reset code", e);
        }
    }

    public void sendPasswordResetConfirmationEmail(String toEmail, String userName) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(toEmail);
            message.setSubject("Password Reset Successful - StoreFlow");

            String emailBody = String.format(
                "Dear %s,\n\n" +
                "Your password has been successfully reset for your StoreFlow account.\n\n" +
                "If you did not make this change, please contact our support team immediately.\n\n" +
                "For your security, we recommend:\n" +
                "- Using a strong, unique password\n" +
                "- Not sharing your password with anyone\n" +
                "- Logging out of shared devices\n\n" +
                "Best regards,\n" +
                "The StoreFlow Team",
                userName
            );

            message.setText(emailBody);

            mailSender.send(message);
            log.info("Password reset confirmation email sent successfully to: {}", toEmail);

        } catch (Exception e) {
            log.error("Failed to send password reset confirmation email to: {}", toEmail, e);
            // Don't throw exception here as the password reset was successful
            // Just log the error
        }
    }

    public void sendPasswordEmail(String toEmail, String password) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(toEmail);
            message.setSubject("Your StoreFlow Account Password");

            String emailBody = String.format(
                "Welcome to StoreFlow!\n\n" +
                "Your account has been successfully created using Google.\n\n" +
                "For security purposes, we've generated a secure password for your account:\n\n" +
                "Password: %s\n\n" +
                "You can use this password to:\n" +
                "- Log in directly with your email and password\n" +
                "- Access your account if Google is unavailable\n" +
                "- Change your password in your account settings\n\n" +
                "For your security:\n" +
                "- Keep this password safe and secure\n" +
                "- Consider changing it to something memorable in your account settings\n" +
                "- Don't share this password with anyone\n\n" +
                "You can continue to use Google OAuth for quick login, or use your email and this password.\n\n" +
                "Welcome to StoreFlow!\n\n" +
                "Best regards,\n" +
                "The StoreFlow Team",
                password
            );

            message.setText(emailBody);

            mailSender.send(message);
            log.info("Password email sent successfully to: {}", toEmail);

        } catch (Exception e) {
            log.error("Failed to send password email to: {}", toEmail, e);
            throw new RuntimeException("Failed to send password email", e);
        }
    }
}
