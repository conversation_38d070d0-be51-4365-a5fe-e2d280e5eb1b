import { ripple } from "@/utils";
import { Link } from "react-router-dom";
import { HashLink } from "react-router-hash-link";

export const MobileNavigation = ({ isOpen, closeMenu }) => {
  return (
    <div
      className={`fixed top-[104px] items-center grow text-xl font-medium bg-black/90 rounded-b-2xl w-full transition-all duration-300 ease-in-out ${
        isOpen
          ? "opacity-100 max-h-96 pb-8"
          : "opacity-0 max-h-0 overflow-hidden pointer-events-none"
      }`}
    >
      <div className="flex items-center size-full flex-col">
        <HashLink
          to="/#about"
          className="grow w-full"
          smooth
          onClick={closeMenu}
        >
          <div className="hover:text-crimson duration-400 text-center pb-8">
            About
          </div>
        </HashLink>
        <HashLink
          to="/#services"
          className="grow w-full"
          smooth
          onClick={closeMenu}
        >
          <div className="hover:text-crimson duration-400 text-center pb-8">
            Services
          </div>
        </HashLink>
        <HashLink
          to="/#products"
          className="grow w-full"
          smooth
          onClick={closeMenu}
        >
          <div className="hover:text-crimson duration-400 text-center pb-8">
            Products
          </div>
        </HashLink>
      </div>

      <div className="grid place-items-center px-8">
        <hr className="border-gray-200 w-full" />
      </div>

      <div className="flex flex-col gap-y-0.5 grow items-center justify-center text-xl font-medium mt-8">
        <Link
          to="/auth/login"
          className="self-stretch px-8"
          onClick={closeMenu}
        >
          <button
            onMouseDown={(e) => ripple.create(e, "dark")}
            className="bg-white border border-white text-black rounded-full w-full py-3 text-center"
          >
            Sign in
          </button>
        </Link>
        <Link
          to="/auth/signup"
          className="self-stretch px-8"
          onClick={closeMenu}
        >
          <button className="text-white rounded-full w-full py-3 text-center">
            Sign up
          </button>
        </Link>
      </div>
    </div>
  );
};

export default MobileNavigation;
