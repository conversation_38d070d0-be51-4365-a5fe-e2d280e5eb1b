package com.storeflow.services;

import com.storeflow.DTOs.CompanyProductDTO;
import com.storeflow.exception.ResourceNotFoundException;
import com.storeflow.models.CompanyProduct;
import com.storeflow.models.Product;
import com.storeflow.repositories.CompanyProductRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service for managing company products (internal inventory).
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CompanyProductService {

    private final CompanyProductRepository companyProductRepository;

    /**
     * Add stock to company inventory from a purchase.
     *
     * @param product The product
     * @param label The product label
     * @param description The product description
     * @param quantity The quantity purchased
     * @param costPrice The cost price per unit
     * @param sellingPrice The selling price per unit
     * @param deliveryTime The delivery time
     * @return The updated or created company product
     */
    @Transactional
    public CompanyProduct addStockFromPurchase(Product product, String label, String description,
                                             Integer quantity, BigDecimal costPrice,
                                             BigDecimal sellingPrice, Integer deliveryTime) {
        log.info("Adding stock to company inventory: {} units of {}", quantity, label);

        Optional<CompanyProduct> existingProduct = companyProductRepository.findByLabel(label);

        if (existingProduct.isPresent()) {
            // Update existing company product
            CompanyProduct companyProduct = existingProduct.get();
            companyProduct.addStock(quantity, costPrice);

            // Update selling price and delivery time (use latest values)
            companyProduct.setSellingPrice(sellingPrice);
            companyProduct.setAverageDeliveryTime(deliveryTime);
            companyProduct.setDescription(description); // Update description in case it changed

            log.info("Updated existing company product: {} (new stock: {})",
                label, companyProduct.getStockQuantity());

            return companyProductRepository.save(companyProduct);
        } else {
            // Create new company product
            CompanyProduct companyProduct = CompanyProduct.builder()
                .product(product)
                .label(label)
                .description(description)
                .stockQuantity(quantity)
                .averageCostPrice(costPrice)
                .sellingPrice(sellingPrice)
                .averageDeliveryTime(deliveryTime)
                .build();

            log.info("Created new company product: {} with {} units", label, quantity);

            return companyProductRepository.save(companyProduct);
        }
    }

    /**
     * Remove stock from company inventory (when clients order).
     *
     * @param label The product label
     * @param quantity The quantity to remove
     * @return true if successful, false if insufficient stock
     */
    @Transactional
    public boolean removeStockFromOrder(String label, Integer quantity) {
        log.info("Removing {} units of '{}' from company inventory", quantity, label);

        Optional<CompanyProduct> companyProductOpt = companyProductRepository.findByLabel(label);

        if (companyProductOpt.isEmpty()) {
            log.error("Company product not found with label: '{}'", label);
            return false;
        }

        CompanyProduct companyProduct = companyProductOpt.get();
        log.info("Found company product: '{}' with current stock: {}",
            companyProduct.getLabel(), companyProduct.getStockQuantity());

        boolean success = companyProduct.removeStock(quantity);

        if (success) {
            companyProductRepository.save(companyProduct);
            log.info("Successfully removed {} units of '{}' (remaining: {})",
                quantity, label, companyProduct.getStockQuantity());
        } else {
            log.error("Insufficient stock for '{}': requested {}, available {}",
                label, quantity, companyProduct.getStockQuantity());
        }

        return success;
    }

    /**
     * Get all company products with stock greater than zero.
     *
     * @return List of available company products
     */
    @Transactional(readOnly = true)
    public List<CompanyProduct> getAvailableProducts() {
        return companyProductRepository.findByStockQuantityGreaterThan(0);
    }

    /**
     * Get a company product by label.
     *
     * @param label The product label
     * @return The company product or null if not found
     */
    @Transactional(readOnly = true)
    public CompanyProduct getProductByLabel(String label) {
        return companyProductRepository.findByLabel(label).orElse(null);
    }

    /**
     * Get a company product by label with stock check.
     *
     * @param label The product label
     * @return The company product if available, null otherwise
     */
    @Transactional(readOnly = true)
    public CompanyProduct getAvailableProductByLabel(String label) {
        return companyProductRepository.findByLabelAndStockQuantityGreaterThan(label, 0).orElse(null);
    }

    /**
     * Get all company products with pagination.
     *
     * @param pageable The pagination information
     * @return Page of company products
     */
    @Transactional(readOnly = true)
    public Page<CompanyProduct> getAllProducts(Pageable pageable) {
        return companyProductRepository.findAll(pageable);
    }

    /**
     * Search company products.
     *
     * @param searchTerm The search term
     * @param pageable The pagination information
     * @return Page of matching company products
     */
    @Transactional(readOnly = true)
    public Page<CompanyProduct> searchProducts(String searchTerm, Pageable pageable) {
        return companyProductRepository.searchByLabelOrDescriptionOrBrand(searchTerm, pageable);
    }

    /**
     * Convert CompanyProduct to DTO.
     *
     * @param companyProduct The company product entity
     * @return The DTO
     */
    public CompanyProductDTO toDTO(CompanyProduct companyProduct) {
        return new CompanyProductDTO(
            companyProduct.getId(),
            companyProduct.getProduct().getId(),
            companyProduct.getLabel(),
            companyProduct.getDescription(),
            companyProduct.getProduct().getBrand(),
            companyProduct.getProduct().getImageUrl(),
            companyProduct.getStockQuantity(),
            companyProduct.getAverageCostPrice(),
            companyProduct.getSellingPrice(),
            companyProduct.getMargin(),
            companyProduct.getAverageDeliveryTime(),
            companyProduct.getCreatedDate(),
            companyProduct.getLastModifiedDate()
        );
    }

    /**
     * Convert list of CompanyProducts to DTOs.
     *
     * @param companyProducts The list of company products
     * @return List of DTOs
     */
    public List<CompanyProductDTO> toDTOs(List<CompanyProduct> companyProducts) {
        return companyProducts.stream()
            .map(this::toDTO)
            .collect(Collectors.toList());
    }

    /**
     * Get total inventory count.
     *
     * @return Total number of company products
     */
    @Transactional(readOnly = true)
    public long getTotalProductCount() {
        return companyProductRepository.count();
    }

    /**
     * Get in-stock inventory count.
     *
     * @return Number of company products with stock > 0
     */
    @Transactional(readOnly = true)
    public long getInStockProductCount() {
        return companyProductRepository.countInStock();
    }

    /**
     * Get total inventory value.
     *
     * @return Total value of company inventory
     */
    @Transactional(readOnly = true)
    public BigDecimal getTotalInventoryValue() {
        return companyProductRepository.calculateTotalInventoryValue();
    }
}
