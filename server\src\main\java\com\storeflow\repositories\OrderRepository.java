package com.storeflow.repositories;

import com.storeflow.enums.OrderStatus;
import com.storeflow.models.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Repository for managing Order entities.
 */
@Repository
public interface OrderRepository extends JpaRepository<Order, UUID> {

    /**
     * Find all orders for a specific client.
     *
     * @param clientId The ID of the client
     * @return A list of orders for the client
     */
    List<Order> findByClientId(UUID clientId);

    /**
     * Find all orders for a specific client with pagination.
     *
     * @param clientId The ID of the client
     * @param pageable Pagination information
     * @return A page of orders for the client
     */
    Page<Order> findByClientId(UUID clientId, Pageable pageable);

    /**
     * Find all orders with a specific status.
     *
     * @param status The order status
     * @return A list of orders with the specified status
     */
    List<Order> findByStatus(OrderStatus status);

    /**
     * Find all orders with a specific status and client.
     *
     * @param status The order status
     * @param clientId The ID of the client
     * @return A list of orders with the specified status and client
     */
    List<Order> findByStatusAndClientId(OrderStatus status, UUID clientId);

    /**
     * Find orders created between two dates.
     *
     * @param startDate The start date
     * @param endDate The end date
     * @return A list of orders created between the dates
     */
    List<Order> findByOrderDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Count orders by status.
     *
     * @param status The order status
     * @return The count of orders with the specified status
     */
    long countByStatus(OrderStatus status);

    /**
     * Count orders for a specific client.
     *
     * @param clientId The ID of the client
     * @return The count of orders for the client
     */
    long countByClientId(UUID clientId);

    /**
     * Get monthly order statistics for a client.
     *
     * @param clientId The ID of the client
     * @param startDate The start date
     * @param endDate The end date
     * @return A list of objects containing month, year, total orders, and total amount
     */
    @Query("""
        SELECT
            MONTH(o.orderDate) as month,
            YEAR(o.orderDate) as year,
            COUNT(o) as totalOrders,
            SUM(o.totalAmount) as totalAmount
        FROM Order o
        WHERE o.client.id = :clientId
            AND o.status = 'COMPLETED'
            AND o.orderDate BETWEEN :startDate AND :endDate
        GROUP BY YEAR(o.orderDate), MONTH(o.orderDate)
        ORDER BY YEAR(o.orderDate), MONTH(o.orderDate)
    """)
    List<Object[]> getMonthlyOrderStatistics(
        @Param("clientId") UUID clientId,
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );
}
