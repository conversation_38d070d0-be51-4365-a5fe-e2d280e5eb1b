import { useEffect, useState } from "react";
import { Routes, Route, Navigate, useNavigate } from "react-router-dom";
import { useAuth } from "@/context/auth-context";
import { useSelector } from "react-redux";
import { Sidebar, Navbar, Configurator } from "@/components/portals";
import { routes } from "@/routes/supplier";

export const Supplier = () => {
  const { isAuthenticated, loading: authLoading, getUserRole } = useAuth();
  const navigate = useNavigate();
  const [firstName, setFirstName] = useState("");
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [profilePicture, setProfilePicture] = useState("");

  // Get user data from Redux store
  const { userData: user, loading: reduxLoading } = useSelector(
    (state) => state.user
  );

  // Update user info when user data changes
  useEffect(() => {
    if (user) {
      setFirstName(user.firstName || "");
      setFullName(`${user.firstName || ""} ${user.lastName || ""}`.trim());
      setEmail(user.email || "");
      setProfilePicture(user.profilePictureUrl || "");
    }
  }, [user]);

  // Redirect if not authenticated or not a supplier
  useEffect(() => {
    if (!authLoading && !isAuthenticated()) {
      navigate("/auth/login", { replace: true });
    } else if (!authLoading && isAuthenticated()) {
      // Get role directly from auth context
      const role = getUserRole();

      if (role !== "supplier") {
        // Redirect to appropriate portal based on role
        switch (role) {
          case "admin":
            navigate("/admin/dashboard", { replace: true });
            break;
          case "client":
            navigate("/client/dashboard", { replace: true });
            break;
          case "employee":
            navigate("/employee/dashboard", { replace: true });
            break;
          default:
            navigate("/auth/login", { replace: true });
        }
      }
    }
  }, [isAuthenticated, authLoading, getUserRole, navigate]);

  // Show loading state while checking authentication
  if (authLoading || reduxLoading || !user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <main className="min-h-dvh bg-blue-gray-50/50">
      <Sidebar userRole="supplier" routes={routes} firstName={firstName} />
      <div className="p-4 lg:ml-72">
        <Navbar
          userRole="supplier"
          profilePicture={profilePicture}
          fullName={fullName}
          email={email}
        />
        <Configurator />
        <Routes>
          {routes.map(({ path, element }, index) => {
            return <Route key={index} path={path} element={element} />;
          })}
          {/* Catch-all route for undefined paths */}
          <Route
            path="*"
            element={<Navigate to="/supplier/dashboard" replace />}
          />
        </Routes>
      </div>
    </main>
  );
};

export default Supplier;
