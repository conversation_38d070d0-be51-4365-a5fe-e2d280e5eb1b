import {
  Login,
  Signup,
  ForgotPassword,
  ResetPassword,
  CompleteGoogleRegistration,
  OAuth2Success,
  OAuth2Error,
} from "@/components/auth";

export const routes = [
  {
    name: "log in",
    path: "/login",
    element: <Login />,
  },

  {
    name: "sign up",
    path: "/signup",
    element: <Signup />,
  },

  {
    name: "forgot password",
    path: "/forgot-password",
    element: <ForgotPassword />,
  },

  {
    name: "reset password",
    path: "/reset-password",
    element: <ResetPassword />,
  },
  {
    name: "complete google registration",
    path: "/complete-google-registration",
    element: <CompleteGoogleRegistration />,
  },
  {
    name: "oauth2 success",
    path: "/oauth2/success",
    element: <OAuth2Success />,
  },
  {
    name: "oauth2 error",
    path: "/oauth2/error",
    element: <OAuth2Error />,
  },
];

export default routes;
