package com.storeflow.services;

import com.storeflow.DTOs.EmployeeDTO;
import com.storeflow.enums.Role;
import com.storeflow.exception.*;
import com.storeflow.mappers.EmployeeMapper;
import com.storeflow.models.Employee;
import com.storeflow.models.User;
import com.storeflow.repositories.EmployeeRepository;
import com.storeflow.repositories.TokenRepository;
import com.storeflow.repositories.UserRepository;
import com.storeflow.requests.EmployeeAdditionRequest;
import com.storeflow.utils.PhoneNumberFormatter;
import com.storeflow.utils.SecurePasswordGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmployeeService {
    private final FileService fileService;
    private final UserRepository userRepository;
    private final TokenRepository tokenRepository;
    private final PasswordEncoder passwordEncoder;
    private final EmployeeRepository employeeRepository;
    private final PhoneNumberFormatter phoneNumberFormatter;
    private final SecurePasswordGenerator securePasswordGenerator;
    private final EmployeeMapper employeeMapper;

    @Transactional
    public UUID addEmployee(EmployeeAdditionRequest employeeDetails) {
        // Check if email already exists
        String employeeEmail = employeeDetails.email();
        if (userRepository.existsByEmail(employeeEmail)) {
            log.warn("Registration attempt with existing email: {}", employeeEmail);
            throw new DuplicateResourceException("Email already exists");
        }

        // Check if phone number already exists
        String employeeFormattedPhoneNumber = phoneNumberFormatter.formatToInternational(employeeDetails.phoneNumber());
        if (userRepository.existsByPhoneNumber(employeeFormattedPhoneNumber)) {
            log.warn("Registration attempt with existing phone number: {}", employeeFormattedPhoneNumber);
            throw new DuplicateResourceException("Phone number already exists");
        }

        // Generate a secure password
        String generatedPassword = securePasswordGenerator.generateSecurePassword();

        // Create the base user
        User user = User.builder()
            .firstName(employeeDetails.firstName().trim())
            .lastName(employeeDetails.lastName().trim())
            .email(employeeEmail)
            .phoneNumber(employeeFormattedPhoneNumber)
            .password(passwordEncoder.encode(generatedPassword))
            .role(Role.EMPLOYEE)
            .profilePictureUrl(fileService.getDefaultProfilePictureUrl())
            .build();

        userRepository.save(user);

        // Create the employee
        Employee employee = Employee.builder()
            .user(user)
            .build();

        employeeRepository.save(employee);

        // Log credentials in development mode
        log.info("""
        \n
        ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
          EMPLOYEE CREATED - CREDENTIALS (DEV MODE)       \s
                                                          \s
          Phone Number: {}                                \s
          Email: {}                                       \s
          Password: {}                                    \s
        ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛""",
            user.getPhoneNumber(), user.getEmail(), generatedPassword);

        return user.getId();
    }

    /**
     * Get all employees with pagination and search functionality
     *
     * @param searchQuery The search query to filter employees (can be null or empty)
     * @param pageable The pagination information
     * @return A page of employee DTOs
     */
    @Transactional(readOnly = true)
    public Page<EmployeeDTO> getAllEmployees(String searchQuery, Pageable pageable) {
        log.info("Fetching employees with search query: '{}', page: {}, size: {}",
                searchQuery, pageable.getPageNumber(), pageable.getPageSize());

        Page<Employee> employeePage;

        if (StringUtils.hasText(searchQuery)) {
            // If search query is provided, use the search method
            employeePage = employeeRepository.findBySearchQuery(searchQuery, pageable);
            log.debug("Found {} employees matching search query '{}'", employeePage.getTotalElements(), searchQuery);
        } else {
            // Otherwise, get all employees with pagination
            employeePage = employeeRepository.findAll(pageable);
            log.debug("Found {} total employees", employeePage.getTotalElements());
        }

        // Map to DTOs and return
        return employeeMapper.toEmployeeDTOPage(employeePage);
    }

    /**
     * Delete an employee by ID
     *
     * @param employeeId The ID of the employee to delete
     * @throws ResourceNotFoundException if the employee doesn't exist
     */
    @Transactional
    public void deleteEmployee(UUID employeeId) {
        log.info("Request to delete employee with ID: {}", employeeId);

        // Find the employee
        Employee employee = employeeRepository.findById(employeeId)
            .orElseThrow(() -> {
                log.warn("Attempted to delete non-existent employee with ID: {}", employeeId);
                return new ResourceNotFoundException("Employee not found with ID: " + employeeId);
            });

        // Get the associated user
        User user = employee.getUser();

        // Delete all tokens associated with the user first
        tokenRepository.deleteAllTokensByUserId(user.getId());
        log.debug("Deleted all tokens for user with ID: {}", user.getId());

        // Delete the employee (due to foreign key constraints)
        employeeRepository.delete(employee);
        log.debug("Deleted employee record with ID: {}", employeeId);

        // Finally delete the user
        userRepository.delete(user);
        log.debug("Deleted user record with ID: {}", user.getId());

        log.info("Employee with ID: {} successfully deleted", employeeId);
    }
}
