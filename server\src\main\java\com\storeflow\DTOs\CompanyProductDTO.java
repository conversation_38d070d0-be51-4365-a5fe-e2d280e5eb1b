package com.storeflow.DTOs;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for CompanyProduct entity.
 */
public record CompanyProductDTO(
    UUID id,
    UUID productId,
    String label,
    String description,
    String brand,
    String imageUrl,
    Integer stockQuantity,
    BigDecimal averageCostPrice,
    BigDecimal sellingPrice,
    BigDecimal margin,
    Integer averageDeliveryTime,
    LocalDateTime createdDate,
    LocalDateTime lastModifiedDate
) {
}
