package com.storeflow.controllers;

import com.storeflow.DTOs.SupplierDTO;
import com.storeflow.DTOs.SupplierProductDTO;
import com.storeflow.mappers.SupplierProductMapper;
import com.storeflow.requests.SupplierProductAdditionRequest;
import com.storeflow.requests.SupplierProductUpdateRequest;
import com.storeflow.responses.ApiResponse;
import com.storeflow.services.ProductService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.security.Principal;
import java.util.List;
import java.util.UUID;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/products")
public class ProductController {
    private final ProductService productService;
    private final SupplierProductMapper supplierProductMapper;

    @PostMapping
//    @PreAuthorize("hasRole('SUPPLIER')")
    public ResponseEntity<ApiResponse<UUID>> addProduct(
        Principal principal,
        @Valid @RequestBody SupplierProductAdditionRequest request) {
        log.info("Received request to add new product: {}", request.label());
        UUID productId = productService.addProduct(principal, request);

        return ResponseEntity.status(HttpStatus.CREATED)
            .body(new ApiResponse<>(
                true,
                "Product added successfully",
                productId
            ));
    }

    @PatchMapping("/{productId}")
//    @PreAuthorize("hasRole('SUPPLIER')")
    public ResponseEntity<ApiResponse<UUID>> updateProduct(
        Principal principal,
        @PathVariable UUID productId,
        @Valid @RequestBody SupplierProductUpdateRequest request) {
        log.info("Received request to update product: {}", productId);
        UUID updatedProductId = productService.updateProduct(principal, productId, request);

        return ResponseEntity.ok(new ApiResponse<>(
            true,
            "Product updated successfully",
            updatedProductId
        ));
    }

    @DeleteMapping("/{productId}")
//    @PreAuthorize("hasRole('SUPPLIER')")
    public ResponseEntity<ApiResponse<Void>> deleteProduct(
        Principal principal,
        @PathVariable UUID productId) {
        log.info("Received request to delete product: {}", productId);
        productService.deleteProduct(principal, productId);

        return ResponseEntity.ok(new ApiResponse<>(
            true,
            "Product deleted successfully",
            null
        ));
    }

    @GetMapping("/{productId}/suppliers")
    public ResponseEntity<ApiResponse<List<SupplierDTO>>> getProductSuppliers(
        @PathVariable UUID productId) {
        log.info("Received request to get suppliers for product: {}", productId);
        List<SupplierDTO> suppliers = productService.getProductSuppliers(productId);

        return ResponseEntity.ok(new ApiResponse<>(
            true,
            "Suppliers retrieved successfully",
            suppliers
        ));
    }

    /**
     * @deprecated Use {@link #getMyProductsPaginated} instead for pagination and search
     */
    @Deprecated
    @GetMapping("/my-inventory/all")
//    @PreAuthorize("hasRole('SUPPLIER')")
    public ResponseEntity<ApiResponse<List<SupplierProductDTO>>> getMyProducts(Principal principal) {
        log.info("Received request to get all products for supplier (deprecated endpoint)");
        List<SupplierProductDTO> products = supplierProductMapper.toSupplierProductDTOs(
            productService.getSupplierProducts(principal));

        return ResponseEntity.ok(new ApiResponse<>(
            true,
            "Products retrieved successfully",
            products
        ));
    }

    @GetMapping("/my-inventory")
//    @PreAuthorize("hasRole('SUPPLIER')")
    public ResponseEntity<ApiResponse<Page<SupplierProductDTO>>> getMyProductsPaginated(
        Principal principal,
        @RequestParam(required = false, defaultValue = "") String query,
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(defaultValue = "createdDate") String sortBy,
        @RequestParam(defaultValue = "desc") String sortDir
    ) {
        log.info("Received request to get products for supplier with query: '{}', page: {}, size: {}",
                query, page, size);

        // Create sort object
//        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);

        // Create pageable object
        Pageable pageable = PageRequest.of(page, size);

        // Get products with pagination and search
        Page<SupplierProductDTO> products;

        if (query != null && !query.trim().isEmpty()) {
            products = supplierProductMapper.toSupplierProductDTOPage(
                productService.searchSupplierProducts(principal, query, pageable));
        } else {
            products = supplierProductMapper.toSupplierProductDTOPage(
                productService.getSupplierProducts(principal, pageable));
        }

        String message = products.isEmpty() ? "No products found." : "Products retrieved successfully.";

        return ResponseEntity.ok(new ApiResponse<>(
            true,
            message,
            products
        ));
    }

    @PostMapping(value = "/my-inventory/{productId}/image", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
//    @PreAuthorize("hasRole('SUPPLIER')")
    public ResponseEntity<ApiResponse<String>> uploadProductImage(
        Principal principal,
        @PathVariable UUID productId,
        @RequestParam("image") MultipartFile imageFile
    ) {
        log.info("Received request to upload image for product: {}", productId);

        if (imageFile.isEmpty()) {
            return ResponseEntity.badRequest().body(new ApiResponse<>(
                false,
                "No image file provided",
                null
            ));
        }

        String imageUrl = productService.uploadProductImage(principal, productId, imageFile);

        return ResponseEntity.ok(new ApiResponse<>(
            true,
            "Product image uploaded successfully",
            imageUrl
        ));
    }

    @GetMapping("/my-inventory/{productId}")
//    @PreAuthorize("hasRole('SUPPLIER')")
    public ResponseEntity<ApiResponse<SupplierProductDTO>> getMyProduct(
        Principal principal,
        @PathVariable UUID productId) {
        log.info("Received request to get product details for: {}", productId);
        SupplierProductDTO product = supplierProductMapper.toSupplierProductDTO(
            productService.getSupplierProduct(principal, productId));

        return ResponseEntity.ok(new ApiResponse<>(
            true,
            "Product retrieved successfully",
            product
        ));
    }

    /**
     * Get all products for a specific supplier with pagination and search.
     *
     * @param supplierId The ID of the supplier
     * @param query Optional search query
     * @param page Page number (0-based)
     * @param size Page size
     * @param sortBy Field to sort by
     * @param sortDir Sort direction ('asc' or 'desc')
     * @return A page of supplier products
     */
    @GetMapping("/supplier/{supplierId}")
//    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE')")
    public ResponseEntity<ApiResponse<Page<SupplierProductDTO>>> getProductsBySupplierId(
        @PathVariable UUID supplierId,
        @RequestParam(required = false, defaultValue = "") String query,
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(defaultValue = "label") String sortBy,
        @RequestParam(defaultValue = "asc") String sortDir
    ) {
        log.info("Received request to get products for supplier {} with query: '{}', page: {}, size: {}",
                supplierId, query, page, size);

        // Create pageable object
        Pageable pageable = PageRequest.of(page, size);

        // Get products with pagination and search
        Page<SupplierProductDTO> products;

        if (query != null && !query.trim().isEmpty()) {
            products = supplierProductMapper.toSupplierProductDTOPage(
                productService.searchProductsBySupplierId(supplierId, query, pageable));
        } else {
            products = supplierProductMapper.toSupplierProductDTOPage(
                productService.getProductsBySupplierId(supplierId, pageable));
        }

        String message = products.isEmpty() ? "No products found for this supplier." : "Products retrieved successfully.";

        return ResponseEntity.ok(new ApiResponse<>(
            true,
            message,
            products
        ));
    }
}
