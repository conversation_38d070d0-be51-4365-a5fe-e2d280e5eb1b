package com.storeflow.models;

import com.storeflow.embeddables.SupplierProductId;
import jakarta.persistence.*;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@Builder
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
@Table(
    name = "suppliers_products",
    indexes = {
        @Index(name = "supplier_product_primary_key", columnList = "supplier_id, product_id", unique = true),
        @Index(name = "supplier_product_label_index", columnList = "label")
    }
)
public class SupplierProduct implements Serializable {
    @EmbeddedId
    private SupplierProductId id;

    @ManyToOne
    @MapsId("supplierId")
    @JoinColumn(
        name = "supplier_id",
        foreignKey = @ForeignKey(name = "supplier_foreign_key")
    )
    private Supplier supplier;

    @ManyToOne
    @MapsId("productId")
    @JoinColumn(
        name = "product_id",
        foreignKey = @ForeignKey(name = "product_foreign_key")
    )
    private Product product;

    @Column(nullable = false)
    private String label;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String description;

    @Column(nullable = false)
    private Integer stockQuantity;

    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal purchasePrice;

    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal sellingPrice;

    @Column(nullable = false)
    private Integer deliveryTime;

    @CreatedDate
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdDate;

    @LastModifiedDate
    @Column(insertable = false)
    private LocalDateTime lastModifiedDate;

    @Transient
    private BigDecimal margin;

    public BigDecimal getMargin() {
        if (purchasePrice != null && sellingPrice != null && sellingPrice.compareTo(BigDecimal.ZERO) > 0) {
            return sellingPrice.subtract(purchasePrice)
                    .divide(sellingPrice, 2, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
        }
        return BigDecimal.ZERO;
    }
}
