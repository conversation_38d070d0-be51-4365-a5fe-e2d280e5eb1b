import { Configurator, Navbar, Sidebar } from "@/components/portals";
import { useAuth } from "@/context/auth-context";
import { useSelector } from "react-redux";
import { routes } from "@/routes/client";
import { useEffect } from "react";
import { Navigate, Route, Routes, useNavigate } from "react-router-dom";

export const Client = () => {
  const { getUserRole, isAuthenticated, loading } = useAuth();
  const user = useSelector((state) => state.user.userData);
  const navigate = useNavigate();

  // Redirect if not authenticated or not an admin
  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated()) {
        navigate("/auth/login", { replace: true });
        return;
      }

      const role = getUserRole()?.toLowerCase();
      if (role !== "client") {
        // Redirect to appropriate dashboard based on role
        switch (role) {
          case "admin":
            navigate("/admin/dashboard", { replace: true });
            break;
          case "supplier":
            navigate("/supplier/dashboard", { replace: true });
            break;
          case "employee":
            navigate("/employee/dashboard", { replace: true });
            break;
          default:
            navigate("/", { replace: true });
        }
      }
    }
  }, [isAuthenticated, getUserRole, loading, navigate]);

  // Show loading when checking auth
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Get user info for display
  const firstName = user?.firstName || user?.email?.split("@")[0] || "Client";
  const fullName =
    user?.firstName && user?.lastName
      ? `${user.firstName} ${user.lastName}`
      : firstName;
  const email = user?.email || "";
  const profilePicture = user?.profilePictureUrl || "/img/default-profile.jpg";

  return (
    <main className="min-h-dvh bg-blue-gray-50/50">
      <Sidebar userRole="client" routes={routes} firstName={firstName} />
      <div className="p-4 lg:ml-72">
        <Navbar
          userRole="client"
          profilePicture={profilePicture}
          fullName={fullName}
          email={email}
        />
        <Configurator />
        <Routes>
          {routes.map(({ path, element }, index) => {
            return <Route key={index} path={path} element={element} />;
          })}
          {/* Catch-all route for undefined paths */}
          <Route
            path="*"
            element={<Navigate to="/client/dashboard" replace />}
          />
        </Routes>
      </div>
    </main>
  );
};

export default Client;
