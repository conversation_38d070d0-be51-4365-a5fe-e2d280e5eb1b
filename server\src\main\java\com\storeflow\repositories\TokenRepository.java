package com.storeflow.repositories;

import com.storeflow.models.Token;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface TokenRepository extends JpaRepository<Token, UUID> {
    @Query("""
        select t from Token t inner join User u on t.user.id = u.id
        where u.id = :userId and (t.expired = false and t.revoked = false)
    """)
    List<Token> findAllValidTokensByUser(UUID userId);

    Optional<Token> findByToken(String token);

    // Delete all tokens that are both expired and revoked
    @Modifying
    @Transactional
    @Query("DELETE FROM Token t WHERE t.expired = true AND t.revoked = true")
    void deleteAllExpiredAndRevokedTokens();

    // Delete tokens that have been expired and revoked for a certain period
    @Modifying
    @Transactional
    @Query("DELETE FROM Token t WHERE t.expired = true AND t.revoked = true AND t.updatedAt < :cutoffDate")
    void deleteExpiredAndRevokedTokensOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);

    // Count tokens that would be deleted (useful for logging)
    @Query("SELECT COUNT(t) FROM Token t WHERE t.expired = true AND t.revoked = true AND t.updatedAt < :cutoffDate")
    long countExpiredAndRevokedTokensOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);

    // Delete all tokens for a specific user
    @Modifying
    @Transactional
    @Query("DELETE FROM Token t WHERE t.user.id = :userId")
    void deleteAllTokensByUserId(@Param("userId") UUID userId);
}
