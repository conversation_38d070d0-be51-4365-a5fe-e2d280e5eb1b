import { useState, useRef, useCallback } from "react";
import { useDispatch } from "react-redux";
import { updateProfilePicture } from "@/redux/user-slice";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import {
  Upload,
  X,
  Save,
  RotateCcw,
  ImageIcon,
  Camera,
  RefreshCw,
} from "lucide-react";
import Cropper from "react-easy-crop";
import { profileService } from "@/services";
import { ripple } from "@/utils";

export const ProfilePicture = ({ user }) => {
  const dispatch = useDispatch();
  const [image, setImage] = useState(null);
  const [preview, setPreview] = useState(null);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [confirmResetOpen, setConfirmResetOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const inputRef = useRef(null);

  const onCropComplete = useCallback((croppedArea, croppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels);
  }, []);

  const onFileChange = (e) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      const reader = new FileReader();

      reader.onload = () => {
        setImage(reader.result);
        setDialogOpen(true);
      };

      reader.readAsDataURL(file);
    }
  };

  const handleUploadClick = () => {
    inputRef.current.click();
  };

  const createCroppedImage = async () => {
    if (!croppedAreaPixels) return null;

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");

    const img = new Image();
    img.src = image;

    await new Promise((resolve) => {
      img.onload = resolve;
    });

    // Calculate the proper dimensions taking rotation into account
    const maxSize = Math.max(croppedAreaPixels.width, croppedAreaPixels.height);
    canvas.width = maxSize;
    canvas.height = maxSize;

    // Move to center of canvas
    ctx.translate(canvas.width / 2, canvas.height / 2);

    // Rotate if needed
    if (rotation !== 0) {
      ctx.rotate((rotation * Math.PI) / 180);
    }

    // Draw the cropped image
    ctx.drawImage(
      img,
      croppedAreaPixels.x,
      croppedAreaPixels.y,
      croppedAreaPixels.width,
      croppedAreaPixels.height,
      -croppedAreaPixels.width / 2,
      -croppedAreaPixels.height / 2,
      croppedAreaPixels.width,
      croppedAreaPixels.height
    );

    // Restore context and create circular crop
    ctx.restore();

    // Create a circular crop (if desired)
    // Uncomment this section if you want a circular profile picture
    /*
    ctx.globalCompositeOperation = 'destination-in';
    ctx.beginPath();
    ctx.arc(canvas.width / 2, canvas.height / 2, canvas.width / 2, 0, Math.PI * 2);
    ctx.closePath();
    ctx.fill();
    */

    // Convert the canvas to a Blob
    return new Promise((resolve) => {
      canvas.toBlob(
        (blob) => {
          resolve(blob);
        },
        "image/jpeg",
        0.95
      );
    });
  };

  const handleSaveImage = async () => {
    setLoading(true);
    try {
      const croppedImageBlob = await createCroppedImage();

      if (!croppedImageBlob) {
        toast.error("Failed to crop image");
        return;
      }

      // Get file size information
      const fileSizeBytes = croppedImageBlob.size;
      const fileSizeKB = (fileSizeBytes / 1024).toFixed(2);
      const fileSizeMB = (fileSizeBytes / (1024 * 1024)).toFixed(2);

      // Display file size in the appropriate unit
      const fileSizeDisplay =
        fileSizeBytes > 1024 * 1024 ? `${fileSizeMB} MB` : `${fileSizeKB} KB`;

      // Show toast with file size information
      toast.info(`Processing image (${fileSizeDisplay})...`);

      // Create a File object from the Blob
      const file = new File([croppedImageBlob], "profile-picture.jpg", {
        type: "image/jpeg",
      });

      // Generate preview URL for immediate feedback
      const previewUrl = URL.createObjectURL(croppedImageBlob);
      setPreview(previewUrl);

      // Upload to server
      const response = await profileService.updateProfilePicture(file);

      // Update Redux store with new profile picture URL
      if (response.data && response.data.data) {
        const newProfilePictureUrl = response.data.data;
        dispatch(updateProfilePicture(newProfilePictureUrl));
      }

      toast.success(
        `Profile picture updated successfully! (${fileSizeDisplay})`
      );
      setDialogOpen(false);

      // No need to reload the window anymore!
    } catch (error) {
      console.error("Error saving profile picture:", error);
      toast.error(
        error.response?.data?.message || "Failed to update profile picture"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleResetProfilePicture = async () => {
    setLoading(true);
    try {
      const response = await profileService.resetProfilePicture();

      // Update Redux store with default profile picture URL
      if (response.data && response.data.data) {
        const defaultProfilePictureUrl = response.data.data;
        dispatch(updateProfilePicture(defaultProfilePictureUrl));
      }

      toast.success("Profile picture reset to default");
      setConfirmResetOpen(false);

      // No need to reload the window anymore!
    } catch (error) {
      console.error("Error resetting profile picture:", error);
      toast.error(
        error.response?.data?.message || "Failed to reset profile picture"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6 mx-auto">
      {/* <div className="bg-white rounded-xl shadow-sm border border-blue-gray-100 overflow-hidden"> */}
      <div className="">
        <div className="flex flex-col md:flex-row gap-4 items-center">
          <div className="flex-shrink-0">
            <div className="group relative w-44 h-44 mx-auto md:mx-0 rounded-full overflow-hidden border-4 border-blue-gray-100 shadow-md hover:shadow-lg transition-all">
              {preview ? (
                <img
                  src={preview}
                  alt="Profile preview"
                  className="w-full h-full object-cover"
                />
              ) : user?.profilePictureUrl ? (
                <img
                  src={user.profilePictureUrl}
                  alt={`${user.firstName} ${user.lastName}`}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-blue-gray-100">
                  <ImageIcon size={48} className="text-blue-gray-400" />
                </div>
              )}
              <button
                onClick={handleUploadClick}
                className="absolute inset-0 bg-black/60 bg-opacity-0 hover:bg-opacity-30 transition-all duration-[400ms] flex items-center justify-center opacity-0 hover:opacity-100 cursor-pointer"
              >
                <Camera className="text-white h-10 w-10" />
              </button>
            </div>
          </div>

          <div className="flex flex-col items-center md:items-start space-y-4 flex-grow">
            <div className="w-full text-center md:text-left space-y-2">
              <h3 className="text-xl font-semibold text-blue-gray-800">
                Profile Picture
              </h3>
              <p className="text-center lg:text-left text-[12px] lg:text-sm text-blue-gray-700">
                Upload a clear photo of yourself. A well-chosen picture helps
                personalize your account.
              </p>
            </div>

            <div className="flex items-center flex-wrap xl:flex-nowrap w-full gap-2">
              <button
                onMouseDown={(e) => ripple.create(e, "light")}
                onClick={handleUploadClick}
                disabled={loading}
                className={`w-full inline-flex items-center justify-center gap-2 px-4 py-3 rounded-xl transition-all ${
                  loading
                    ? "bg-blue-gray-200 cursor-not-allowed"
                    : "bg-blue-gray-900 hover:bg-blue-gray-800 text-white shadow-sm hover:shadow"
                }`}
              >
                <Upload size={18} />
                <span>Upload New Picture</span>
              </button>

              <button
                onMouseDown={(e) => ripple.create(e, "dark")}
                onClick={() => setConfirmResetOpen(true)}
                disabled={loading}
                className="w-full inline-flex items-center justify-center gap-2 px-4 py-3 rounded-xl border border-blue-gray-200 bg-white hover:bg-blue-gray-50 text-blue-gray-700 transition-all"
              >
                <RotateCcw size={18} />
                <span>Reset to Default</span>
              </button>

              <input
                type="file"
                ref={inputRef}
                onChange={onFileChange}
                accept="image/jpeg,image/jpg,image/png"
                className="hidden"
              />
            </div>

            <p className="text-[12px] lg:text-sm text-blue-gray-700">
              You can upload JPG, JPEG and PNG images. Max image size: 8MB.
            </p>
          </div>
        </div>
      </div>
      {/* </div> */}

      {/* Image Cropper Dialog - Fixed positioning to avoid height issues */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-[95vw] w-[550px] max-h-[90vh] flex flex-col bg-white rounded-xl overflow-hidden p-0 shadow-lg">
          <DialogHeader className="px-6 pt-6 pb-4">
            <DialogTitle className="text-xl font-semibold text-blue-gray-700">
              Crop Profile Picture
            </DialogTitle>
            <DialogDescription className="text-blue-gray-600">
              Drag, zoom and position your photo to create the perfect profile
              picture
            </DialogDescription>
          </DialogHeader>

          <div className="relative w-full h-[300px] md:h-[350px] mx-auto bg-gray-900 overflow-hidden">
            {image && (
              <Cropper
                image={image}
                crop={crop}
                zoom={zoom}
                rotation={rotation}
                aspect={1}
                onCropChange={setCrop}
                onZoomChange={setZoom}
                onRotationChange={setRotation}
                onCropComplete={onCropComplete}
                cropShape="round"
                objectFit="contain"
                showGrid={false}
                style={{
                  containerStyle: { height: "100%", width: "100%" },
                  cropAreaStyle: {
                    boxShadow: "0 0 0 9999px rgba(0, 0, 0, 0.8)",
                    border: "2px solid #fff",
                  },
                  mediaStyle: { maxHeight: "100%" },
                }}
              />
            )}
          </div>

          <div className="flex flex-col sm:flex-row items-center gap-4 p-5 border-t border-blue-gray-100 bg-blue-gray-50">
            <div className="w-full sm:w-auto flex-1 flex flex-col">
              <label className="text-sm font-medium mb-1 text-blue-gray-700">
                Zoom
              </label>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setZoom(Math.max(1, zoom - 0.1))}
                  className="text-blue-gray-700 hover:text-blue-gray-900"
                >
                  <span className="text-lg font-bold">−</span>
                </button>
                <input
                  type="range"
                  min={1}
                  max={3}
                  step={0.1}
                  value={zoom}
                  onChange={(e) => setZoom(Number(e.target.value))}
                  className="w-full h-2 rounded-full accent-blue-gray-800"
                />
                <button
                  onClick={() => setZoom(Math.min(3, zoom + 0.1))}
                  className="text-blue-gray-700 hover:text-blue-gray-900"
                >
                  <span className="text-lg font-bold">+</span>
                </button>
              </div>
            </div>

            <div className="flex flex-col items-center">
              <label className="text-sm font-medium mb-1 text-blue-gray-700">
                Rotate
              </label>
              <button
                type="button"
                onClick={() => setRotation((prev) => (prev + 90) % 360)}
                className="p-2 rounded-full bg-white border border-blue-gray-200 hover:bg-blue-gray-50 text-blue-gray-700 transition-colors shadow-sm"
              >
                <RefreshCw size={18} />
              </button>
            </div>
          </div>

          <DialogFooter className="p-4 bg-white border-t border-blue-gray-100 gap-2">
            <button
              onMouseDown={(e) => ripple.create(e, "dark")}
              onClick={() => setDialogOpen(false)}
              disabled={loading}
              className="w-full px-4 py-2.5 rounded-xl border border-blue-gray-200 bg-white text-blue-gray-700 transition-colors hover:bg-blue-gray-50"
            >
              Cancel
            </button>
            <button
              onMouseDown={(e) => ripple.create(e, "light")}
              onClick={handleSaveImage}
              disabled={loading}
              className="w-full px-4 py-2.5 rounded-xl bg-blue-gray-900 text-white transition-colors hover:bg-blue-gray-800 flex items-center justify-center gap-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <Save size={16} />
                  <span>Save & Apply</span>
                </>
              )}
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reset Confirmation Dialog */}
      <AlertDialog open={confirmResetOpen} onOpenChange={setConfirmResetOpen}>
        <AlertDialogContent className="bg-white p-0 rounded-xl overflow-hidden max-w-md">
          <AlertDialogHeader className="p-6">
            <AlertDialogTitle className="text-xl font-semibold text-blue-gray-800">
              Reset Profile Picture?
            </AlertDialogTitle>
            <AlertDialogDescription className="text-blue-gray-700">
              This will remove your current profile picture and replace it with
              the default image.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="p-4 flex gap-2 border-t border-blue-gray-100">
            <AlertDialogCancel
              disabled={loading}
              onMouseDown={(e) => ripple.create(e, "dark")}
              className="text-base flex-1 border border-blue-gray-200 bg-white text-blue-gray-700 transition-colors hover:bg-blue-gray-50"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              disabled={loading}
              onMouseDown={(e) => ripple.create(e, "light")}
              onClick={handleResetProfilePicture}
              className="text-base flex-1 bg-red-500 hover:bg-red-600 text-white transition-colors disabled:bg-red-300"
            >
              {loading ? (
                <span className="flex items-center justify-center gap-2">
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                  <span>Resetting...</span>
                </span>
              ) : (
                "Reset Picture"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ProfilePicture;
