package com.storeflow.validation;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class PhoneNumberValidator implements ConstraintValidator<PhoneNumber, String> {

    private PhoneNumberUtil phoneUtil;

    @Override
    public void initialize(PhoneNumber constraintAnnotation) {
        this.phoneUtil = PhoneNumberUtil.getInstance();
    }

    @Override
    public boolean isValid(String phoneNumber, ConstraintValidatorContext context) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return true;
        }

        try {
            // Clean and parse the phone number
            String cleanedPhone = cleanPhoneNumber(phoneNumber);

            // Parse the phone number
            Phonenumber.PhoneNumber parsedNumber = phoneUtil.parse(cleanedPhone, "ZZ");

            // Validate the number
            // Return false instead of throwing exception
            return phoneUtil.isValidNumber(parsedNumber);
        } catch (NumberParseException e) {
            // Handle parsing errors by returning false
            return false;
        } catch (Exception e) {
            // Catch-all for any other exceptions
            return false;
        }
    }

    private String cleanPhoneNumber(String phoneNumber) {
        if (phoneNumber == null) return "";

        // Remove spaces and ensure + prefix
        String cleaned = phoneNumber.replaceAll("\\s+", "");
        if (!cleaned.startsWith("+")) {
            cleaned = "+" + cleaned;
        }
        return cleaned;
    }
}
