package com.storeflow.DTOs;

import com.storeflow.enums.OrderStatus;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * DTO for Order entity.
 */
public record OrderDTO(
    UUID id,
    UUID clientId,
    String clientName,
    LocalDateTime orderDate,
    OrderStatus status,
    BigDecimal totalAmount,
    String notes,
    String phoneNumber,
    List<OrderItemDTO> items,
    LocalDateTime createdDate,
    LocalDateTime lastModifiedDate,
    String createdBy,
    String lastModifiedBy
) {
}
