package com.storeflow.DTOs;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for OrderItem entity.
 */
public record OrderItemDTO(
    UUID id,
    String productLabel,
    String productDescription,
    String productImageUrl,
    String productBrand,
    Integer quantity,
    BigDecimal unitPrice,
    BigDecimal totalPrice,
    LocalDateTime createdDate,
    LocalDateTime lastModifiedDate
) {
}
