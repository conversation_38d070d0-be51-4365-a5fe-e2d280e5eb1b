package com.storeflow.configurations;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.storeflow.enums.Role;
import com.storeflow.exception.TokenAuthenticationException;
import com.storeflow.exception.TokenProcessingException;
import com.storeflow.exception.TokenRefreshException;
import com.storeflow.responses.ErrorResponse;
import com.storeflow.repositories.TokenRepository;
import com.storeflow.services.JwtService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    private final JwtService jwtService;
    private final TokenRepository tokenRepository;
    private final UserDetailsService userDetailsService;
    private final ObjectMapper objectMapper;

    @Override
    protected void doFilterInternal(
        @NonNull HttpServletRequest request,
        @NonNull HttpServletResponse response,
        @NonNull FilterChain filterChain
    ) throws ServletException, IOException {
        final String authHeader = request.getHeader("Authorization");

        // Early return if no auth header or not a Bearer token
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            filterChain.doFilter(request, response);
            return;
        }

        final String jwt = authHeader.substring(7);

        try {
            final String userEmail = jwtService.extractUsername(jwt);

            if (userEmail != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                // Load user details from database
                UserDetails userDetails = this.userDetailsService.loadUserByUsername(userEmail);

                boolean isTokenValid = tokenRepository.findByToken(jwt)
                    .map(token -> !token.isExpired() && !token.isRevoked())
                    .orElse(false);

                if (jwtService.isTokenValid(jwt, userDetails) && isTokenValid) {
                    // Get role from token to ensure we're using what was valid when token was issued
                    String roleFromToken = jwtService.extractRole(jwt);

                    // Create authorities from the role in token
                    List<SimpleGrantedAuthority> authorities = createAuthoritiesFromRole(roleFromToken);

                    UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                        userDetails,
                        null,
                        authorities
                    );
                    authToken.setDetails(
                        new WebAuthenticationDetailsSource().buildDetails(request)
                    );
                    SecurityContextHolder.getContext().setAuthentication(authToken);
                }
            }

            filterChain.doFilter(request, response);

        } catch (TokenAuthenticationException | TokenProcessingException | TokenRefreshException ex) {
            // Determine appropriate status code based on exception type
            HttpStatus status = HttpStatus.UNAUTHORIZED;

            log.error("JWT token error in filter: {}", ex.getMessage());

            // Set response status and content type
            response.setStatus(status.value());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);

            // Create error response
            ErrorResponse errorResponse = new ErrorResponse(
                status.value(),
                ex.getMessage(),
                request.getRequestURI()
            );

            // Write the error response to the output
            objectMapper.writeValue(response.getOutputStream(), errorResponse);
        }
    }

    /**
     * Creates authorities list from a role name
     */
    private List<SimpleGrantedAuthority> createAuthoritiesFromRole(String roleName) {
        try {
            // Convert role name to uppercase and create ROLE_ authority
            String upperRole = roleName.toUpperCase();
            return Collections.singletonList(new SimpleGrantedAuthority("ROLE_" + upperRole));
        } catch (Exception e) {
            // Fallback to default user role if any issues
            log.warn("Error creating authorities from role: {}", roleName, e);
            return Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"));
        }
    }
}
