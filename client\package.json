{"name": "storeflow", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@material-tailwind/react": "^2.1.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.6", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "formik": "^2.4.6", "input-otp": "^1.4.2", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.510.0", "material-ripple-effects": "^2.0.1", "prop-types": "^15.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-easy-crop": "^5.4.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.3", "react-hot-toast": "^2.5.2", "react-phone-number-input": "^3.4.12", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0", "react-router-hash-link": "^2.4.3", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.6", "yup": "^1.6.1", "yup-phone": "^1.3.2", "zod": "^3.24.4"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tailwind-scrollbar": "^4.0.2", "tw-animate-css": "^1.2.9", "vite": "^6.3.5"}}