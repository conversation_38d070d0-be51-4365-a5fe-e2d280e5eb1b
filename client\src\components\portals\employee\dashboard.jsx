import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import {
  Package,
  TrendingUp,
  DollarSign,
  ShoppingCart,
  ArrowUpRight,
  Wallet,
  ClockIcon,
  Loader2,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { employeeAnalyticsService } from "@/services/employee-analytics-service";
import { toast } from "react-hot-toast";

export const Dashboard = () => {
  const { userData: user } = useSelector((state) => state.user);
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalPurchases: 0,
    totalSpent: 0,
    productsPurchased: 0,
    activeSuppliers: 0,
    recentActivity: [],
  });

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const response =
          await employeeAnalyticsService.getEmployeeDashboardStats();

        if (response.success) {
          setStats(response.data);
        } else {
          toast.error(response.message || "Failed to load dashboard data");
        }
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
        toast.error("Failed to load dashboard data");
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Format date for recent activity
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return "Today";
    if (diffDays === 2) return "Yesterday";
    if (diffDays <= 7) return `${diffDays - 1} days ago`;
    return date.toLocaleDateString("en-US", { month: "short", day: "numeric" });
  };

  // Get icon for activity type
  const getActivityIcon = (type) => {
    switch (type) {
      case "purchase":
        return <ShoppingCart className="h-5 w-5 text-blue-600" />;
      case "order":
        return <Package className="h-5 w-5 text-green-600" />;
      default:
        return <ClockIcon className="h-5 w-5 text-slate-600" />;
    }
  };

  // Create stats cards data
  const statsCards = [
    {
      title: "Total Purchases",
      value: stats.totalPurchases.toString(),
      icon: <ShoppingCart className="h-5 w-5 text-blue-600" />,
      change: stats.totalPurchases > 0 ? "Active" : "No purchases yet",
      changeType: stats.totalPurchases > 0 ? "positive" : "neutral",
    },
    {
      title: "Total Spent",
      value: `${stats.totalSpent.toFixed(2)} MAD`,
      icon: <DollarSign className="h-5 w-5 text-amber-600" />,
      change: stats.totalSpent > 0 ? "Investment made" : "No spending yet",
      changeType: stats.totalSpent > 0 ? "positive" : "neutral",
    },
    {
      title: "Active Suppliers",
      value: stats.activeSuppliers.toString(),
      icon: <Wallet className="h-5 w-5 text-purple-600" />,
      change: stats.activeSuppliers > 0 ? "Partners" : "No suppliers yet",
      changeType: stats.activeSuppliers > 0 ? "positive" : "neutral",
    },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex flex-col items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mb-2" />
          <p className="text-slate-500 text-lg">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Welcome section */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-slate-900">
              Welcome, {user?.firstName || "Employee"}!
            </h1>
            <p className="text-slate-500 mt-1">
              Here's what's happening with your purchases today.
            </p>
          </div>
          <Button
            className="bg-blue-600 hover:bg-blue-700 text-white"
            onClick={() => navigate("/employee/purchase")}
          >
            <ShoppingCart className="mr-2 h-4 w-4" />
            Purchase Products
          </Button>
        </div>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {statsCards.map((stat, index) => (
          <Card key={index} className="border-slate-200 rounded-xl">
            <CardHeader className="flex flex-row items-center justify-between pb-2 pt-4 px-4">
              <CardTitle className="text-sm font-medium text-slate-500">
                {stat.title}
              </CardTitle>
              {stat.icon}
            </CardHeader>
            <CardContent className="px-4 pb-4">
              <div className="text-2xl font-bold text-slate-900">
                {stat.value}
              </div>
              <div className="flex items-center pt-1">
                <span
                  className={`text-xs font-medium flex items-center ${
                    stat.changeType === "positive"
                      ? "text-green-600"
                      : stat.changeType === "negative"
                      ? "text-red-600"
                      : "text-slate-500"
                  }`}
                >
                  {stat.changeType === "positive" ? (
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                  ) : stat.changeType === "negative" ? (
                    <TrendingUp className="h-3 w-3 mr-1" />
                  ) : (
                    <ClockIcon className="h-3 w-3 mr-1" />
                  )}
                  {stat.change}
                </span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent activity section */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
        <h2 className="text-lg font-semibold text-slate-900 mb-4">
          Recent Activity
        </h2>
        <div className="space-y-4">
          {stats.recentActivity.length === 0 ? (
            <div className="text-center py-8">
              <ClockIcon className="h-12 w-12 mx-auto text-slate-300 mb-3" />
              <p className="text-slate-500 text-sm">No recent activity</p>
              <p className="text-slate-400 text-xs mt-1">
                Start making purchases to see your activity here
              </p>
            </div>
          ) : (
            stats.recentActivity.map((activity, index) => (
              <div
                key={index}
                className="flex items-center p-3 bg-slate-50 rounded-lg"
              >
                <div
                  className={`h-9 w-9 rounded-full flex items-center justify-center mr-3 ${
                    activity.type === "purchase"
                      ? "bg-blue-100"
                      : activity.type === "order"
                      ? "bg-green-100"
                      : "bg-slate-100"
                  }`}
                >
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-slate-900">
                    {activity.title}
                  </p>
                  <p className="text-xs text-slate-500">
                    {activity.description}
                  </p>
                  <p className="text-xs text-slate-400 mt-1">
                    {formatDate(activity.date)}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};
