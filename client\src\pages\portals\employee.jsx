import { useEffect } from "react";
import { Routes, Route, Navigate, useNavigate } from "react-router-dom";
import { useAuth } from "@/context/auth-context";
import { useSelector } from "react-redux";
import { Sidebar, Navbar, Configurator } from "@/components/portals";
import { routes } from "@/routes/employee";

export const Employee = () => {
  const { getUserRole, isAuthenticated, loading } = useAuth();
  const navigate = useNavigate();
  const { userData } = useSelector((state) => state.user);

  // Redirect if not authenticated or not an employee
  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated()) {
        navigate("/auth/login", { replace: true });
        return;
      }

      const role = getUserRole()?.toLowerCase();
      if (role !== "employee") {
        // Redirect to appropriate dashboard based on role
        switch (role) {
          case "admin":
            navigate("/admin/dashboard", { replace: true });
            break;
          case "client":
            navigate("/client/dashboard", { replace: true });
            break;
          case "supplier":
            navigate("/supplier/dashboard", { replace: true });
            break;
          default:
            navigate("/", { replace: true });
        }
      }
    }
  }, [isAuthenticated, getUserRole, loading, navigate]);

  // Show loading when checking auth
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Extract user info for the sidebar and navbar
  const firstName = userData?.firstName || "";
  const lastName = userData?.lastName || "";
  const fullName = `${firstName} ${lastName}`;
  const email = userData?.email || "";
  const profilePicture =
    userData?.profilePictureUrl || "/img/default-profile.jpg";

  return (
    <main className="min-h-dvh bg-blue-gray-50/50">
      <Sidebar userRole="employee" routes={routes} firstName={firstName} />
      <div className="p-4 lg:ml-72">
        <Navbar
          userRole="employee"
          profilePicture={profilePicture}
          fullName={fullName}
          email={email}
        />
        <Configurator />
        <Routes>
          {routes.map(({ path, element }, index) => {
            return <Route key={index} path={path} element={element} />;
          })}
          {/* Catch-all route for undefined paths */}
          <Route
            path="*"
            element={<Navigate to="/employee/dashboard" replace />}
          />
        </Routes>
      </div>
    </main>
  );
};

export default Employee;
