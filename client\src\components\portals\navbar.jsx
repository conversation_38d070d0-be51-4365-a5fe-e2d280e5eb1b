import {
  Navbar as MT<PERSON><PERSON><PERSON>,
  Typography,
  Icon<PERSON>utton,
  Breadcrumbs,
  Tooltip,
} from "@material-tailwind/react";
import {
  useThemeController,
  setOpenSidenav,
  setOpenConfigurator,
} from "@/context";
import PropTypes from "prop-types";
import { useLocation, Link } from "react-router-dom";
import { MenuIcon, SettingsIcon } from "lucide-react";

export const Navbar = ({ userRole, profilePicture, fullName, email }) => {
  const [controller, dispatch] = useThemeController();
  const { fixedNavbar, openSidenav } = controller;
  const { pathname } = useLocation();
  const [layout, page] = pathname.split("/").filter((el) => el !== "");

  return (
    <MTNavbar
      color={fixedNavbar ? "white" : "transparent"}
      className={`overflow-hidden rounded-xl transition-all mb-4 ${
        fixedNavbar
          ? "sticky top-4 z-40 border border-blue-gray-100 py-3 shadow-md shadow-blue-gray-500/5"
          : "px-0 py-1"
      }`}
      fullWidth
      blurred={fixedNavbar}
    >
      <div className="flex justify-between gap-6 flex-row items-center">
        <div className="capitalize">
          <Breadcrumbs
            className="bg-transparent p-0 transition-all flex items-center justify-between"
            separator={<span className="font-satoshi text-base">/</span>}
          >
            <Link to={`/${userRole}/dashboard`}>
              <Typography
                variant="paragraph"
                color="blue-gray"
                className="font-satoshi font-normal opacity-50 transition-all hover:text-blue-500 hover:opacity-100"
              >
                {layout}
              </Typography>
            </Link>
            <Typography
              variant="paragraph"
              color="blue-gray"
              className="font-satoshi font-normal"
            >
              {page}
            </Typography>
          </Breadcrumbs>
        </div>
        <div className="flex items-center">
          <Tooltip
            content="Open or close the side bar"
            className="font-supreme"
          >
            <IconButton
              variant="text"
              color="blue-gray"
              className="grid lg:hidden"
              onClick={() => setOpenSidenav(dispatch, !openSidenav)}
            >
              <MenuIcon className="size-5 lg:size-6 text-blue-gray-500" />
            </IconButton>
          </Tooltip>
          <div className="flex items-center gap-x-1">
            <Tooltip content="More customizations" className="font-supreme">
              <IconButton
                variant="text"
                color="blue-gray"
                onClick={() => setOpenConfigurator(dispatch, true)}
              >
                <SettingsIcon className="size-5 lg:size-6 text-blue-gray-500" />
              </IconButton>
            </Tooltip>
            <Tooltip
              placement="bottom-end"
              className="bg-black/80"
              content={
                <div>
                  <Typography
                    variant="paragraph"
                    className="font-supreme font-medium"
                  >
                    StoreFlow Account
                  </Typography>
                  <Typography
                    variant="small"
                    className="font-supreme font-normal"
                  >
                    {fullName}
                  </Typography>
                  <Typography
                    variant="small"
                    className="font-supreme font-normal"
                  >
                    {email}
                  </Typography>
                </div>
              }
            >
              <Link to={`/${userRole}/profile`}>
                <img
                  alt={fullName}
                  src={profilePicture || "/img/default-profile.jpg"}
                  className="transition-all cursor-pointer rounded-full size-12 object-center object-cover"
                />
              </Link>
            </Tooltip>
          </div>
        </div>
      </div>
    </MTNavbar>
  );
};

Navbar.propTypes = {
  userRole: PropTypes.string.isRequired,
  profilePicture: PropTypes.string.isRequired,
  fullName: PropTypes.string.isRequired,
  email: PropTypes.string.isRequired,
};

export default Navbar;
