import { Link, useNavigate } from "react-router-dom";
import { isDesktop, ripple } from "@/utils";
import { AtSignIcon, ArrowLeftIcon, KeyRoundIcon } from "lucide-react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useState } from "react";
import { authApi } from "@/context/auth-context";
import { toast } from "sonner";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
  InputOTPSeparator,
} from "@/components/ui/input-otp";

export const ForgotPassword = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [verificationCode, setVerificationCode] = useState("");
  const [userEmail, setUserEmail] = useState("");
  const [resendLoading, setResendLoading] = useState(false);

  // Define validation schema using Yup
  const ForgotPasswordSchema = Yup.object().shape({
    email: Yup.string()
      .email("Email must be valid")
      .required("Email is required"),
  });

  // Initialize formik
  const formik = useFormik({
    initialValues: {
      email: "",
    },
    validationSchema: ForgotPasswordSchema,
    onSubmit: async (values) => {
      setLoading(true);
      try {
        const response = await authApi.post(
          "/api/v1/auth/forgot-password",
          values
        );

        if (response.data.success) {
          setUserEmail(values.email);
          setEmailSent(true);
          toast.success("Verification code sent to your email!");
        }
      } catch (error) {
        console.error("Forgot password error:", error);
        const errorMessage =
          error.response?.data?.message ||
          "Failed to send reset email. Please try again.";
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    },
  });

  // Handle resend code
  const handleResendCode = async () => {
    setResendLoading(true);
    try {
      const response = await authApi.post("/api/v1/auth/forgot-password", {
        email: userEmail,
      });

      if (response.data.success) {
        toast.success("New verification code sent!");
        setVerificationCode(""); // Clear current code
      }
    } catch (error) {
      console.error("Resend code error:", error);
      const errorMessage =
        error.response?.data?.message ||
        "Failed to resend code. Please try again.";
      toast.error(errorMessage);
    } finally {
      setResendLoading(false);
    }
  };

  // Handle code verification
  const handleCodeComplete = async (code) => {
    setLoading(true);
    try {
      const response = await authApi.post("/api/v1/auth/verify-reset-code", {
        email: userEmail,
        verificationCode: code,
      });

      if (response.data.success && response.data.data) {
        // Code is valid, redirect to reset password page
        navigate(
          `/auth/reset-password?email=${encodeURIComponent(
            userEmail
          )}&code=${code}`
        );
      } else {
        toast.error("Invalid verification code. Please try again.");
        setVerificationCode("");
      }
    } catch (error) {
      console.error("Code verification error:", error);
      const errorMessage =
        error.response?.data?.message ||
        "Invalid verification code. Please try again.";
      toast.error(errorMessage);
      setVerificationCode("");
    } finally {
      setLoading(false);
    }
  };

  if (emailSent) {
    return (
      <section className="min-h-screen flex flex-col justify-center items-center px-8 py-20 lg:px-[340px] bg-blue-gray-50/40">
        <div className="flex items-center justify-center gap-x-1.5 absolute top-0 right-0 px-6 py-3 select-none">
          <img src="/icon.png" className="size-7" alt="Logo" />
          <p className="text-blue-gray-700 font-satoshi text-xl">
            <span className="text-crimson">S</span>tore
            <span className="text-crimson">F</span>low
          </p>
        </div>

        <div className="lg:bg-white lg:rounded-3xl w-full lg:p-10 lg:border lg:border-blue-gray-50 lg:shadow-dark text-blue-gray-700">
          <div className="text-center">
            <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6">
              <KeyRoundIcon className="w-8 h-8 text-blue-600" />
            </div>

            <p className="font-satoshi font-bold text-header text-blue-gray-800 text-center w-full py-0 mb-4">
              Enter verification code
            </p>

            <p className="font-normal text-subheader text-blue-gray-600 text-center w-full mb-8 py-0">
              We've sent a 6-digit code to <br />
              <span className="font-semibold text-blue-gray-800">
                {userEmail}
              </span>
            </p>

            <div className="mb-8 flex justify-center">
              <InputOTP
                maxLength={6}
                value={verificationCode}
                onChange={setVerificationCode}
                onComplete={handleCodeComplete}
                disabled={loading}
              >
                <InputOTPGroup className="gap-2">
                  <InputOTPSlot index={0} />
                  <InputOTPSlot index={1} />
                  <InputOTPSlot index={2} />
                </InputOTPGroup>
                <InputOTPSeparator />
                <InputOTPGroup className="gap-2">
                  <InputOTPSlot index={3} />
                  <InputOTPSlot index={4} />
                  <InputOTPSlot index={5} />
                </InputOTPGroup>
              </InputOTP>
            </div>

            {loading && (
              <div className="flex items-center justify-center gap-2 mb-6 text-blue-gray-600">
                <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-600"></div>
                Verifying code...
              </div>
            )}

            <div className="mb-6 text-center">
              <button
                onClick={handleResendCode}
                disabled={resendLoading || loading}
                className="text-blue-600 hover:text-blue-800 transition-colors duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {resendLoading
                  ? "Sending..."
                  : "Didn't receive the code? Resend"}
              </button>
            </div>

            <div className="space-y-4">
              <button
                onClick={() => {
                  setEmailSent(false);
                  setVerificationCode("");
                  setUserEmail("");
                }}
                onMouseDown={(event) =>
                  ripple.create(event, isDesktop() ? "light" : "dark")
                }
                className="relative overflow-hidden w-full bg-blue-gray-800 text-white py-4 px-6 rounded-xl font-medium text-base lg:text-lg transition-all duration-300 hover:bg-blue-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-gray-500 focus:ring-offset-2 flex items-center justify-center"
                disabled={loading}
              >
                Try another email
              </button>

              <Link
                to="/auth/login"
                className="inline-flex items-center justify-center gap-2 text-blue-gray-600 hover:text-blue-gray-800 transition-colors duration-200 font-medium"
              >
                <ArrowLeftIcon className="size-5" />
                Back to login
              </Link>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="min-h-screen flex flex-col justify-center items-center px-8 py-20 lg:px-[340px] bg-blue-gray-50/40">
      <div className="flex items-center justify-center gap-x-1.5 absolute top-0 right-0 px-6 py-3 select-none">
        <img src="/icon.png" className="size-7" alt="Logo" />
        <p className="text-blue-gray-700 font-satoshi text-xl">
          <span className="text-crimson">S</span>tore
          <span className="text-crimson">F</span>low
        </p>
      </div>

      <div className="lg:bg-white lg:rounded-3xl w-full lg:p-10 lg:border lg:border-blue-gray-50 lg:shadow-dark text-blue-gray-700">
        <p className="font-satoshi font-bold text-header text-blue-gray-800 text-center w-full py-0">
          Forgot your password?
        </p>
        <p className="font-normal text-subheader text-blue-gray-800 text-center w-full mb-7 py-0">
          Enter your email and we'll send you a verification code
        </p>

        <form
          onSubmit={formik.handleSubmit}
          className="w-full flex flex-col items-center gap-y-7 mb-7"
        >
          {/* Email Input */}
          <div className="w-full relative">
            <div className="relative">
              <input
                type="email"
                name="email"
                placeholder="Enter your email"
                value={formik.values.email}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                className={`w-full h-[53px] pl-12 pr-4 border rounded-xl text-base lg:text-lg placeholder-blue-gray-400 focus:outline-none focus:ring-2 transition-all duration-300 ${
                  formik.touched.email && formik.errors.email
                    ? "border-red-500 focus:ring-red-200 bg-red-50"
                    : "border-blue-gray-200 focus:ring-blue-300 focus:border-blue-500 bg-white"
                }`}
              />
              <AtSignIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 text-blue-gray-400 size-5 lg:size-6" />
            </div>
            {formik.touched.email && formik.errors.email && (
              <p className="text-red-500 text-sm mt-1 ml-1">
                {formik.errors.email}
              </p>
            )}
          </div>

          <button
            type="submit"
            disabled={loading || !formik.isValid}
            onMouseDown={(event) => ripple.create(event, "light")}
            className="relative overflow-hidden w-full bg-blue-gray-800 text-white py-4 px-6 rounded-xl font-medium text-base lg:text-lg transition-all duration-300 hover:bg-blue-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed group flex items-center justify-center gap-x-2"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white"></div>
                Sending...
              </>
            ) : (
              <>Send verification code</>
            )}
          </button>
        </form>

        <div className="text-center">
          <Link
            to="/auth/login"
            className="inline-flex items-center justify-center gap-2 text-blue-gray-600 hover:text-blue-gray-800 transition-colors duration-200 font-medium"
          >
            <ArrowLeftIcon className="size-5" />
            Back to login
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ForgotPassword;
