package com.storeflow.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.storeflow.enums.Permission.*;

@Getter
@RequiredArgsConstructor
public enum Role {
    CLIENT(Collections.emptySet()),
    ADMIN(
        Set.of(
            ADMIN_CREATE,
            ADMIN_READ,
            ADMIN_UPDATE,
            ADMIN_DELETE,
            EMPLOYEE_CREATE,
            EMPLOYEE_READ,
            EMPLOYEE_UPDATE,
            EMPLOYEE_DELETE
        )
    ),
    EMPLOYEE(
        Set.of(
            EMPLOYEE_CREATE,
            EMPLOYEE_READ,
            EMPLOYEE_UPDATE,
            EMPLOYEE_DELETE
        )
    ),
    SUPPLIER(
        Set.of(
            SUPPLIER_CREATE,
            SUPPLIER_READ,
            SUPPLIER_UPDATE,
            SUPPLIER_DELETE
        )
    );

    private final Set<Permission> permissions;

    public List<SimpleGrantedAuthority> getAuthorities() {
        var authorities = getPermissions()
            .stream()
            .map(permission -> new SimpleGrantedAuthority(permission.getPermission()))
            .collect(Collectors.toList());

        authorities.add(new SimpleGrantedAuthority("ROLE_" + this.name()));
        return authorities;
    }
}
