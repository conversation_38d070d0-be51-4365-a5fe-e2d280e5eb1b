package com.storeflow.repositories;

import com.storeflow.enums.PurchaseStatus;
import com.storeflow.models.PurchaseItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Repository for managing PurchaseItem entities.
 */
@Repository
public interface PurchaseItemRepository extends JpaRepository<PurchaseItem, UUID> {

    /**
     * Find all purchase items for a specific purchase.
     *
     * @param purchaseId The ID of the purchase
     * @return A list of purchase items for the purchase
     */
    List<PurchaseItem> findByPurchaseId(UUID purchaseId);

    /**
     * Find all purchase items for a specific product.
     *
     * @param productId The ID of the product
     * @return A list of purchase items for the product
     */
    List<PurchaseItem> findByProductId(UUID productId);

    /**
     * Find all purchase items for a specific supplier.
     *
     * @param supplierId The ID of the supplier
     * @return A list of purchase items for the supplier
     */
    List<PurchaseItem> findBySupplierId(UUID supplierId);

    /**
     * Get monthly sales statistics for a supplier's product.
     *
     * @param supplierId The ID of the supplier
     * @param productId The ID of the product
     * @param startDate The start date
     * @param endDate The end date
     * @return A list of objects containing month, year, total quantity, and total amount
     */
    @Query("""
        SELECT
            MONTH(p.purchaseDate) as month,
            YEAR(p.purchaseDate) as year,
            SUM(pi.quantity) as totalQuantity,
            SUM(pi.quantity * pi.unitPrice) as totalAmount
        FROM PurchaseItem pi
        JOIN pi.purchase p
        WHERE pi.supplier.id = :supplierId
            AND pi.product.id = :productId
            AND p.status = 'COMPLETED'
            AND p.purchaseDate BETWEEN :startDate AND :endDate
        GROUP BY YEAR(p.purchaseDate), MONTH(p.purchaseDate)
        ORDER BY YEAR(p.purchaseDate), MONTH(p.purchaseDate)
    """)
    List<Object[]> getMonthlyProductSalesStatistics(
        @Param("supplierId") UUID supplierId,
        @Param("productId") UUID productId,
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );

    /**
     * Get monthly sales statistics for all products of a supplier.
     *
     * @param supplierId The ID of the supplier
     * @param startDate The start date
     * @param endDate The end date
     * @return A list of objects containing month, year, total quantity, and total amount
     */
    @Query("""
        SELECT
            MONTH(p.purchaseDate) as month,
            YEAR(p.purchaseDate) as year,
            SUM(pi.quantity) as totalQuantity,
            SUM(pi.quantity * pi.unitPrice) as totalAmount
        FROM PurchaseItem pi
        JOIN pi.purchase p
        WHERE pi.supplier.id = :supplierId
            AND p.status = 'COMPLETED'
            AND p.purchaseDate BETWEEN :startDate AND :endDate
        GROUP BY YEAR(p.purchaseDate), MONTH(p.purchaseDate)
        ORDER BY YEAR(p.purchaseDate), MONTH(p.purchaseDate)
    """)
    List<Object[]> getMonthlySupplierSalesStatistics(
        @Param("supplierId") UUID supplierId,
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );

    /**
     * Find all purchase items by purchase status.
     *
     * @param status The purchase status
     * @return A list of purchase items with the specified purchase status
     */
    @Query("""
        SELECT pi FROM PurchaseItem pi
        JOIN pi.purchase p
        WHERE p.status = :status
    """)
    List<PurchaseItem> findByPurchaseStatus(@Param("status") PurchaseStatus status);

    /**
     * Find all purchase items by product label and purchase status.
     *
     * @param productLabel The product label
     * @param status The purchase status
     * @return A list of purchase items with the specified label and purchase status
     */
    @Query("""
        SELECT pi FROM PurchaseItem pi
        JOIN pi.purchase p
        WHERE pi.productLabel = :productLabel AND p.status = :status
    """)
    List<PurchaseItem> findByProductLabelAndPurchaseStatus(
        @Param("productLabel") String productLabel,
        @Param("status") PurchaseStatus status
    );

    /**
     * Find all purchase items by product label and purchase status, ordered by creation date (FIFO).
     *
     * @param productLabel The product label
     * @param status The purchase status
     * @return A list of purchase items with the specified label and purchase status, ordered by creation date ascending
     */
    @Query("""
        SELECT pi FROM PurchaseItem pi
        JOIN pi.purchase p
        WHERE pi.productLabel = :productLabel AND p.status = :status
        ORDER BY pi.createdDate ASC
    """)
    List<PurchaseItem> findByProductLabelAndPurchaseStatusOrderByCreatedDateAsc(
        @Param("productLabel") String productLabel,
        @Param("status") PurchaseStatus status
    );
}
