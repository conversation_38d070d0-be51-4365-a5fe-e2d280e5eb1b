// useScrollLock.js
import { useEffect } from "react";

export const useScrollLock = (lock) => {
  useEffect(() => {
    // Get the original body style properties
    const originalStyle = window.getComputedStyle(document.body);
    const originalOverflow = originalStyle.overflow;

    // Function to toggle scroll lock
    if (lock) {
      // Save current scroll position
      const scrollY = window.scrollY;

      // Add styles to lock scroll and maintain position
      document.body.style.overflow = "hidden";
      document.body.style.position = "fixed";
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = "100%";
    } else {
      // Get the scroll position from body top property
      const scrollY = document.body.style.top;

      // Remove scroll lock styles
      document.body.style.overflow = originalOverflow;
      document.body.style.position = "";
      document.body.style.top = "";
      document.body.style.width = "";

      // Restore scroll position if we have one
      if (scrollY) {
        window.scrollTo(0, parseInt(scrollY || "0", 10) * -1);
      }
    }

    // Cleanup function to ensure scroll is re-enabled if component unmounts
    return () => {
      document.body.style.overflow = originalOverflow;
      document.body.style.position = "";
      document.body.style.top = "";
      document.body.style.width = "";
    };
  }, [lock]);
};

export default useScrollLock;
