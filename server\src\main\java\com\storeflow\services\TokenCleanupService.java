package com.storeflow.services;

import com.storeflow.repositories.TokenRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
public class TokenCleanupService {

    private final TokenRepository tokenRepository;

    @Value("${application.security.token.cleanup.retention-days:30}")
    private int tokenRetentionDays;

    /**
     * Cleanup expired and revoked tokens older than the configured retention period
     * Runs at midnight every day (0 0 0 * * *)
     */
    @Scheduled(cron = "0 0 0 * * *")
    public void cleanupExpiredTokens() {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(tokenRetentionDays);

        // First count how many tokens will be deleted (for logging)
        long tokensToDelete = tokenRepository.countExpiredAndRevokedTokensOlderThan(cutoffDate);

        if (tokensToDelete > 0) {
            log.info("Cleaning up {} expired and revoked tokens older than {}",
                tokensToDelete, cutoffDate);

            tokenRepository.deleteExpiredAndRevokedTokensOlderThan(cutoffDate);

            log.info("Successfully deleted {} expired and revoked tokens", tokensToDelete);
        } else {
            log.info("No expired and revoked tokens to clean up");
        }
    }

    /**
     * Method to manually trigger token cleanup
     * Can be exposed through an admin API if needed
     *
     * @param retentionDays Number of days to keep tokens before deletion
     * @return Number of tokens deleted
     */
    public long manualCleanup(int retentionDays) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(retentionDays);
        long tokensToDelete = tokenRepository.countExpiredAndRevokedTokensOlderThan(cutoffDate);

        if (tokensToDelete > 0) {
            log.info("Manual cleanup: Deleting {} expired and revoked tokens older than {}",
                tokensToDelete, cutoffDate);

            tokenRepository.deleteExpiredAndRevokedTokensOlderThan(cutoffDate);
        }

        return tokensToDelete;
    }
}
