package com.storeflow.models;

import jakarta.persistence.*;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Entity representing products owned by the company (internal inventory).
 * These are products that admin/employees have purchased from suppliers.
 */
@Entity
@Getter
@Setter
@Builder
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
@Table(
    name = "company_products",
    indexes = {
        @Index(name = "company_product_primary_key", columnList = "id", unique = true),
        @Index(name = "company_product_label_index", columnList = "label"),
        @Index(name = "company_product_product_index", columnList = "product_id")
    }
)
public class CompanyProduct {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @ManyToOne
    @JoinColumn(
        name = "product_id",
        foreignKey = @ForeignKey(name = "company_product_product_fk"),
        nullable = false
    )
    private Product product;

    @Column(nullable = false)
    private String label;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String description;

    @Column(nullable = false)
    private Integer stockQuantity;

    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal averageCostPrice;

    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal sellingPrice;

    @Column(nullable = false)
    private Integer averageDeliveryTime;

    @CreatedDate
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdDate;

    @LastModifiedDate
    @Column(insertable = false)
    private LocalDateTime lastModifiedDate;

    /**
     * Calculate the margin percentage
     * @return The margin percentage
     */
    @Transient
    public BigDecimal getMargin() {
        if (averageCostPrice != null && sellingPrice != null && sellingPrice.compareTo(BigDecimal.ZERO) > 0) {
            return sellingPrice.subtract(averageCostPrice)
                    .divide(sellingPrice, 2, java.math.RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
        }
        return BigDecimal.ZERO;
    }

    /**
     * Add stock to the company inventory
     * @param quantity The quantity to add
     * @param costPrice The cost price of the added stock
     */
    public void addStock(Integer quantity, BigDecimal costPrice) {
        if (quantity <= 0) return;
        
        // Calculate new average cost price
        BigDecimal currentTotalCost = averageCostPrice.multiply(BigDecimal.valueOf(stockQuantity));
        BigDecimal newTotalCost = costPrice.multiply(BigDecimal.valueOf(quantity));
        BigDecimal totalCost = currentTotalCost.add(newTotalCost);
        
        // Update stock quantity
        stockQuantity += quantity;
        
        // Update average cost price
        if (stockQuantity > 0) {
            averageCostPrice = totalCost.divide(BigDecimal.valueOf(stockQuantity), 2, java.math.RoundingMode.HALF_UP);
        }
    }

    /**
     * Remove stock from the company inventory
     * @param quantity The quantity to remove
     * @return true if successful, false if insufficient stock
     */
    public boolean removeStock(Integer quantity) {
        if (quantity <= 0 || stockQuantity < quantity) {
            return false;
        }
        
        stockQuantity -= quantity;
        return true;
    }
}
