import {
  setOpenConfigurator,
  setSidenavColor,
  setSidenavType,
  setFixedNavbar,
  useThemeController,
} from "@/context";
import { ripple } from "@/utils";
import { Typo<PERSON>, Button, Switch } from "@material-tailwind/react";
import { XIcon } from "lucide-react";

export const Configurator = () => {
  const [controller, dispatch] = useThemeController();
  const { openConfigurator, sidenavColor, sidenavType, fixedNavbar } =
    controller;

  const sidenavColors = {
    white: "bg-gray-100 border-gray-200",
    black: "bg-black border-gray-200",
    green: "bg-green-500",
    orange: "bg-orange-500",
    red: "bg-red-500",
    pink: "bg-pink-500",
  };

  return (
    <aside
      className={`text-slate-900 bg-white font-clash-grotesk fixed top-0 right-0 z-50 h-dvh w-full lg:w-96 shadow-lg transition-transform duration-300 ${
        openConfigurator ? "translate-x-0" : "translate-x-full lg:translate-x-96"
      }`}
    >
      <div className="h-full overflow-y-auto flex flex-col justify-between px-5">
        <div className="flex flex-col py-6">
          <div className="flex items-center justify-between w-full">
            <p className="text-xl font-semibold font-satoshi">Dashboard Configurator</p>
            <button
              className="hover:bg-blue-gray-50 p-2.5 rounded-lg transition-all cursor-pointer"
              onMouseDown={(event) => ripple.create(event, "dark")}
              onClick={() => setOpenConfigurator(dispatch, false)}
            >
              <XIcon className="size-5 text-blue-gray-600" />
            </button>
          </div>
          <p className="text-gray-600">See the dashboard options.</p>
        </div>

        <hr className="text-blue-gray-50" />

        <div className="py-6">
          <p className="text-lg font-semibold">Sidenav Colors</p>
          <div className="mt-3 flex items-center gap-2">
            {Object.keys(sidenavColors).map((color) => (
              <span
                key={color}
                className={`size-6 cursor-pointer rounded-full border transition-transform hover:scale-105 ${
                  sidenavColors[color]
                } ${
                  sidenavColor === color ? "border-black" : "border-transparent"
                }`}
                onClick={() => {
                  setSidenavColor(dispatch, color);
                  console.log(color);
                }}
              />
            ))}
          </div>
        </div>

        <hr className="text-blue-gray-50" />

        <div className="flex flex-col gap-y-2 py-6">
          <p className="text-lg font-semibold text-slate-900">Sidenav Types</p>
          <p className="text-slate-900">
            Choose between 3 different sidenav types.
          </p>
          <div className="flex items-center gap-2">
            <Button
              variant={sidenavType === "dark" ? "gradient" : "outlined"}
              onClick={() => setSidenavType(dispatch, "dark")}
              className="capitalize cursor-pointer font-clash-grotesk font-medium text-[15px] flex items-center justify-center"
              fullWidth
            >
              Dark
            </Button>
            <Button
              variant={sidenavType === "transparent" ? "gradient" : "outlined"}
              onClick={() => setSidenavType(dispatch, "transparent")}
              className="capitalize cursor-pointer font-clash-grotesk font-medium text-[15px] flex items-center justify-center"
              fullWidth
            >
              Transparent
            </Button>
            <Button
              variant={sidenavType === "white" ? "gradient" : "outlined"}
              onClick={() => setSidenavType(dispatch, "white")}
              className="capitalize cursor-pointer font-clash-grotesk font-medium text-[15px] flex items-center justify-center"
              fullWidth
            >
              White
            </Button>
          </div>
        </div>

        <hr className="text-blue-gray-50" />

        <div className="flex items-center justify-between py-6">
          <Typography
            variant="h6"
            color="blue-gray"
            className="font-clash-grotesk"
          >
            Navbar Fixed
          </Typography>
          <Switch
            id="navbar-fixed"
            value={fixedNavbar}
            onChange={() => setFixedNavbar(dispatch, !fixedNavbar)}
          />
        </div>
      </div>
    </aside>
  );
};

export default Configurator;
