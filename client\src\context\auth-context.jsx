import {
  createContext,
  useContext,
  useEffect,
  useState,
  useRef,
  useCallback,
} from "react";
import axios from "axios";
import { jwtDecode } from "jwt-decode";
import { setUserData, setLoading as setReduxLoading } from "@/redux/user-slice";
import { useDispatch } from "react-redux";
import { toast } from "sonner";

// Define API base URL
const API_URL = "http://localhost:8080";

// Create axios instance for auth requests
const authApi = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Create the auth context
const AuthContext = createContext();

// Create a custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  // State to store the current user and loading state
  const dispatch = useDispatch();
  const [user, setUser] = useState(null);
  const [accessToken, setAccessToken] = useState(
    localStorage.getItem("accessToken") || null
  );
  const [refreshToken, setRefreshToken] = useState(
    localStorage.getItem("refreshToken") || null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Ref to prevent concurrent refresh operations
  const refreshInProgress = useRef(false);

  // Function to do basic client-side token validation
  const isTokenValid = (token) => {
    if (!token) return false;

    try {
      const decoded = jwtDecode(token);

      // Check if token has expired
      const currentTime = Date.now() / 1000;
      if (decoded.exp < currentTime) {
        return false;
      }

      return true;
    } catch (error) {
      console.error("Error decoding token:", error);
      return false;
    }
  };

  // Function to check if user is authenticated
  const isAuthenticated = useCallback(() => {
    return !!accessToken && isTokenValid(accessToken);
  }, [accessToken]);

  // Function to get user info from token
  const getUserFromToken = (token) => {
    if (!token) return null;

    try {
      return jwtDecode(token);
    } catch (error) {
      console.error("Error decoding token:", error);
      return null;
    }
  };

  // Function to get user role from token
  const getUserRole = useCallback(() => {
    const userInfo = getUserFromToken(accessToken);
    // Return the role, ensuring it's lowercase for consistency
    return userInfo?.role ? userInfo.role.toLowerCase() : null;
  }, [accessToken]);

  // Function to clear all auth state
  const clearAuthState = () => {
    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    setAccessToken(null);
    setRefreshToken(null);
    setUser(null);
    // Clear Redux state
    dispatch(setUserData(null));
  };

  // Function to refresh the access token
  const refreshAccessToken = async () => {
    // Prevent concurrent refresh attempts
    if (refreshInProgress.current || !refreshToken) return false;

    refreshInProgress.current = true;

    try {
      const response = await authApi.post("/api/v1/auth/refresh-token", {
        refreshToken,
      });

      // console.log("Refresh token response:", response.data);

      // Extract tokens from response
      const { accessToken: newAccessToken, refreshToken: newRefreshToken } =
        response.data;

      if (!newAccessToken || !newRefreshToken) {
        throw new Error("Invalid response from refresh token endpoint");
      }

      // Update tokens in localStorage
      localStorage.setItem("accessToken", newAccessToken);
      localStorage.setItem("refreshToken", newRefreshToken);

      // Update state
      setAccessToken(newAccessToken);
      setRefreshToken(newRefreshToken);

      refreshInProgress.current = false;
      return true;
    } catch (err) {
      console.error(
        "Error refreshing token:",
        err.response?.data || err.message
      );
      clearAuthState();
      refreshInProgress.current = false;
      return false;
    }
  };

  // Function to fetch user profile from server
  const fetchUserProfile = async (token) => {
    if (!token || !isTokenValid(token)) {
      return null;
    }

    try {
      dispatch(setReduxLoading(true));
      const response = await authApi.get("/api/v1/auth/verify", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      // Extract user data from ApiResponse wrapper
      if (response.data.success && response.data.data) {
        const userData = response.data.data;
        // Update Redux store with user data
        dispatch(setUserData(userData));
        return userData;
      }

      dispatch(setReduxLoading(false));
      return null;
    } catch (error) {
      console.error(
        "Error fetching user profile:",
        error.response?.data || error.message
      );
      dispatch(setReduxLoading(false));
      return null;
    }
  };

  // Function to explicitly get user profile (for components to call directly)
  const getUserProfile = useCallback(async () => {
    if (!accessToken) return null;

    try {
      // Always fetch from server to get the most up-to-date profile data
      const userProfile = await fetchUserProfile(accessToken);

      // Don't update auth context user state - Redux is the source of truth for profile data
      // Auth context only maintains basic token info for role/auth checks

      return userProfile;
    } catch (error) {
      console.error("Error in getUserProfile:", error);

      // If unauthorized, try to refresh token
      if (error.response?.status === 401 && refreshToken) {
        const refreshed = await refreshAccessToken();
        if (refreshed) {
          // Try again with new token
          return getUserProfile(); // Recursive call with new token
        }
      }

      return null;
    }
  }, [accessToken, refreshToken]);

  // Function to handle login
  const login = async (credentials) => {
    setLoading(true);
    dispatch(setReduxLoading(true));
    setError(null);

    try {
      const response = await authApi.post("/api/v1/auth/login", credentials);

      // Extract tokens from response
      const { accessToken: newAccessToken, refreshToken: newRefreshToken } =
        response.data;

      if (!newAccessToken || !newRefreshToken) {
        throw new Error("Invalid response from login endpoint");
      }

      // Store tokens in localStorage for persistence
      localStorage.setItem("accessToken", newAccessToken);
      localStorage.setItem("refreshToken", newRefreshToken);

      // Update state
      setAccessToken(newAccessToken);
      setRefreshToken(newRefreshToken);

      // Get basic user info and update Redux FIRST
      const basicUserInfo = getUserFromToken(newAccessToken);

      if (basicUserInfo) {
        // Update local state with basic token info
        setUser(basicUserInfo);
        // Update Redux state with basic info
        dispatch(setUserData(basicUserInfo));

        // Then fetch full profile in background
        try {
          const userProfile = await fetchUserProfile(newAccessToken);
          if (userProfile) {
            // Only update auth context with basic token info, Redux already updated by fetchUserProfile
            setUser(basicUserInfo);
          }
        } catch (profileError) {
          console.error("Error fetching complete profile:", profileError);
        }
      }

      // Return success and the user role
      return {
        success: true,
        userRole: basicUserInfo?.role ? basicUserInfo.role.toLowerCase() : null,
      };
    } catch (err) {
      console.error("Login error:", err.response?.data || err.message);

      let errorMessage = "Login failed";
      // ... rest of error handling

      setError(errorMessage);
      return { success: false, userRole: null };
    } finally {
      setLoading(false);
      dispatch(setReduxLoading(false));
    }
  };

  // Function to handle logout
  const logout = async () => {
    try {
      if (accessToken && isTokenValid(accessToken)) {
        // Call logout API endpoint
        await authApi.post(
          "/api/v1/auth/logout",
          {},
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );
      }
    } catch (err) {
      console.error("Error during logout:", err.response?.data || err.message);
    } finally {
      clearAuthState();
      // Clear Redux state
      dispatch(setUserData(null));
    }
  };

  // Function to initiate Google OAuth
  const initiateGoogleOAuth = async () => {
    try {
      const baseUrl =
        import.meta.env.VITE_API_BASE_URL || "http://localhost:8080";
      const oauthUrl = `${baseUrl}/oauth2/authorization/google`;

      // Redirect to Google OAuth endpoint
      window.location.href = oauthUrl;
    } catch (error) {
      console.error("Error initiating Google OAuth:", error);
      setError("Failed to initiate Google authentication");
      return false;
    }
  };

  // Function to handle registration
  const register = async (userData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await authApi.post("/api/v1/auth/register", userData);

      console.log("Registration response:", response.data);

      if (response.data && response.data.success) {
        return true;
      } else {
        throw new Error(response.data.message || "Registration failed");
      }
    } catch (err) {
      console.error("Registration error:", err.response?.data || err.message);

      let errorMessage = "Registration failed";
      if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Function to update auth tokens (for external components like OAuth completion)
  const updateAuthTokens = (newAccessToken, newRefreshToken) => {
    // Store tokens in localStorage
    localStorage.setItem("accessToken", newAccessToken);
    localStorage.setItem("refreshToken", newRefreshToken);

    // Update state
    setAccessToken(newAccessToken);
    setRefreshToken(newRefreshToken);

    // Get basic user info from token and update state
    const basicUserInfo = getUserFromToken(newAccessToken);
    if (basicUserInfo) {
      setUser(basicUserInfo);
      dispatch(setUserData(basicUserInfo));
    }
  };

  // Set up interceptor to handle token refresh
  useEffect(() => {
    const requestInterceptor = authApi.interceptors.request.use(
      (config) => {
        // Only add token if it exists and is valid
        if (
          accessToken &&
          isTokenValid(accessToken) &&
          !config.headers.Authorization
        ) {
          config.headers.Authorization = `Bearer ${accessToken}`;
          console.log("Added auth token to request:", config.url);
        } else if (!accessToken) {
          console.warn("No access token available for request:", config.url);
        } else if (!isTokenValid(accessToken)) {
          console.warn("Invalid token for request:", config.url);
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    const responseInterceptor = authApi.interceptors.response.use(
      (response) => {
        // Log successful responses for debugging
        console.log(
          `API Response success [${response.config.url}]:`,
          response.status
        );
        return response;
      },
      async (error) => {
        // Log the error for debugging
        console.error(
          `API Response error [${error.config?.url}]:`,
          error.response?.status || "Network Error"
        );

        const originalRequest = error.config;

        // Prevent refresh attempts for refresh-token endpoint itself
        const isRefreshEndpoint = originalRequest.url.includes("refresh-token");

        // If unauthorized error and not already retrying and not refresh endpoint
        if (
          error.response?.status === 401 &&
          !originalRequest._retry &&
          refreshToken &&
          !isRefreshEndpoint &&
          !refreshInProgress.current
        ) {
          console.log("Attempting to refresh token due to 401 error");
          originalRequest._retry = true;

          try {
            // Try to refresh the token
            const refreshed = await refreshAccessToken();

            if (refreshed) {
              console.log("Token refreshed successfully, retrying request");
              // Update the Authorization header with the new token
              originalRequest.headers.Authorization = `Bearer ${accessToken}`;

              // Retry the original request
              return authApi(originalRequest);
            } else {
              console.warn("Token refresh failed");
              // If we couldn't refresh, clear auth state
              clearAuthState();
            }
          } catch (refreshError) {
            console.error(
              "Error during token refresh in interceptor:",
              refreshError
            );
            clearAuthState();
            return Promise.reject(refreshError);
          }
        } else if (error.response?.status === 403) {
          console.warn("Forbidden access (403) detected");
          // Handle forbidden errors - might be a permission issue
          toast.error("You don't have permission to access this resource");
        }

        return Promise.reject(error);
      }
    );

    // Clean up interceptors on unmount
    return () => {
      authApi.interceptors.request.eject(requestInterceptor);
      authApi.interceptors.response.eject(responseInterceptor);
    };
  }, [accessToken, refreshToken]);

  // Proactive token refresh based on expiration time
  useEffect(() => {
    if (!accessToken || !isTokenValid(accessToken)) {
      return;
    }

    const tokenInfo = getUserFromToken(accessToken);
    if (!tokenInfo || !tokenInfo.exp) {
      return;
    }

    const expiresAt = tokenInfo.exp * 1000; // Convert to milliseconds
    const timeUntilExpiry = expiresAt - Date.now();

    // Refresh at 80% of lifetime
    const refreshTime = timeUntilExpiry * 0.8;

    // Don't schedule if already expired or too close to expiry
    if (refreshTime < 5000) {
      // At least 5 seconds in the future
      return;
    }

    // console.log(
    //   `Scheduling token refresh in ${Math.round(refreshTime / 1000)} seconds`
    // );

    const refreshTimerId = setTimeout(() => {
      // console.log("Executing scheduled token refresh");
      refreshAccessToken();
    }, refreshTime);

    // Clean up timer on unmount or when token changes
    return () => clearTimeout(refreshTimerId);
  }, [accessToken]);

  // Refresh on app focus
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && isAuthenticated()) {
        const tokenInfo = getUserFromToken(accessToken);
        if (tokenInfo && tokenInfo.iat) {
          const issueTime = tokenInfo.iat * 1000;
          const tokenAge = Date.now() - issueTime;

          // If token is older than 5 minutes, refresh it
          if (tokenAge > 5 * 60 * 1000) {
            console.log("Refreshing token on app focus");
            refreshAccessToken();
          }
        }
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () =>
      document.removeEventListener("visibilitychange", handleVisibilityChange);
  }, [accessToken]);

  // Initialize auth state on mount
  useEffect(() => {
    const initializeAuth = async () => {
      setLoading(true);

      if (accessToken) {
        // Check if token is valid client-side
        if (isTokenValid(accessToken)) {
          // Set basic user info from token immediately for faster UI
          const basicUserInfo = getUserFromToken(accessToken);
          setUser(basicUserInfo);
          // Update Redux store with basic info
          dispatch(setUserData(basicUserInfo));

          // Fetch full user profile asynchronously
          const userProfile = await fetchUserProfile(accessToken);
          if (userProfile) {
            // Only update auth context with basic token info, Redux already updated by fetchUserProfile
            setUser(basicUserInfo);
          }
        } else if (refreshToken) {
          // Token is expired but we have refresh token
          const refreshed = await refreshAccessToken();
          if (!refreshed) {
            clearAuthState();
          }
        } else {
          clearAuthState();
        }
      }

      setLoading(false);
    };

    initializeAuth();
  }, []);

  // The context value that will be provided
  const value = {
    user,
    accessToken,
    refreshToken,
    loading,
    error,
    isAuthenticated,
    getUserRole,
    login,
    logout,
    register,
    initiateGoogleOAuth,
    refreshAccessToken,
    getUserProfile,
    updateAuthTokens,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Export the auth API for use in components
export { authApi };
