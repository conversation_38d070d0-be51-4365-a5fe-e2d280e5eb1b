package com.storeflow.enums;

/**
 * Enum representing the status of a purchase from a supplier.
 */
public enum PurchaseStatus {
    /**
     * The purchase has been created but not yet processed
     */
    PENDING,
    
    /**
     * The purchase has been confirmed and is being processed
     */
    CONFIRMED,
    
    /**
     * The purchase has been completed and the products have been received
     */
    COMPLETED,
    
    /**
     * The purchase has been cancelled
     */
    CANCELLED
}
