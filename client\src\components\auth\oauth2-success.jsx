import { useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useAuth, authApi } from "@/context/auth-context";
import toast from "react-hot-toast";
import { useDispatch } from "react-redux";
import { setUserData } from "@/redux/user-slice";

export const OAuth2Success = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { getUserRole, updateAuthTokens } = useAuth();
  const dispatch = useDispatch();

  useEffect(() => {
    const handleOAuth2Success = async () => {
      try {
        const accessToken = searchParams.get("accessToken");
        const refreshToken = searchParams.get("refreshToken");
        const message = searchParams.get("message");

        if (!accessToken || !refreshToken) {
          toast.error("Invalid authentication session. Please try again.");
          navigate("/auth/login");
          return;
        }

        // Update auth tokens in context (this will also fetch full profile data)
        await updateAuthTokens(
          decodeURIComponent(accessToken),
          decodeURIComponent(refreshToken)
        );

        if (message) {
          toast.success(decodeURIComponent(message));
        }

        // Additional profile fetch to ensure we have the latest data
        try {
          const profileResponse = await authApi.get("/api/v1/auth/verify", {
            headers: {
              Authorization: `Bearer ${decodeURIComponent(accessToken)}`,
            },
          });

          if (profileResponse.data.success && profileResponse.data.data) {
            // Update Redux store with complete user data including profile picture
            dispatch(setUserData(profileResponse.data.data));
            console.log(
              "Existing Google OAuth user profile updated:",
              profileResponse.data.data
            );
          }
        } catch (profileError) {
          console.error("Error fetching user profile:", profileError);
          // Continue with redirect even if profile fetch fails
        }

        // Get user role and redirect to appropriate dashboard
        const userRole = getUserRole();
        let redirectTo;

        switch (userRole) {
          case "admin":
            redirectTo = "/admin/dashboard";
            break;
          case "client":
            redirectTo = "/client/dashboard";
            break;
          case "supplier":
            redirectTo = "/supplier/dashboard";
            break;
          case "employee":
            redirectTo = "/employee/dashboard";
            break;
          default:
            redirectTo = "/client/dashboard"; // Default to client for Google OAuth users
            break;
        }

        navigate(redirectTo, { replace: true });
      } catch (error) {
        console.error("OAuth2 success handling error:", error);
        toast.error("Authentication failed. Please try again.");
        navigate("/auth/login");
      }
    };

    handleOAuth2Success();
  }, [searchParams, navigate, getUserRole]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-blue-gray-50/40">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p className="text-blue-gray-700 text-lg">
          Completing authentication...
        </p>
      </div>
    </div>
  );
};

export default OAuth2Success;
