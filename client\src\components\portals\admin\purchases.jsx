import { useState } from "react";
import { Helmet } from "react-helmet-async";
import { PurchaseHistory } from "@/components/shared";

/**
 * Admin Purchases page component
 * Displays the purchase history for the admin
 */
const AdminPurchases = () => {
  return (
    <>
      <Helmet>
        <title>Purchases | Admin Portal</title>
      </Helmet>

      <div className="container mx-auto">
        <PurchaseHistory />
      </div>
    </>
  );
};

export default AdminPurchases;
