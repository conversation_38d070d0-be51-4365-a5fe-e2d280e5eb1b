import axios from "axios";

// Define API base URL
const API_URL = "http://localhost:8080";

/**
 * Create a direct API client that doesn't rely on the auth context
 * This is useful for debugging API issues
 */
export const createDirectApiClient = () => {
  // Create a new axios instance
  const client = axios.create({
    baseURL: API_URL,
    headers: {
      "Content-Type": "application/json",
    },
  });

  // Add request interceptor to add auth token
  client.interceptors.request.use(
    (config) => {
      // Get token from localStorage
      const token = localStorage.getItem("accessToken");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
        console.log(`[Direct API] Adding auth token to request: ${config.url}`);
      } else {
        console.warn(`[Direct API] No auth token available for request: ${config.url}`);
      }
      return config;
    },
    (error) => {
      console.error("[Direct API] Request interceptor error:", error);
      return Promise.reject(error);
    }
  );

  // Add response interceptor for logging
  client.interceptors.response.use(
    (response) => {
      console.log(`[Direct API] Response success [${response.config.url}]:`, response.status);
      return response;
    },
    (error) => {
      if (error.response) {
        console.error(
          `[Direct API] Response error [${error.config?.url}]:`,
          error.response.status,
          error.response.data
        );
      } else if (error.request) {
        console.error(
          `[Direct API] No response received [${error.config?.url}]:`,
          error.request
        );
      } else {
        console.error(
          `[Direct API] Request setup error [${error.config?.url}]:`,
          error.message
        );
      }
      return Promise.reject(error);
    }
  );

  return client;
};

// Create a shared instance for common use
export const directApi = createDirectApiClient();
