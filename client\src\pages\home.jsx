import {
  About,
  Footer,
  Main,
  Navbar,
  Products,
  Services,
} from "@/components/home";
import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";

export const Home = () => {
  const location = useLocation();
  const [isPastMain, setIsPastMain] = useState(false);

  useEffect(() => {
    // Smooth scrolling for hash links
    if (location.hash) {
      const element = document.getElementById(location.hash.substring(1));
      if (element) {
        element.scrollIntoView({ behavior: "smooth", block: "start" });
      }
    }
  }, [location]);

  useEffect(() => {
    const handleScroll = () => {
      setIsPastMain(window.scrollY > 0);
    };

    // Run once when the component mounts to handle refresh scenarios
    handleScroll();

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <>
      <Navbar isPastMain={isPastMain} />
      <Main />
      <About />
      <Services />
      <Products />
      <Footer />
    </>
  );
};

export default Home;
