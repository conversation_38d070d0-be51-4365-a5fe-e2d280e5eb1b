import { ripple } from "@/utils";
import { Link } from "react-router-dom";

export const DesktopAuthLinks = () => {
  return (
    <div className="hidden lg:flex gap-x-0.5 grow items-center justify-center text-xl font-medium self-stretch">
      <Link to="/auth/login">
        <button
          onMouseDown={(e) => ripple.create(e, "dark")}
          className="bg-white hover:bg-gray-200 border border-white text-blue-gray-700 rounded-lg text-center py-2 w-24 transition-all duration-200"
        >
          Log in
        </button>
      </Link>

      <Link to="/auth/signup">
        <button className="text-white hover:text-gray-200 rounded-lg text-center py-2 w-24 transition-all duration-200">
          Sign up
        </button>
      </Link>
    </div>
  );
};

export default DesktopAuthLinks;
