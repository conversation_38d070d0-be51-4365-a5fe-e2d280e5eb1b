package com.storeflow.requests;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

public record ChangePasswordRequest(
    @NotBlank(message = "Current password is required.")
    @Size(min = 8, message = "Password must contain at least 8 characters.")
    String currentPassword,

    @NotBlank(message = "New password is required.")
    @Size(min = 8, message = "Password must contain at least 8 characters.")
    @Pattern(regexp = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[!\"#$%&'()*+,-./:;<=>?@\\[\\]^_`{|}~]).{8,}$",
        message = "Password must contain at least one digit, one lowercase letter, one uppercase letter, and one special character.")
    String newPassword,

    @NotBlank(message = "Confirmation password is required.")
    @Size(min = 8, message = "Password must contain at least 8 characters.")
    @Pattern(regexp = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[!\"#$%&'()*+,-./:;<=>?@\\[\\]^_`{|}~]).{8,}$",
        message = "Password must contain at least one digit, one lowercase letter, one uppercase letter, and one special character.")
    String confirmationPassword
) {
}
