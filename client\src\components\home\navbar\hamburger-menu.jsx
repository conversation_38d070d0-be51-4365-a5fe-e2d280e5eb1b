export const HamburgerMenu = ({ isOpen, toggleMenu }) => {
  return (
    <div
      className="flex grow items-end justify-center flex-col gap-y-[5px] pr-5 w-1/2 text-xl font-medium self-stretch cursor-pointer"
      onClick={toggleMenu}
    >
      <span
        className={`w-[25px] h-[3px] bg-white rounded-full transition-all duration-300 ${
          isOpen ? "transform rotate-45 translate-y-[8px]" : ""
        }`}
      ></span>
      <span
        className={`w-[25px] h-[3px] bg-white rounded-full transition-all duration-300 ${
          isOpen ? "opacity-0" : ""
        }`}
      ></span>
      <span
        className={`w-[25px] h-[3px] bg-white rounded-full transition-all duration-300 ${
          isOpen ? "transform -rotate-45 -translate-y-[8px]" : ""
        }`}
      ></span>
    </div>
  );
};

export default HamburgerMenu;
