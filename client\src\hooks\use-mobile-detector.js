// useMobileDetector.js
import { isDesktop } from "@/utils";
import { useState, useEffect } from "react";

export const useMobileDetector = (onMobileChange) => {
  const [isOnMobile, setIsOnMobile] = useState(isDesktop());

  useEffect(() => {
    const handleResize = () => {
      const notDesktop = isDesktop();
      setIsOnMobile(notDesktop);

      if (typeof onMobileChange === "function") {
        onMobileChange(notDesktop);
      }
    };

    // Add event listener
    window.addEventListener("resize", handleResize);

    // Initial check
    handleResize();

    // Cleanup
    return () => window.removeEventListener("resize", handleResize);
  }, [onMobileChange]);

  return isOnMobile;
};

export default useMobileDetector;
