import { useAuth } from "@/context/auth-context";

export const AuthTester = () => {
  const { user, loading, error, isAuthenticated, login, logout, register } =
    useAuth();

  const handleLogin = async () => {
    const result = await login({
      email: "<EMAIL>",
      password: "initialPassword123",
    });

    console.log("Login result:", result);
  };

  const handleLogout = async () => {
    await logout();
    console.log("Logged out");
  };

  return (
    <div className="p-4 border rounded-lg max-w-md mx-auto mt-8">
      <h2 className="text-xl font-bold mb-4">Auth Context Tester</h2>

      <div className="mb-4">
        <p>
          <strong>Loading:</strong> {loading ? "Yes" : "No"}
        </p>
        <p>
          <strong>Authenticated:</strong> {isAuthenticated() ? "Yes" : "No"}
        </p>
        <p>
          <strong>Error:</strong> {error || "None"}
        </p>
      </div>

      {user ? (
        <div className="mb-4 p-3 bg-blue-50 rounded">
          <h3 className="font-semibold">User Info:</h3>
          <pre className="whitespace-pre-wrap text-sm">
            {JSON.stringify(user, null, 2)}
          </pre>
        </div>
      ) : (
        <p className="mb-4">No user logged in</p>
      )}

      <div className="flex gap-2">
        <button
          onClick={handleLogin}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          disabled={loading}
        >
          Test Login
        </button>

        <button
          onClick={handleLogout}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          disabled={loading}
        >
          Test Logout
        </button>
      </div>
    </div>
  );
};

export default AuthTester;
