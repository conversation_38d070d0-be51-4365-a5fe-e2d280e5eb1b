package com.storeflow.models;

import com.storeflow.enums.PurchaseStatus;
import jakarta.persistence.*;
import lombok.*;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Entity representing a purchase made by an admin or employee from a supplier.
 */
@Entity
@Getter
@Setter
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@EntityListeners(AuditingEntityListener.class)
@Table(
    name = "purchases",
    indexes = {
        @Index(name = "purchase_primary_key", columnList = "id", unique = true),
        @Index(name = "purchase_supplier_index", columnList = "supplier_id"),
        @Index(name = "purchase_status_index", columnList = "status"),
        @Index(name = "purchase_date_index", columnList = "purchase_date")
    }
)
public class Purchase {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @ManyToOne
    @JoinColumn(
        name = "supplier_id",
        foreignKey = @ForeignKey(name = "purchase_supplier_fk"),
        nullable = true  // Allow null for historical data preservation when supplier is deleted
    )
    private Supplier supplier;

    // Preserve supplier information for historical data even after supplier deletion
    @Column(name = "supplier_name")
    private String supplierName;

    @Column(name = "supplier_email")
    private String supplierEmail;

    @Column(name = "purchase_date", nullable = false)
    private LocalDateTime purchaseDate;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PurchaseStatus status;

    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal totalAmount;

    @Column(columnDefinition = "TEXT")
    private String notes;

    @OneToMany(mappedBy = "purchase", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<PurchaseItem> items = new ArrayList<>();

    @CreatedDate
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdDate;

    @LastModifiedDate
    @Column(insertable = false)
    private LocalDateTime lastModifiedDate;

    @CreatedBy
    @Column(nullable = false, updatable = false)
    private String createdBy;

    @LastModifiedBy
    @Column(insertable = false)
    private String lastModifiedBy;

    /**
     * Helper method to add an item to the purchase
     * @param item The purchase item to add
     */
    public void addItem(PurchaseItem item) {
        if (items == null) {
            items = new ArrayList<>();
        }
        items.add(item);
        item.setPurchase(this);
    }

    /**
     * Helper method to remove an item from the purchase
     * @param item The purchase item to remove
     */
    public void removeItem(PurchaseItem item) {
        if (items != null) {
            items.remove(item);
            item.setPurchase(null);
        }
    }

    /**
     * Calculate the total amount of the purchase based on the items
     */
    public void calculateTotalAmount() {
        if (items == null) {
            this.totalAmount = BigDecimal.ZERO;
            return;
        }
        this.totalAmount = items.stream()
            .map(item -> item.getUnitPrice().multiply(BigDecimal.valueOf(item.getQuantity())))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Custom builder to ensure items list is initialized
     */
    public static class PurchaseBuilder {
        private List<PurchaseItem> items;

        public PurchaseBuilder() {
            this.items = new ArrayList<>();
        }

        public PurchaseBuilder items(List<PurchaseItem> items) {
            this.items = items != null ? items : new ArrayList<>();
            return this;
        }
    }
}
