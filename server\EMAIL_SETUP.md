# Email Configuration Setup

This document explains how to set up email functionality for the password reset feature.

## Required Environment Variables

Add the following environment variables to your system or IDE configuration:

```bash
# Email Configuration
EMAIL_USERNAME=your_email
EMAIL_PASSWORD=your_app_password_here
EMAIL_FROM=your_email
FRONTEND_URL=http://localhost:5173
```

## Gmail App Password Setup

Since we're using Gmail SMTP, you need to create an App Password:

1. Go to your Google Account settings
2. Navigate to Security
3. Enable 2-Factor Authentication if not already enabled
4. Go to "App passwords"
5. Generate a new app password for "Mail"
6. Use this generated password as `EMAIL_PASSWORD`

**Important:** Do NOT use your regular Gmail password. Use the generated App Password.

## Testing the Email Functionality

1. Start the backend server with the environment variables configured
2. Go to the login page in the frontend
3. Click "Forgot password?"
4. Enter a valid email address that exists in your database
5. Check the email inbox for the reset link
6. Click the reset link and set a new password

## Troubleshooting

### Common Issues:

1. **Authentication failed**: Make sure you're using an App Password, not your regular password
2. **Connection timeout**: Check if your firewall allows outbound connections on port 587
3. **Invalid credentials**: Verify the EMAIL_USERNAME and EMAIL_PASSWORD are correct
4. **Email not received**: Check spam folder, and ensure the email exists in your database

### Email Service Alternatives

If Gmail doesn't work, you can modify the SMTP settings in `application.properties`:

For other email providers, update these properties:
```properties
spring.mail.host=your-smtp-host
spring.mail.port=your-smtp-port
spring.mail.username=your-email
spring.mail.password=your-password
```

## Security Notes

- Never commit email credentials to version control
- Use environment variables or secure configuration management
- Consider using OAuth2 for production environments
- Regularly rotate email passwords/tokens
