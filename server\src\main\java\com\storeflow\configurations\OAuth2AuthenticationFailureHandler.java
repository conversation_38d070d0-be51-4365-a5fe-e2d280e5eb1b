package com.storeflow.configurations;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class OAuth2AuthenticationFailureHandler implements AuthenticationFailureHandler {

    @Value("${application.frontend.url}")
    private String frontendUrl;

    @Override
    public void onAuthenticationFailure(
        HttpServletRequest request,
        HttpServletResponse response,
        AuthenticationException exception
    ) throws IOException {
        log.error("OAuth2 authentication failed: {}", exception.getMessage(), exception);
        
        String errorMessage = URLEncoder.encode(
            exception.getMessage() != null ? exception.getMessage() : "Authentication failed",
            StandardCharsets.UTF_8
        );
        
        String redirectUrl = String.format(
            "%s/auth/oauth2/error?error=%s",
            frontendUrl, errorMessage
        );
        
        log.info("Redirecting to OAuth error page: {}", redirectUrl);
        response.sendRedirect(redirectUrl);
    }
}
