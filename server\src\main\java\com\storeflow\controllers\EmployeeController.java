package com.storeflow.controllers;

import com.storeflow.DTOs.EmployeeDTO;
import com.storeflow.requests.EmployeeAdditionRequest;
import com.storeflow.responses.ApiResponse;
import com.storeflow.services.EmployeeService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("/api/v1/employees")
@RequiredArgsConstructor
public class EmployeeController {
    private final EmployeeService employeeService;

    @PostMapping
//    @PreAuthorize("hasAuthority('admin:create')")
    public ResponseEntity<ApiResponse<UUID>> createEmployee(
        @Valid @RequestBody EmployeeAdditionRequest employee
    ) {
        log.info("Request to create new employee: {} {}", employee.firstName(), employee.lastName());
        UUID employeeId = employeeService.addEmployee(employee);

        ApiResponse<UUID> response = ApiResponse.<UUID>builder()
            .success(true)
            .message("Employee created successfully.")
            .data(employeeId)
            .build();

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping
//    @PreAuthorize("hasAnyAuthority('admin:read', 'employee:read')")
    public ResponseEntity<ApiResponse<Page<EmployeeDTO>>> getAllEmployees(
        @RequestParam(required = false, defaultValue = "") String query,
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(defaultValue = "addedDate") String sortBy,
        @RequestParam(defaultValue = "desc") String sortDir
    ) {
        log.info("Request to get all employees with query: '{}', page: {}, size: {}, sortBy: {}, sortDir: {}",
                query, page, size, sortBy, sortDir);

        // Create sort object - map frontend field names to entity field names
        String entitySortBy = mapSortField(sortBy);
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), entitySortBy);

        // Create pageable object with sorting
        Pageable pageable = PageRequest.of(page, size, sort);

        // Get employees with pagination and search
        Page<EmployeeDTO> employees = employeeService.getAllEmployees(query, pageable);

        String message = employees.isEmpty() ? "No employees found." : "Employees retrieved successfully.";

        ApiResponse<Page<EmployeeDTO>> response = ApiResponse.<Page<EmployeeDTO>>builder()
            .success(true)
            .message(message)
            .data(employees)
            .build();

        return ResponseEntity.status(HttpStatus.OK).body(response);
    }

    /**
     * Maps frontend sort field names to entity field names
     */
    private String mapSortField(String sortBy) {
        return switch (sortBy) {
            case "addedDate" -> "user.addedDate";
            case "firstName" -> "user.firstName";
            case "lastName" -> "user.lastName";
            case "email" -> "user.email";
            case "phoneNumber" -> "user.phoneNumber";
            default -> "user.addedDate"; // Default to added date
        };
    }

    @DeleteMapping("/{employeeId}")
//    @PreAuthorize("hasAuthority('admin:delete')")
    public ResponseEntity<ApiResponse<Void>> deleteEmployee(
        @PathVariable UUID employeeId
    ) {
        log.info("Request to delete employee with ID: {}", employeeId);

        employeeService.deleteEmployee(employeeId);

        ApiResponse<Void> response = ApiResponse.<Void>builder()
            .success(true)
            .message("Employee deleted successfully.")
            .data(null)
            .build();

        return ResponseEntity.status(HttpStatus.OK).body(response);
    }
}
