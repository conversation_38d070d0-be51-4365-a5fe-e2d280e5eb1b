import { authApi } from "@/context/auth-context";

/**
 * Service for admin analytics-related API calls
 */
export const adminAnalyticsService = {
  /**
   * Get monthly purchase analytics data (units sold and revenue)
   * @returns {Promise<Object>} - Promise resolving to API response with monthly data
   */
  getMonthlyPurchaseAnalytics: async () => {
    try {
      // Since the analytics endpoint doesn't exist, we'll create synthetic monthly data
      // from the regular purchases endpoint
      console.log("Fetching purchases for monthly analytics...");
      const response = await authApi.get("/api/v1/purchases", {
        params: {
          page: 0,
          size: 100, // Get a larger number to have enough data for analysis
          sortBy: "purchaseDate",
          sortDir: "desc",
        },
      });

      console.log("Raw purchase response for monthly data:", response.data);

      if (response.data.success && response.data.data) {
        const purchases = response.data.data.content || [];

        // Create monthly data structure
        const monthNames = [
          "Jan",
          "Feb",
          "Mar",
          "Apr",
          "May",
          "Jun",
          "Jul",
          "Aug",
          "Sep",
          "Oct",
          "Nov",
          "Dec",
        ];
        const monthlyData = {};

        // Initialize monthly data
        monthNames.forEach((month) => {
          monthlyData[month] = {
            purchases: 0,
            revenue: 0,
          };
        });

        // Process purchases to aggregate by month
        console.log("Processing purchases for monthly data:", purchases);

        // Inspect the first purchase to understand its structure
        if (purchases.length > 0) {
          console.log(
            "First purchase structure for monthly data:",
            JSON.stringify(purchases[0], null, 2)
          );
        }

        purchases.forEach((purchase) => {
          // Check for different date field names
          const purchaseDate =
            purchase.purchaseDate || purchase.date || purchase.createdDate;

          if (purchaseDate) {
            const date = new Date(purchaseDate);
            const month = monthNames[date.getMonth()];
            console.log("Purchase date:", purchaseDate, "Month:", month);

            // Increment purchase count for this month
            monthlyData[month].purchases += 1;

            // Try different possible field names for total price
            let purchaseAmount = 0;

            // Log the purchase object to see its structure
            console.log(
              "Purchase object structure:",
              JSON.stringify(purchase, null, 2)
            );

            if (typeof purchase.totalPrice === "number") {
              purchaseAmount = purchase.totalPrice;
              console.log("Found totalPrice field:", purchaseAmount);
            } else if (typeof purchase.total === "number") {
              purchaseAmount = purchase.total;
              console.log("Found total field:", purchaseAmount);
            } else if (purchase.items && Array.isArray(purchase.items)) {
              // If no direct total price, calculate from items
              purchaseAmount = purchase.items.reduce((sum, item) => {
                const itemPrice = item.price || item.unitPrice || 0;
                const quantity = item.quantity || 1;
                return sum + itemPrice * quantity;
              }, 0);
              console.log("Calculated items total:", purchaseAmount);
            }

            // If we still don't have a value, try parsing string values
            if (purchaseAmount === 0) {
              if (typeof purchase.totalPrice === "string") {
                try {
                  purchaseAmount = parseFloat(purchase.totalPrice);
                  console.log("Parsed totalPrice string:", purchaseAmount);
                } catch (e) {
                  console.log("Failed to parse totalPrice string");
                }
              } else if (typeof purchase.total === "string") {
                try {
                  purchaseAmount = parseFloat(purchase.total);
                  console.log("Parsed total string:", purchaseAmount);
                } catch (e) {
                  console.log("Failed to parse total string");
                }
              }
            }

            // Add to revenue for this month
            if (purchaseAmount > 0) {
              console.log("Adding to revenue for", month, ":", purchaseAmount);
              monthlyData[month].revenue += purchaseAmount;
            }
          } else {
            console.log("Purchase missing date:", purchase);
          }
        });

        console.log("Monthly data after processing:", monthlyData);

        // Convert to arrays for charts
        const purchasesData = Object.keys(monthlyData).map((month) => ({
          name: month,
          count: monthlyData[month].purchases,
        }));

        const revenueData = Object.keys(monthlyData).map((month) => ({
          name: month,
          amount: monthlyData[month].revenue,
        }));

        return {
          success: true,
          message: "Monthly purchase data generated successfully",
          data: {
            purchases: purchasesData,
            revenue: revenueData,
          },
        };
      }

      return {
        success: false,
        message: "Failed to generate monthly purchase data",
        data: null,
      };
    } catch (error) {
      console.error("Error generating monthly purchase analytics:", error);
      return {
        success: false,
        message: "Failed to generate monthly purchase data",
        data: null,
      };
    }
  },

  /**
   * Get overall purchase statistics
   * @returns {Promise<Object>} - Promise resolving to API response with statistics
   */
  getPurchaseStatistics: async () => {
    try {
      // Since the analytics endpoint doesn't exist, we'll calculate statistics
      // from the regular purchases endpoint
      console.log("Fetching purchases for statistics calculation...");
      const response = await authApi.get("/api/v1/purchases", {
        params: {
          page: 0,
          size: 100, // Get a larger number to have enough data for analysis
          sortBy: "purchaseDate",
          sortDir: "desc",
        },
      });

      console.log("Raw purchase response:", response.data);

      if (response.data.success && response.data.data) {
        const purchases = response.data.data.content || [];
        const totalElements = response.data.data.totalElements || 0;

        // Calculate total spent
        let totalSpent = 0;
        console.log("Processing purchases for statistics:", purchases);

        // Inspect the first purchase to understand its structure
        if (purchases.length > 0) {
          console.log(
            "First purchase structure:",
            JSON.stringify(purchases[0], null, 2)
          );
        }

        purchases.forEach((purchase) => {
          console.log("Processing purchase:", purchase);

          // Try different possible field names for total price
          let purchaseAmount = 0;

          if (typeof purchase.totalPrice === "number") {
            purchaseAmount = purchase.totalPrice;
            console.log("Found totalPrice field:", purchaseAmount);
          } else if (typeof purchase.total === "number") {
            purchaseAmount = purchase.total;
            console.log("Found total field:", purchaseAmount);
          } else if (purchase.items && Array.isArray(purchase.items)) {
            // If no direct total price, calculate from items
            console.log("Calculating total from items:", purchase.items);
            purchaseAmount = purchase.items.reduce((sum, item) => {
              const itemPrice = item.price || item.unitPrice || 0;
              const quantity = item.quantity || 1;
              return sum + itemPrice * quantity;
            }, 0);
            console.log("Calculated items total:", purchaseAmount);
          }

          // If we still don't have a value, try parsing string values
          if (purchaseAmount === 0) {
            if (typeof purchase.totalPrice === "string") {
              try {
                purchaseAmount = parseFloat(purchase.totalPrice);
                console.log("Parsed totalPrice string:", purchaseAmount);
              } catch (e) {
                console.log("Failed to parse totalPrice string");
              }
            } else if (typeof purchase.total === "string") {
              try {
                purchaseAmount = parseFloat(purchase.total);
                console.log("Parsed total string:", purchaseAmount);
              } catch (e) {
                console.log("Failed to parse total string");
              }
            }
          }

          if (purchaseAmount > 0) {
            console.log("Adding to total spent:", purchaseAmount);
            totalSpent += purchaseAmount;
          }
        });

        console.log(
          "Total purchases:",
          totalElements,
          "Total spent:",
          totalSpent
        );

        return {
          success: true,
          message: "Purchase statistics calculated successfully",
          data: {
            totalPurchases: totalElements,
            totalSpent: totalSpent,
            // Add other statistics as needed
          },
        };
      }

      return {
        success: false,
        message: "Failed to calculate purchase statistics",
        data: null,
      };
    } catch (error) {
      console.error("Error calculating purchase statistics:", error);
      return {
        success: false,
        message: "Failed to calculate purchase statistics",
        data: null,
      };
    }
  },

  /**
   * Get count of suppliers
   * @returns {Promise<Object>} - Promise resolving to API response with supplier count
   */
  getSupplierCount: async () => {
    try {
      // Get first page with size=1 to get total elements count
      const response = await authApi.get("/api/v1/suppliers", {
        params: {
          page: 0,
          size: 1,
        },
      });

      return {
        success: response.data.success,
        message: response.data.message,
        data: response.data.data?.totalElements || 0,
      };
    } catch (error) {
      console.error("Error fetching supplier count:", error);
      return {
        success: false,
        message:
          error.response?.data?.message || "Failed to fetch supplier count",
        data: 0,
      };
    }
  },

  /**
   * Get count of employees
   * @returns {Promise<Object>} - Promise resolving to API response with employee count
   */
  getEmployeeCount: async () => {
    try {
      // Get first page with size=1 to get total elements count
      const response = await authApi.get("/api/v1/employees", {
        params: {
          page: 0,
          size: 1,
        },
      });

      return {
        success: response.data.success,
        message: response.data.message,
        data: response.data.data?.totalElements || 0,
      };
    } catch (error) {
      console.error("Error fetching employee count:", error);
      return {
        success: false,
        message:
          error.response?.data?.message || "Failed to fetch employee count",
        data: 0,
      };
    }
  },

  /**
   * Get count of clients
   * @returns {Promise<Object>} - Promise resolving to API response with client count
   */
  getClientCount: async () => {
    try {
      // Use the new backend endpoint to get client count
      const response = await authApi.get("/api/v1/users/count/clients");

      return {
        success: response.data.success,
        message: response.data.message,
        data: response.data.data || 0,
      };
    } catch (error) {
      console.error("Error fetching client count:", error);
      return {
        success: false,
        message:
          error.response?.data?.message || "Failed to fetch client count",
        data: 0,
      };
    }
  },

  /**
   * Get dashboard statistics (all counts in one call)
   * @returns {Promise<Object>} - Promise resolving to combined statistics
   */
  getDashboardStats: async () => {
    // Initialize with default values
    let purchaseStats = { success: false, data: {} };
    let supplierCount = { success: false, data: 0 };
    let employeeCount = { success: false, data: 0 };
    let clientCount = { success: false, data: 0 };

    try {
      // Get purchase statistics - handle potential errors for each call
      try {
        purchaseStats = await adminAnalyticsService.getPurchaseStatistics();
      } catch (error) {
        console.warn("Error fetching purchase statistics:", error);
      }

      // Get supplier count
      try {
        supplierCount = await adminAnalyticsService.getSupplierCount();
      } catch (error) {
        console.warn("Error fetching supplier count:", error);
      }

      // Get employee count
      try {
        employeeCount = await adminAnalyticsService.getEmployeeCount();
      } catch (error) {
        console.warn("Error fetching employee count:", error);
      }

      // Get client count
      try {
        clientCount = await adminAnalyticsService.getClientCount();
      } catch (error) {
        console.warn("Error fetching client count:", error);
      }

      // Determine overall success based on individual calls
      const overallSuccess =
        purchaseStats.success ||
        supplierCount.success ||
        employeeCount.success ||
        clientCount.success;

      return {
        success: overallSuccess,
        message: overallSuccess
          ? "Dashboard statistics retrieved successfully"
          : "Partial or no dashboard statistics retrieved",
        data: {
          purchases: purchaseStats.data || {},
          supplierCount: supplierCount.data || 0,
          employeeCount: employeeCount.data || 0,
          clientCount: clientCount.data || 0,
        },
      };
    } catch (error) {
      console.error("Error fetching dashboard statistics:", error);
      return {
        success: false,
        message: "Failed to fetch dashboard statistics",
        data: {
          purchases: {},
          supplierCount: 0,
          employeeCount: 0,
          clientCount: 0,
        },
      };
    }
  },
};

export default adminAnalyticsService;
