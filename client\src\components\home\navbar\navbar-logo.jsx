// NavbarLogo.jsx
import { isDesktop } from "@/utils";
import { HashLink } from "react-router-hash-link";

export const NavbarLogo = () => {
  return (
    <div className="font-medium font-clash-display grow flex items-center lg:justify-center lg:pl-0 pl-5 w-1/2 lg:w-auto">
      <HashLink to="#main" smooth>
        <div className="flex items-center justify-center gap-x-2">
          <img src="/icon.png" className="size-10 lg:size-14" alt="Logo" />
        </div>
      </HashLink>
    </div>
  );
};

export default NavbarLogo;
