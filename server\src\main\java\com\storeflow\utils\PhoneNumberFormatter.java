package com.storeflow.utils;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.google.i18n.phonenumbers.geocoding.PhoneNumberOfflineGeocoder;
import com.storeflow.exception.PhoneNumberValidationException;
import org.springframework.stereotype.Component;

import java.util.Locale;

@Component
public class PhoneNumberFormatter {

    private final PhoneNumberUtil phoneUtil;
    private final PhoneNumberOfflineGeocoder geocoder;

    public PhoneNumberFormatter() {
        this.phoneUtil = PhoneNumberUtil.getInstance();
        this.geocoder = PhoneNumberOfflineGeocoder.getInstance();
    }

    /**
     * Formats phone number to international format with spaces
     * Handles: E.164, international with spaces, any format starting with +
     */
    public String formatToInternational(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return phoneNumber;
        }

        try {
            // Clean the input (remove spaces, check if it needs + prefix)
            String cleanedNumber = cleanPhoneNumber(phoneNumber);

            // Parse without default country
            Phonenumber.PhoneNumber parsedNumber = phoneUtil.parse(cleanedNumber, "ZZ");

            return phoneUtil.format(parsedNumber, PhoneNumberUtil.PhoneNumberFormat.INTERNATIONAL);
        } catch (NumberParseException e) {
            throw new PhoneNumberValidationException("Invalid phone number format", e, phoneNumber);
        }
    }

    /**
     * Formats phone number to E.164 format (no spaces)
     */
    public String formatToE164(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return phoneNumber;
        }

        try {
            String cleanedNumber = cleanPhoneNumber(phoneNumber);
            Phonenumber.PhoneNumber parsedNumber = phoneUtil.parse(cleanedNumber, "ZZ");
            return phoneUtil.format(parsedNumber, PhoneNumberUtil.PhoneNumberFormat.E164);
        } catch (NumberParseException e) {
            throw new PhoneNumberValidationException("Invalid phone number format", e, phoneNumber);
        }
    }

    /**
     * Gets the country region code (e.g., "MA") from phone number
     */
    public String getCountryRegionCode(String phoneNumber) {
        try {
            String cleanedNumber = cleanPhoneNumber(phoneNumber);
            Phonenumber.PhoneNumber parsedNumber = phoneUtil.parse(cleanedNumber, "ZZ");
            String country = phoneUtil.getRegionCodeForNumber(parsedNumber);

            // Handle the case where country code exists but national number is incomplete
            if (country == null && hasCountryCode(parsedNumber)) {
                return getRegionCodeFromCountryCode(parsedNumber.getCountryCode());
            }

            return country != null ? country : "Unknown";
        } catch (NumberParseException e) {
            throw new PhoneNumberValidationException("Invalid phone number format", e, phoneNumber);
        }
    }

    /**
     * Gets only the numeric country code (e.g., "212")
     */
    public String getCountryCode(String phoneNumber) {
        try {
            String cleanedNumber = cleanPhoneNumber(phoneNumber);
            Phonenumber.PhoneNumber parsedNumber = phoneUtil.parse(cleanedNumber, "ZZ");
            return String.valueOf(parsedNumber.getCountryCode());
        } catch (NumberParseException e) {
            throw new PhoneNumberValidationException("Invalid phone number format", e, phoneNumber);
        }
    }

    /**
     * Gets the full country name from phone number
     */
    public String getCountryName(String phoneNumber) {
        try {
            String cleanedNumber = cleanPhoneNumber(phoneNumber);
            Phonenumber.PhoneNumber parsedNumber = phoneUtil.parse(cleanedNumber, "ZZ");

            // Use geocoder to get country name
            String countryName = geocoder.getDescriptionForNumber(parsedNumber, Locale.ENGLISH);

            // If geocoder doesn't return a country name, try getting it from region code
            if (countryName == null || countryName.isEmpty()) {
                String regionCode = phoneUtil.getRegionCodeForNumber(parsedNumber);
                countryName = getCountryNameFromRegion(regionCode);
            }

            return countryName != null ? countryName : "Unknown Country";
        } catch (NumberParseException e) {
            return "Unknown";
        }
    }

    /**
     * Gets the country name from region code using Java's built-in Locale
     */
    private String getCountryNameFromRegion(String regionCode) {
        if (regionCode == null) return null;

        Locale locale = new Locale("", regionCode);
        return locale.getDisplayCountry(Locale.ENGLISH);
    }

    /**
     * Gets the region code (e.g., "MA") from country code number
     */
    public String getRegionCodeFromCountryCode(int countryCode) {
        for (String regionCode : phoneUtil.getSupportedRegions()) {
            if (phoneUtil.getCountryCodeForRegion(regionCode) == countryCode) {
                return regionCode;
            }
        }
        return "Unknown";
    }

    /**
     * Cleans phone number by removing spaces and ensuring + prefix
     */
    private String cleanPhoneNumber(String phoneNumber) {
        String cleaned = phoneNumber.replaceAll("\\s+", ""); // Remove all spaces
        if (!cleaned.startsWith("+")) {
            // Only add + if the number looks like it has a country code
            if (cleaned.matches("\\d{10,15}")) {
                return "+" + cleaned;
            }
        }
        return cleaned;
    }

    /**
     * Check if parsed number has a country code
     */
    private boolean hasCountryCode(Phonenumber.PhoneNumber number) {
        return number.hasCountryCode() && number.getCountryCode() > 0;
    }
}
