import { authApi } from "@/context/auth-context";

/**
 * Service for employee-related API calls
 */
export const employeeService = {
  /**
   * Get all employees with pagination and search
   * @param {Object} params - Query parameters
   * @param {string} params.query - Search query
   * @param {number} params.page - Page number (0-based)
   * @param {number} params.size - Page size
   * @param {string} params.sortBy - Field to sort by
   * @param {string} params.sortDir - Sort direction ('asc' or 'desc')
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  getAllEmployees: async (params = {}) => {
    try {
      const { query = "", page = 0, size = 6, sortBy = "addedDate", sortDir = "desc" } = params;
      
      const response = await authApi.get("/api/v1/employees", {
        params: {
          query,
          page,
          size,
          sortBy,
          sortDir,
        },
      });
      
      return response.data;
    } catch (error) {
      console.error("Error fetching employees:", error);
      throw error;
    }
  },

  /**
   * Add a new employee
   * @param {Object} employeeData - Employee data
   * @param {string} employeeData.firstName - First name
   * @param {string} employeeData.lastName - Last name
   * @param {string} employeeData.email - Email address
   * @param {string} employeeData.phoneNumber - Phone number
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  addEmployee: async (employeeData) => {
    try {
      const response = await authApi.post("/api/v1/employees", employeeData);
      return response.data;
    } catch (error) {
      console.error("Error adding employee:", error);
      throw error;
    }
  },

  /**
   * Delete an employee
   * @param {string} employeeId - Employee ID
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  deleteEmployee: async (employeeId) => {
    try {
      const response = await authApi.delete(`/api/v1/employees/${employeeId}`);
      return response.data;
    } catch (error) {
      console.error("Error deleting employee:", error);
      throw error;
    }
  },
};
