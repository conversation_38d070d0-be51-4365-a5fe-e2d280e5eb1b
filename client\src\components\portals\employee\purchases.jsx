import { useState } from "react";
import { Helmet } from "react-helmet-async";
import { PurchaseHistory } from "@/components/shared";

/**
 * Employee Purchases page component
 * Displays the purchase history for the employee
 */
const EmployeePurchases = () => {
  return (
    <>
      <Helmet>
        <title>Purchases | Employee Portal</title>
      </Helmet>

      <div className="container mx-auto ">
        <PurchaseHistory showOnlyUserPurchases={true} />
      </div>
    </>
  );
};

export default EmployeePurchases;
