import { useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { AlertCircleIcon } from "lucide-react";
import toast from "react-hot-toast";

export const OAuth2Error = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    const error = searchParams.get("error");
    if (error) {
      toast.error(decodeURIComponent(error));
    } else {
      toast.error("Authentication failed. Please try again.");
    }
  }, [searchParams]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-blue-gray-50/40 px-8">
      <div className="w-full max-w-md bg-white rounded-2xl shadow-lg p-8 text-center">
        <div className="flex items-center justify-center gap-x-1.5 mb-6">
          <img src="/icon.png" className="size-7" alt="Logo" />
          <p className="text-blue-gray-700 font-satoshi text-xl">
            <span className="text-crimson">S</span>tore
            <span className="text-crimson">F</span>low
          </p>
        </div>

        <div className="mb-6">
          <AlertCircleIcon className="size-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-blue-gray-800 mb-2">
            Authentication Failed
          </h1>
          <p className="text-blue-gray-600">
            {searchParams.get("error") 
              ? decodeURIComponent(searchParams.get("error"))
              : "Something went wrong during authentication. Please try again."
            }
          </p>
        </div>

        <div className="space-y-3">
          <button
            onClick={() => navigate("/auth/login")}
            className="w-full bg-blue-gray-900 text-white py-3 px-4 rounded-xl font-medium transition-all duration-300 hover:bg-blue-gray-800"
          >
            Back to Login
          </button>
          
          <button
            onClick={() => navigate("/")}
            className="w-full bg-transparent text-blue-gray-700 py-3 px-4 rounded-xl font-medium border border-blue-gray-300 transition-all duration-300 hover:bg-blue-gray-50"
          >
            Go to Home
          </button>
        </div>
      </div>
    </div>
  );
};

export default OAuth2Error;
