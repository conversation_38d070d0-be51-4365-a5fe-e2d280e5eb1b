import { homeProductsData } from "@/data";
import { isDesktop, ripple } from "@/utils";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  LaptopMinimalIcon,
} from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";

export const Products = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollContainerRef = useRef(null);
  const [isMobile, setIsMobile] = useState(false);

  const navigate = useNavigate();

  // Determine if we're on mobile/tablet or desktop
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    handleResize(); // Initial check
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const scrollNext = () => {
    if (currentIndex < homeProductsData.length - 1) {
      setCurrentIndex((prev) => prev + 1);
      if (scrollContainerRef.current) {
        scrollContainerRef.current.scrollTo({
          left:
            (currentIndex + 1) *
            (scrollContainerRef.current.scrollWidth / homeProductsData.length),
          behavior: "smooth",
        });
      }
    }
  };

  const scrollPrev = () => {
    if (currentIndex > 0) {
      setCurrentIndex((prev) => prev - 1);
      if (scrollContainerRef.current) {
        scrollContainerRef.current.scrollTo({
          left:
            (currentIndex - 1) *
            (scrollContainerRef.current.scrollWidth / homeProductsData.length),
          behavior: "smooth",
        });
      }
    }
  };

  return (
    <section
      id="products"
      className="font-light relative flex flex-col gap-y-12 px-4 py-12 lg:p-12 min-h-dvh bg-blue-gray-50/40"
    >
      <div className="uppercase flex flex-col lg:flex-row items-center justify-between p-6 lg:p-12 border border-blue-gray-50 rounded-2xl shadow-gray-500/20 shadow-md">
        <p className="capitalize font-satoshi text-header text-center text-blue-gray-800 font-semibold mb-4 lg:mb-0">
          Explore Our Products
        </p>
        <div className="inline-flex size-12 lg:size-16 items-center justify-center rounded-full bg-blue-gray-900 text-center shadow-lg">
          <LaptopMinimalIcon className="size-6 lg:size-8 text-crimson" />
        </div>
      </div>

      {isMobile ? (
        <div className="overflow-hidden relative border rounded-2xl border-blue-gray-50 shadow-gray-500/20 shadow-md">
          {/* Pagination controls */}
          <button
            onClick={scrollPrev}
            disabled={currentIndex === 0}
            className={`absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1 z-10 ${
              currentIndex === 0
                ? "text-slate-200"
                : "text-slate-900 hover:text-crimson"
            } transition-colors`}
            aria-label="Previous slide"
          >
            <ChevronLeftIcon className="size-8" />
          </button>

          <button
            onClick={scrollNext}
            disabled={currentIndex >= homeProductsData.length - 1}
            className={`absolute right-0 top-1/2 -translate-y-1/2 translate-x-1 z-10 ${
              currentIndex >= homeProductsData.length - 1
                ? "text-slate-200"
                : "text-slate-900 hover:text-crimson"
            } transition-colors`}
            aria-label="Next slide"
          >
            <ChevronRightIcon className="size-8" />
          </button>

          {/* Scrollable container */}
          <div
            ref={scrollContainerRef}
            className="overflow-x-auto scrollbar-hide snap-x snap-mandatory flex gap-6 lg:gap-8 px-4 py-2"
          >
            {homeProductsData.map(
              ({
                id,
                label,
                description,
                technicalInfo,
                stockQuantity,
                price,
                imageUrl,
              }) => (
                <div
                  key={id}
                  className="p-6 flex flex-col gap-y-3 items-center snap-center flex-shrink-0 w-full text-blue-gray-700"
                >
                  <img
                    src={imageUrl}
                    alt="Product Image"
                    className="w-1/2 object-contain"
                  />
                  <p className="font-semibold text-xl lg:text-2xl text-center text-blue-gray-800">
                    {label}
                  </p>
                  <p className="text-lg lg:text-xl text-center font-medium">
                    {description}
                  </p>

                  <div className="flex items-center justify-between font-normal w-full">
                    <p className="text-base lg:text-xl w-1/2">
                      {technicalInfo}
                    </p>

                    <p className="text-base lg:text-xl text-right whitespace-nowrap w-1/2">
                      {stockQuantity} available
                    </p>
                  </div>

                  <p className="text-lg lg:text-xl text-center font-semibold">
                    {price}
                    <sup>MAD</sup>
                  </p>
                </div>
              )
            )}
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-2 gap-12">
          {homeProductsData.map(
            ({
              id,
              label,
              description,
              technicalInfo,
              stockQuantity,
              price,
              imageUrl,
            }) => (
              <div
                key={id}
                className="p-12 flex flex-col gap-y-3 items-center shadow-gray-500/20 shadow-lg rounded-2xl border border-blue-gray-50 text-blue-gray-700"
              >
                <div className="w-full flex items-center gap-x-3 font-medium">
                  <img
                    src={imageUrl}
                    alt="Product Image"
                    className="w-1/2 object-contain"
                  />
                  <div className="flex flex-col gap-y-5">
                    <p className="text-3xl text-blue-gray-800">{label}</p>

                    <p className="text-base lg:text-lg">{technicalInfo}</p>

                    <p className="text-2xl font-semibold">
                      {price}
                      <sup className="text-sm">MAD</sup>
                    </p>

                    <p className="text-base lg:text-lg">
                      {stockQuantity} available
                    </p>
                  </div>
                </div>
                <p className="text-xl text-center font-normal">{description}</p>
              </div>
            )
          )}
        </div>
      )}

      <button
        onMouseDown={(event) =>
          ripple.create(event, isDesktop() ? "light" : "dark")
        }
        onClick={() => navigate("/auth/login")}
        className="group text-base bg-blue-gray-900 lg:bg-transparent text-white lg:text-blue-gray-800 flex items-center justify-center w-full rounded-full py-3 font-normal transition-all duration-300 hover:shadow-darker active:shadow-darker lg:rounded-xl lg:text-header"
        type="button"
      >
        Explore more & Start your commands !
      </button>
    </section>
  );
};

export default Products;
