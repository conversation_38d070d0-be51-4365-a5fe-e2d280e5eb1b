package com.storeflow.controllers;

import com.storeflow.DTOs.OrderDTO;
import com.storeflow.DTOs.requests.CreateOrderRequest;
import com.storeflow.DTOs.requests.UpdateOrderStatusRequest;
import com.storeflow.responses.ApiResponse;
import com.storeflow.services.OrderService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

/**
 * Controller for managing client orders.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/orders")
@RequiredArgsConstructor
public class OrderController {

    private final OrderService orderService;

    /**
     * Create a new order.
     *
     * @param request The order creation request
     * @return The created order
     */
    @PostMapping
//    @PreAuthorize("hasRole('CLIENT')")
    public ResponseEntity<ApiResponse<OrderDTO>> createOrder(@Valid @RequestBody CreateOrderRequest request) {
        log.info("Received request to create order with {} items", request.items().size());

        OrderDTO order = orderService.createOrder(request);

        log.info("Order created successfully with ID: {}", order.id());

        return ResponseEntity.status(HttpStatus.CREATED)
            .body(new ApiResponse<>(true, "Order created successfully", order));
    }

    /**
     * Update the status of an order.
     *
     * @param orderId The ID of the order
     * @param request The update request
     * @return The updated order
     */
    @PutMapping("/{orderId}/status")
    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE')")
    public ResponseEntity<ApiResponse<OrderDTO>> updateOrderStatus(
            @PathVariable UUID orderId,
            @Valid @RequestBody UpdateOrderStatusRequest request) {
        OrderDTO order = orderService.updateOrderStatus(orderId, request);
        return ResponseEntity.ok(new ApiResponse<>(true, "Order status updated successfully", order));
    }

    /**
     * Get an order by ID.
     *
     * @param orderId The ID of the order
     * @return The order
     */
    @GetMapping("/{orderId}")
    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE', 'CLIENT')")
    public ResponseEntity<ApiResponse<OrderDTO>> getOrderById(@PathVariable UUID orderId) {
        OrderDTO order = orderService.getOrderById(orderId);

        log.info("Retrieved order with ID: {}", order.id());
        log.info("Order has {} items", order.items().size());

        return ResponseEntity.ok(new ApiResponse<>(true, "Order retrieved successfully", order));
    }

    /**
     * Get all orders with pagination (for admin/employee).
     *
     * @param pageable The pagination information
     * @return A page of orders
     */
    @GetMapping
    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE')")
    public ResponseEntity<ApiResponse<Page<OrderDTO>>> getAllOrders(Pageable pageable) {
        Page<OrderDTO> orders = orderService.getAllOrders(pageable);
        return ResponseEntity.ok(new ApiResponse<>(true, "Orders retrieved successfully", orders));
    }

    /**
     * Get orders for the current client with pagination.
     *
     * @param pageable The pagination information
     * @return A page of orders for the current client
     */
    @GetMapping("/my-orders")
//    @PreAuthorize("hasRole('CLIENT')")
    public ResponseEntity<ApiResponse<Page<OrderDTO>>> getMyOrders(Pageable pageable) {
        Page<OrderDTO> orders = orderService.getOrdersForCurrentClient(pageable);
        return ResponseEntity.ok(new ApiResponse<>(true, "Orders retrieved successfully", orders));
    }
}
