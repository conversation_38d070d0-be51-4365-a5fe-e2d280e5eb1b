package com.storeflow.controllers;

import com.storeflow.DTOs.CompanyProductDTO;
import com.storeflow.responses.ApiResponse;
import com.storeflow.services.CompanyProductService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * Controller for managing company products (internal inventory).
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/company-products")
public class CompanyProductController {

    private final CompanyProductService companyProductService;

    /**
     * Get all company products with pagination and search.
     *
     * @param query Optional search query
     * @param page Page number (0-based)
     * @param size Page size
     * @param sortBy Field to sort by
     * @param sortDir Sort direction ('asc' or 'desc')
     * @return A page of company products
     */
    @GetMapping
//    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE')")
    public ResponseEntity<ApiResponse<Page<CompanyProductDTO>>> getAllCompanyProducts(
        @RequestParam(required = false, defaultValue = "") String query,
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(defaultValue = "label") String sortBy,
        @RequestParam(defaultValue = "asc") String sortDir
    ) {
        log.info("Received request to get company products with query: '{}', page: {}, size: {}", 
            query, page, size);

        // Create sort object
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);

        // Get products with pagination and search
        Page<CompanyProductDTO> products;

        if (query != null && !query.trim().isEmpty()) {
            products = companyProductService.searchProducts(query, pageable)
                .map(companyProductService::toDTO);
        } else {
            products = companyProductService.getAllProducts(pageable)
                .map(companyProductService::toDTO);
        }

        return ResponseEntity.ok(new ApiResponse<>(
            true,
            "Company products retrieved successfully",
            products
        ));
    }

    /**
     * Get company inventory statistics.
     *
     * @return Inventory statistics
     */
    @GetMapping("/stats")
//    @PreAuthorize("hasAnyRole('ADMIN', 'EMPLOYEE')")
    public ResponseEntity<ApiResponse<InventoryStats>> getInventoryStats() {
        log.info("Received request to get company inventory statistics");

        long totalProducts = companyProductService.getTotalProductCount();
        long inStockProducts = companyProductService.getInStockProductCount();
        BigDecimal totalValue = companyProductService.getTotalInventoryValue();

        InventoryStats stats = new InventoryStats(totalProducts, inStockProducts, totalValue);

        return ResponseEntity.ok(new ApiResponse<>(
            true,
            "Inventory statistics retrieved successfully",
            stats
        ));
    }

    /**
     * Record class for inventory statistics.
     */
    public record InventoryStats(
        long totalProducts,
        long inStockProducts,
        BigDecimal totalValue
    ) {}
}
