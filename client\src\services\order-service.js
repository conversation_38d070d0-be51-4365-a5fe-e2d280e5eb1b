import { authApi } from "@/context/auth-context";

/**
 * Service for order-related API calls
 */
export const orderService = {
  /**
   * Create a new order
   * @param {Object} orderData - Order creation data
   * @param {Array} orderData.items - Array of order items
   * @param {string} orderData.notes - Order notes
   * @param {string} orderData.phoneNumber - Phone number
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  createOrder: async (orderData) => {
    try {
      const response = await authApi.post("/api/v1/orders", orderData);
      return response.data;
    } catch (error) {
      console.error("Error creating order:", error);
      throw error;
    }
  },

  /**
   * Get an order by ID
   * @param {string} orderId - The order ID
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  getOrderById: async (orderId) => {
    try {
      const response = await authApi.get(`/api/v1/orders/${orderId}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching order:", error);
      throw error;
    }
  },

  /**
   * Get orders for the current client with pagination
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number (0-based)
   * @param {number} params.size - Page size
   * @param {string} params.sortBy - Field to sort by
   * @param {string} params.sortDir - Sort direction ('asc' or 'desc')
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  getMyOrders: async (params = {}) => {
    try {
      const {
        page = 0,
        size = 10,
        sortBy = "orderDate",
        sortDir = "desc",
      } = params;

      const response = await authApi.get("/api/v1/orders/my-orders", {
        params: {
          page,
          size,
          sortBy,
          sortDir,
        },
      });

      return response.data;
    } catch (error) {
      console.error("Error fetching my orders:", error);
      throw error;
    }
  },

  /**
   * Get all orders with pagination (for admin/employee)
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number (0-based)
   * @param {number} params.size - Page size
   * @param {string} params.sortBy - Field to sort by
   * @param {string} params.sortDir - Sort direction ('asc' or 'desc')
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  getAllOrders: async (params = {}) => {
    try {
      const {
        page = 0,
        size = 10,
        sortBy = "orderDate",
        sortDir = "desc",
      } = params;

      const response = await authApi.get("/api/v1/orders", {
        params: {
          page,
          size,
          sortBy,
          sortDir,
        },
      });

      return response.data;
    } catch (error) {
      console.error("Error fetching all orders:", error);
      throw error;
    }
  },

  /**
   * Update order status (for admin/employee)
   * @param {string} orderId - The order ID
   * @param {Object} statusData - Status update data
   * @param {string} statusData.status - New status
   * @param {string} statusData.notes - Optional notes
   * @returns {Promise<Object>} - Promise resolving to API response
   */
  updateOrderStatus: async (orderId, statusData) => {
    try {
      const response = await authApi.put(
        `/api/v1/orders/${orderId}/status`,
        statusData
      );
      return response.data;
    } catch (error) {
      console.error("Error updating order status:", error);
      throw error;
    }
  },
};
