package com.storeflow.DTOs.requests;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * Request DTO for creating an order item.
 */
public record CreateOrderItemRequest(
    @NotBlank(message = "Product label is required")
    String productLabel,
    
    @NotNull(message = "Quantity is required")
    @Min(value = 1, message = "Quantity must be at least 1")
    Integer quantity
) {
}
