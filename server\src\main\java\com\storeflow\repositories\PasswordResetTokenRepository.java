package com.storeflow.repositories;

import com.storeflow.models.PasswordResetToken;
import com.storeflow.models.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface PasswordResetTokenRepository extends JpaRepository<PasswordResetToken, UUID> {

    Optional<PasswordResetToken> findByVerificationCode(String verificationCode);

    Optional<PasswordResetToken> findByUserAndVerificationCodeAndUsedFalseAndExpiryDateAfter(
        User user, String verificationCode, LocalDateTime currentTime);

    Optional<PasswordResetToken> findByUserAndUsedFalseAndExpiryDateAfter(User user, LocalDateTime currentTime);

    @Modifying
    @Query("DELETE FROM PasswordResetToken p WHERE p.expiryDate < :currentTime")
    void deleteExpiredTokens(@Param("currentTime") LocalDateTime currentTime);

    @Modifying
    @Query("UPDATE PasswordResetToken p SET p.used = true WHERE p.user = :user AND p.used = false")
    void markAllUserTokensAsUsed(@Param("user") User user);

    boolean existsByUserAndUsedFalseAndExpiryDateAfter(User user, LocalDateTime currentTime);
}
