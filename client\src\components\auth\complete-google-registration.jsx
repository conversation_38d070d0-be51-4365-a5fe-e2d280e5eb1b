import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useFormik } from "formik";
import * as Yup from "yup";
import { PhoneIcon, AlertCircleIcon } from "lucide-react";
import { isDesktop, ripple } from "@/utils";
import { authApi, useAuth } from "@/context/auth-context";
import toast from "react-hot-toast";
import PhoneInput from "react-phone-number-input";
import "react-phone-number-input/style.css";
import { allowedCountries } from "@/utils/phone-utils";
import "yup-phone";
import { jwtDecode } from "jwt-decode";
import { useDispatch } from "react-redux";
import { setUserData } from "@/redux/user-slice";

// Validation schema
const PhoneSchema = Yup.object().shape({
  phoneNumber: Yup.string()
    .test("phone", "Invalid phone number", function (value) {
      if (!value) return false;

      // Basic international phone number validation
      const internationalPattern = /^\+[1-9]\d{1,14}$/;
      // Special case for Moroccan numbers (including local format)
      const moroccanPattern = /^(\+212|0)[5-7][0-9]{8}$/;

      return internationalPattern.test(value) || moroccanPattern.test(value);
    })
    .required("Phone number is required"),
});

export const CompleteGoogleRegistration = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const dispatch = useDispatch();

  // Get auth context to update tokens
  const { updateAuthTokens } = useAuth();

  // Get Google OAuth data from URL parameters
  const firstName = searchParams.get("firstName");
  const lastName = searchParams.get("lastName");
  const email = searchParams.get("email");
  const googleId = searchParams.get("googleId");
  const profilePicture = searchParams.get("profilePicture");
  const message = searchParams.get("message");

  useEffect(() => {
    // Redirect if no Google OAuth data provided
    if (!email || !googleId || !firstName || !lastName) {
      toast.error("Invalid Google registration session. Please try again.");
      navigate("/auth/login");
      return;
    }

    // Store Google OAuth data temporarily for the completion request
    localStorage.setItem(
      "tempGoogleData",
      JSON.stringify({
        firstName: decodeURIComponent(firstName),
        lastName: decodeURIComponent(lastName),
        email: decodeURIComponent(email),
        googleId: decodeURIComponent(googleId),
        profilePicture: profilePicture
          ? decodeURIComponent(profilePicture)
          : null,
      })
    );

    if (message) {
      toast.success(decodeURIComponent(message));
    }
  }, [firstName, lastName, email, googleId, profilePicture, message, navigate]);

  const formik = useFormik({
    initialValues: {
      phoneNumber: "",
    },
    validationSchema: PhoneSchema,
    onSubmit: async (values) => {
      setLoading(true);
      setError(null);

      try {
        const tempGoogleData = localStorage.getItem("tempGoogleData");

        if (!tempGoogleData) {
          throw new Error(
            "Google registration data not found. Please try again."
          );
        }

        const googleData = JSON.parse(tempGoogleData);

        const response = await authApi.post(
          "/api/v1/auth/oauth2/complete-registration",
          {
            phoneNumber: values.phoneNumber,
            firstName: googleData.firstName,
            lastName: googleData.lastName,
            email: googleData.email,
            googleId: googleData.googleId,
            profilePictureUrl: googleData.profilePicture,
          }
        );

        if (response.data.success) {
          // Clear temporary Google data
          localStorage.removeItem("tempGoogleData");

          // Update auth context with new tokens
          updateAuthTokens(
            response.data.data.accessToken,
            response.data.data.refreshToken
          );

          toast.success("Registration completed successfully!");

          // Fetch user profile data with the new token
          try {
            const profileResponse = await authApi.get("/api/v1/auth/verify", {
              headers: {
                Authorization: `Bearer ${response.data.data.accessToken}`,
              },
            });

            if (profileResponse.data.success && profileResponse.data.data) {
              // Update Redux store with complete user data including profile picture
              dispatch(setUserData(profileResponse.data.data));
            }
          } catch (profileError) {
            console.error("Error fetching user profile:", profileError);
            // Continue with redirect even if profile fetch fails
          }

          // Get user role from the new access token
          let redirectTo = "/client/dashboard"; // Default for Google OAuth users

          try {
            const decoded = jwtDecode(response.data.data.accessToken);
            const userRole = decoded.role
              ? decoded.role.toLowerCase()
              : "client";

            switch (userRole) {
              case "admin":
                redirectTo = "/admin/dashboard";
                break;
              case "client":
                redirectTo = "/client/dashboard";
                break;
              case "supplier":
                redirectTo = "/supplier/dashboard";
                break;
              case "employee":
                redirectTo = "/employee/dashboard";
                break;
              default:
                redirectTo = "/client/dashboard";
                break;
            }
          } catch (decodeError) {
            console.error("Error decoding token:", decodeError);
            // Keep default redirect to client dashboard
          }

          // Set flag for showing success toast after redirect
          localStorage.setItem("showRegistrationSuccessToast", "true");

          // Small delay to ensure auth context is updated before navigation
          setTimeout(() => {
            // Use navigate instead of window.location.href to preserve React state
            navigate(redirectTo, { replace: true });
          }, 100);
        } else {
          throw new Error(
            response.data.message || "Registration completion failed"
          );
        }
      } catch (err) {
        console.error("Registration completion error:", err);
        const errorMessage =
          err.response?.data?.message ||
          err.message ||
          "Failed to complete registration";
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    },
  });

  return (
    <section className="min-h-screen flex flex-col justify-center items-center px-8 py-20 lg:px-[340px] bg-blue-gray-50/40">
      <div className="flex items-center justify-center gap-x-1.5 absolute top-0 right-0 px-6 py-3 select-none">
        <img src="/icon.png" className="size-7" alt="Logo" />
        <p className="text-blue-gray-700 font-satoshi text-xl">
          <span className="text-crimson">S</span>tore
          <span className="text-crimson">F</span>low
        </p>
      </div>

      <div className="w-full max-w-md bg-white rounded-2xl shadow-lg p-8">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-blue-gray-800 mb-2">
            Complete Your Registration
          </h1>
          <p className="text-blue-gray-600">
            Please provide your phone number to complete your Google
            registration
          </p>
        </div>

        <form onSubmit={formik.handleSubmit} className="space-y-6">
          {/* Phone Number Input */}
          <div className="flex flex-col gap-y-1 w-full">
            <label
              htmlFor="phoneNumber"
              className="block text-sm font-medium text-blue-gray-700"
            >
              Phone Number
            </label>
            <div
              className={`phone-input-container w-full border ${
                formik.touched.phoneNumber && formik.errors.phoneNumber
                  ? "border-red-500"
                  : "border-blue-gray-500"
              } rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px] overflow-hidden`}
            >
              <PhoneInput
                international
                defaultCountry="MA"
                className="custom-phone-input"
                countries={allowedCountries}
                value={formik.values.phoneNumber}
                onChange={(value) => formik.setFieldValue("phoneNumber", value)}
                onBlur={() => formik.setFieldTouched("phoneNumber", true)}
                placeholder="Enter your phone number"
              />
            </div>
            {formik.touched.phoneNumber && formik.errors.phoneNumber && (
              <p className="text-red-500 text-sm pl-2">
                {formik.errors.phoneNumber}
              </p>
            )}
          </div>

          {/* Error Message */}
          {error && (
            <div className="flex items-center gap-2 text-red-500 text-sm bg-red-50 p-3 rounded-lg">
              <AlertCircleIcon className="size-4" />
              <span>{error}</span>
            </div>
          )}

          {/* Submit Button */}
          <button
            onMouseDown={(event) =>
              ripple.create(event, isDesktop() ? "light" : "dark")
            }
            type="submit"
            disabled={loading}
            className={`group cursor-pointer text-base text-white bg-blue-gray-900 border border-blue-gray-800 shadow-dark flex items-center justify-center w-full rounded-full py-3 font-medium transition-all duration-300 hover:bg-transparent hover:text-blue-gray-800 hover:border-blue-gray-50 hover:shadow-darker active:shadow-darker lg:rounded-xl lg:text-lg ${
              loading ? "opacity-70 cursor-not-allowed" : ""
            }`}
          >
            <PhoneIcon className="text-white transition-all duration-300 group-hover:text-blue-gray-800 size-5 lg:size-6 mr-2" />
            <span>
              {loading ? "Completing Registration..." : "Complete Registration"}
            </span>
          </button>
        </form>

        <div className="mt-6 text-center">
          <button
            onClick={() => {
              localStorage.removeItem("tempGoogleData");
              navigate("/auth/login");
            }}
            className="text-blue-gray-600 hover:text-blue-gray-800 transition-colors duration-200 text-sm"
          >
            Cancel and return to login
          </button>
        </div>
      </div>
    </section>
  );
};

export default CompleteGoogleRegistration;
