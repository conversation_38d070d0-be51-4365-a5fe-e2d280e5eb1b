import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { updateUserProfile } from "@/redux/user-slice";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";
import { Mail, MapPin, UserRound, CheckCircle, Save } from "lucide-react";
import { profileService } from "@/services";
import PhoneInput from "react-phone-number-input";
import "react-phone-number-input/style.css";
import { allowedCountries } from "@/utils/phone-utils";
import { ripple } from "@/utils";

export const ProfileForm = ({ user }) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  // Create validation schema based on user role
  const formSchema = z.object({
    firstName: z
      .string()
      .min(2, { message: "First name must be at least 2 characters" })
      .trim(),
    lastName: z
      .string()
      .min(2, { message: "Last name must be at least 2 characters" })
      .trim(),
    email: z.string().email({ message: "Invalid email address" }),
    phoneNumber: z.string().min(1, { message: "Phone number is required" }),
    ...(user?.role === "supplier"
      ? {
          localization: z
            .string()
            .min(1, { message: "Localization is required" })
            .trim(),
        }
      : {}),
  });

  // Initialize form with react-hook-form
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      localization: "",
    },
    mode: "onBlur",
  });

  useEffect(() => {
    if (user && user.firstName) {
      // Reset form with new user data when it becomes available
      const formData = {
        firstName: user.firstName || "",
        lastName: user.lastName || "",
        email: user.email || "",
        phoneNumber: user.phoneNumber || "",
        localization: user.localization || "",
      };
      form.reset(formData);
    }
  }, [user, form]);

  // Form submission handler
  const onSubmit = async (values) => {
    setLoading(true);
    setSuccess(false);

    try {
      // Get only the changed fields by comparing with original values
      const changedFields = {};
      const dirtyFields = form.formState.dirtyFields;

      // Add only dirty (changed) fields to the request payload
      Object.keys(dirtyFields).forEach((fieldName) => {
        if (dirtyFields[fieldName]) {
          changedFields[fieldName] = values[fieldName];
        }
      });

      // Only proceed if there are actually changed fields
      if (Object.keys(changedFields).length === 0) {
        toast.info("No changes detected");
        setLoading(false);
        return;
      }

      // Send only the changed fields to the API
      await profileService.updateProfile(changedFields);

      // Update Redux store with the changed fields
      dispatch(updateUserProfile(changedFields));

      toast.success("Profile updated successfully!");
      setSuccess(true);

      // Update the form's default values to the current values
      form.reset(values);

      // Wait for a moment before hiding success indicator
      setTimeout(() => setSuccess(false), 3000);
    } catch (error) {
      console.error("Error updating profile:", error);

      let errorMessage = "Failed to update profile";
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      }

      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-4 text-blue-gray-700 max-w-4xl mx-auto"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* First Name */}
          <FormField
            control={form.control}
            name="firstName"
            render={({ field }) => (
              <div className="space-y-2">
                <FormLabel className="text-blue-gray-700 font-medium block">
                  First Name
                </FormLabel>
                <div className="space-y-1">
                  <FormItem className="m-0 p-0">
                    <FormControl>
                      <div className="flex items-center w-full border rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px] shadow-sm bg-white">
                        <UserRound
                          className={`ml-3 size-5 lg:size-6 ${
                            field.value
                              ? "text-blue-gray-700"
                              : "text-blue-gray-400"
                          }`}
                        />
                        <input
                          className="flex-1 h-full text-base focus:outline-0 transition-all placeholder:text-blue-gray-400 rounded-xl px-2 lg:text-lg bg-transparent"
                          placeholder="Your first name"
                          disabled={loading}
                          {...field}
                        />
                      </div>
                    </FormControl>
                  </FormItem>
                  <div className="h-6 px-1">
                    <FormMessage className="text-red-500 text-sm" />
                  </div>
                </div>
              </div>
            )}
          />

          {/* Last Name */}
          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <div className="space-y-2">
                <FormLabel className="text-blue-gray-700 font-medium block">
                  Last Name
                </FormLabel>
                <div className="space-y-1">
                  <FormItem className="m-0 p-0">
                    <FormControl>
                      <div className="flex items-center w-full border rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px] shadow-sm bg-white">
                        <UserRound
                          className={`ml-3 size-5 lg:size-6 ${
                            field.value
                              ? "text-blue-gray-700"
                              : "text-blue-gray-400"
                          }`}
                        />
                        <input
                          className="flex-1 h-full text-base focus:outline-0 transition-all placeholder:text-blue-gray-400 rounded-xl px-2 lg:text-lg bg-transparent"
                          placeholder="Your last name"
                          disabled={loading}
                          {...field}
                        />
                      </div>
                    </FormControl>
                  </FormItem>
                  <div className="h-6 px-1">
                    <FormMessage className="text-red-500 text-sm" />
                  </div>
                </div>
              </div>
            )}
          />

          {/* Email */}
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <div className="space-y-2">
                <FormLabel className="text-blue-gray-700 font-medium block">
                  Email Address
                </FormLabel>
                <div className="space-y-1">
                  <FormItem className="m-0 p-0">
                    <FormControl>
                      <div className="flex items-center w-full border rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px] shadow-sm bg-white">
                        <Mail
                          className={`ml-3 size-5 lg:size-6 ${
                            field.value
                              ? "text-blue-gray-700"
                              : "text-blue-gray-400"
                          }`}
                        />
                        <input
                          type="email"
                          className="flex-1 h-full text-base focus:outline-0 transition-all placeholder:text-blue-gray-400 rounded-xl px-2 lg:text-lg bg-transparent"
                          placeholder="Your email address"
                          disabled={loading}
                          {...field}
                        />
                      </div>
                    </FormControl>
                  </FormItem>
                  <div className="h-6 px-1">
                    <FormMessage className="text-red-500 text-sm" />
                  </div>
                </div>
              </div>
            )}
          />

          {/* Phone Number */}
          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <div className="space-y-2">
                <FormLabel className="text-blue-gray-700 font-medium block">
                  Phone Number
                </FormLabel>
                <div className="space-y-1">
                  <FormItem className="m-0 p-0">
                    <FormControl>
                      <div className="phone-input-container pl-1 flex items-center w-full border rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px] shadow-sm bg-white">
                        <PhoneInput
                          international
                          defaultCountry="MA"
                          countries={allowedCountries}
                          disabled={loading}
                          value={field.value}
                          onChange={field.onChange}
                          onBlur={field.onBlur}
                          className="custom-phone-input"
                        />
                      </div>
                    </FormControl>
                  </FormItem>
                  <div className="h-6 px-1">
                    <FormMessage className="text-red-500 text-sm" />
                  </div>
                </div>
              </div>
            )}
          />

          {/* Localization (for suppliers only) */}
          {user?.role === "supplier" && (
            <FormField
              control={form.control}
              name="localization"
              render={({ field }) => (
                <div className="space-y-2 md:col-span-2">
                  <FormLabel className="text-blue-gray-700 font-medium block">
                    Business Location
                  </FormLabel>
                  <div className="space-y-1">
                    <FormItem className="m-0 p-0">
                      <FormControl>
                        <div className="flex items-start w-full border rounded-xl focus-within:border-blue-gray-800 transition-all min-h-[100px] shadow-sm bg-white">
                          <MapPin className="ml-3 mt-3 h-5 w-5 text-blue-gray-400 flex-shrink-0" />
                          <textarea
                            placeholder="Your business address or location"
                            className="flex-1 text-base focus:outline-0 transition-all placeholder:text-blue-gray-400 rounded-xl py-3 px-2 lg:text-lg resize-none bg-transparent"
                            disabled={loading}
                            rows={3}
                            {...field}
                          />
                        </div>
                      </FormControl>
                    </FormItem>
                    <div className="h-6 px-1">
                      <FormMessage className="text-red-500 text-sm" />
                    </div>
                  </div>
                </div>
              )}
            />
          )}
          <button
            onMouseDown={(e) => ripple.create(e, "light")}
            type="submit"
            disabled={
              loading || !form.formState.isDirty || !form.formState.isValid
            }
            className={`inline-flex items-center justify-center gap-2 text-white text-base px-8 py-3 rounded-xl lg:text-lg transition-all duration-[400ms] lg:col-start-2 ${
              loading || !form.formState.isDirty || !form.formState.isValid
                ? "bg-blue-gray-600 cursor-not-allowed"
                : "bg-blue-gray-900 hover:bg-slate-900 shadow-dark hover:shadow-darker"
            }`}
          >
            {loading ? (
              <>
                <div className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></div>
                <span>Saving...</span>
              </>
            ) : success ? (
              <>
                <CheckCircle className="h-5 w-5" />
                <span>Saved</span>
              </>
            ) : (
              <>
                <Save className="h-5 w-5" />
                <span>Save Changes</span>
              </>
            )}
          </button>
        </div>
      </form>
    </Form>
  );
};

export default ProfileForm;
