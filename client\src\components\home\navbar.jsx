import { useMobileDetector, useScrollLock } from "@/hooks";
import { useCallback, useState, useEffect, useRef } from "react";
import NavbarLogo from "./navbar/navbar-logo";
import DesktopNavigation from "./navbar/desktop-navigation";
import MobileNavigation from "./navbar/mobile-navigation";
import DesktopAuthLinks from "./navbar/desktop-auth-links";
import HamburgerMenu from "./navbar/hamburger-menu";

export const Navbar = ({ isPastMain }) => {
  // Add state to track if mobile menu is open
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  // Add state to track if navbar is visible (when scrolled)
  const [isVisible, setIsVisible] = useState(true);
  const navRef = useRef(null);

  // When mobile changes, close the menu if switching to desktop
  const handleMobileChange = useCallback(
    (isMobile) => {
      if (!isMobile && isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    },
    [isMobileMenuOpen]
  );

  // Use our custom hooks
  const isOnMobile = useMobileDetector(handleMobileChange);
  useScrollLock(isMobileMenuOpen);

  // Toggle function for mobile menu
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen((prev) => !prev);
  };

  // Close menu function
  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  // Handle the navbar hover detection
  useEffect(() => {
    const handleMouseMove = (e) => {
      // Show navbar if mouse is near the top of the screen (within 100px)
      if (isPastMain && e.clientY <= 100) {
        setIsVisible(true);
      }
    };

    // Listen for mouse movement to detect hover near top
    window.addEventListener("mousemove", handleMouseMove);

    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
    };
  }, [isPastMain]);

  // Set visibility based on scroll
  useEffect(() => {
    // Always visible when at the top or when mobile menu is open
    if (!isPastMain || isMobileMenuOpen) {
      setIsVisible(true);
      return;
    }

    // When scrolling begins, hide the navbar
    setIsVisible(false);
  }, [isPastMain, isMobileMenuOpen]);

  return (
    <div
      ref={navRef}
      className="w-full fixed top-0 z-50"
      // Extra div for hover detection area
      onMouseEnter={() => isPastMain && setIsVisible(true)}
      onMouseLeave={() =>
        isPastMain && !isMobileMenuOpen && setIsVisible(false)
      }
    >
      <nav
        className={`w-full text-white py-8 transition-all duration-300 ${
          isPastMain
            ? "bg-black/90 shadow-lg"
            : isMobileMenuOpen
            ? "bg-black/90 shadow-lg"
            : "bg-transparent"
        } ${
          isPastMain && !isVisible && !isMobileMenuOpen
            ? "-translate-y-full"
            : "translate-y-0"
        }`}
      >
        <div className="flex items-center justify-evenly flex-wrap size-full lg:gap-0">
          {/* Logo */}
          <NavbarLogo />

          {/* Desktop Navigation Links */}
          <DesktopNavigation />

          {/* Mobile Navigation Links (with closeMenu prop) */}
          {isOnMobile && (
            <MobileNavigation
              isOpen={isMobileMenuOpen}
              closeMenu={closeMobileMenu}
            />
          )}

          {/* Auth Links - Desktop */}
          <DesktopAuthLinks />

          {/* Hamburger Menu - Mobile only */}
          {isOnMobile && (
            <HamburgerMenu
              isOpen={isMobileMenuOpen}
              toggleMenu={toggleMobileMenu}
            />
          )}
        </div>
      </nav>
    </div>
  );
};

export default Navbar;
