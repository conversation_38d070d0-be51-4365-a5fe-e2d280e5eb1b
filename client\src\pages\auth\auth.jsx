import { routes } from "@/routes/auth";
import { Navigate, Route, Routes } from "react-router-dom";

export const Auth = () => {
  return (
    <Routes>
      {routes.map(({ path, element }, index) => (
        <Route key={index} path={path} element={element} />
      ))}

      {/* Catch-all route for undefined paths */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

export default Auth;
