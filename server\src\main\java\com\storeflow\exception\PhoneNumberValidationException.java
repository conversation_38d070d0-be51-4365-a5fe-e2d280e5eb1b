package com.storeflow.exception;

import lombok.Getter;

import java.util.Objects;

/**
 * Exception thrown when phone number validation fails.
 * This can occur during parsing, formatting, or when a phone number
 * doesn't meet specific validation requirements.
 */
@Getter
public class PhoneNumberValidationException extends RuntimeException {

    /**
     * -- GETTER --
     *  Returns the phone number that caused this exception.
     */
    private final String phoneNumber;

    /**
     * Constructs a new exception with the specified detail message.
     *
     * @param message the detail message
     */
    public PhoneNumberValidationException(String message) {
        super(message);
        this.phoneNumber = null;
    }

    /**
     * Constructs a new exception with the specified detail message and phone number.
     *
     * @param message the detail message
     * @param phoneNumber the invalid phone number that caused this exception
     */
    public PhoneNumberValidationException(String message, String phoneNumber) {
        super(message);
        this.phoneNumber = phoneNumber;
    }

    /**
     * Constructs a new exception with the specified detail message, cause, and phone number.
     *
     * @param message the detail message
     * @param cause the cause of the exception
     * @param phoneNumber the invalid phone number that caused this exception
     */
    public PhoneNumberValidationException(String message, Throwable cause, String phoneNumber) {
        super(message, cause);
        this.phoneNumber = phoneNumber;
    }

    /**
     * Constructs a new exception with the specified detail message and cause.
     *
     * @param message the detail message
     * @param cause the cause of the exception
     */
    public PhoneNumberValidationException(String message, Throwable cause) {
        super(message, cause);
        this.phoneNumber = null;
    }

    @Override
    public String toString() {
        return super.toString() + (phoneNumber != null ? " [phoneNumber=" + phoneNumber + "]" : "");
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;

        PhoneNumberValidationException that = (PhoneNumberValidationException) o;
        return Objects.equals(phoneNumber, that.phoneNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), phoneNumber);
    }
}
