import {HashLink} from "react-router-hash-link"

export const DesktopNavigation = () => {
  return (
    <div className="hidden lg:flex grow lg:items-center self-stretch text-xl">
      <div className="flex items-center w-full">
        <HashLink to="/#about" className="grow" smooth>
          <div className="hover:text-crimson duration-400 text-center">
            About
          </div>
        </HashLink>
        <HashLink to="/#services" className="grow" smooth>
          <div className="hover:text-crimson duration-400 text-center">
            Services
          </div>
        </HashLink>
        <HashLink to="/#products" className="grow" smooth>
          <div className="hover:text-crimson duration-400 text-center">
            Products
          </div>
        </HashLink>
      </div>
    </div>
  );
};

export default DesktopNavigation;
