package com.storeflow.auditing;

import com.storeflow.models.User;
import org.springframework.data.domain.AuditorAware;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.user.OAuth2User;

import java.util.Optional;

public class ApplicationAuditAware implements AuditorAware<String> {
    @Override
    @NonNull
    public Optional<String> getCurrentAuditor() {
        Authentication authentication = SecurityContextHolder
            .getContext()
            .getAuthentication();

        if (authentication == null ||
            !authentication.isAuthenticated() ||
            authentication instanceof AnonymousAuthenticationToken) {
            return Optional.of("Registration");  // Default value when no user is authenticated
        }

        Object principal = authentication.getPrincipal();

        // Handle different types of principals
        if (principal instanceof User) {
            // Standard JWT authentication
            User userPrincipal = (User) principal;
            return Optional.of(userPrincipal.getEmail());
        } else if (principal instanceof OAuth2User) {
            // OAuth2 authentication (Google, etc.)
            OAuth2User oAuth2User = (OAuth2User) principal;
            String email = oAuth2User.getAttribute("email");
            return Optional.of(email != null ? email : "OAuth2User");
        } else {
            // Fallback for other authentication types
            return Optional.of("System");
        }
    }
}