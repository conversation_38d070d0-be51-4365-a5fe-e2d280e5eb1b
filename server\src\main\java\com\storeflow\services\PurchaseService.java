package com.storeflow.services;

import com.storeflow.DTOs.PurchaseDTO;
import com.storeflow.DTOs.PurchaseItemDTO;
import com.storeflow.DTOs.requests.CreatePurchaseItemRequest;
import com.storeflow.DTOs.requests.CreatePurchaseRequest;
import com.storeflow.DTOs.requests.UpdatePurchaseStatusRequest;
import com.storeflow.DTOs.responses.MonthlyAnalyticsResponse;
import com.storeflow.enums.PurchaseStatus;
import com.storeflow.exception.ResourceNotFoundException;
import com.storeflow.models.*;
import com.storeflow.repositories.ProductRepository;
import com.storeflow.repositories.PurchaseItemRepository;
import com.storeflow.repositories.PurchaseRepository;
import com.storeflow.repositories.SupplierRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.Principal;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Service for managing purchases from suppliers.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PurchaseService {
    private final PurchaseRepository purchaseRepository;
    private final PurchaseItemRepository purchaseItemRepository;
    private final SupplierRepository supplierRepository;
    private final ProductRepository productRepository;
    private final SupplierProductService supplierProductService;
    private final CompanyProductService companyProductService;

    /**
     * Create a new purchase from a supplier.
     *
     * @param request The purchase request
     * @return The created purchase DTO
     */
    @Transactional
    public PurchaseDTO createPurchase(CreatePurchaseRequest request) {
        log.info("Creating purchase for supplier ID: {}", request.supplierId());

        // Find the supplier
        Supplier supplier = supplierRepository.findById(request.supplierId())
                .orElseThrow(() -> {
                    log.error("Supplier not found with ID: {}", request.supplierId());
                    return new ResourceNotFoundException("Supplier not found with ID: " + request.supplierId());
                });

        log.info("Found supplier: {} {}", supplier.getUser().getFirstName(), supplier.getUser().getLastName());

        // Create the purchase
        Purchase purchase = Purchase.builder()
                .supplier(supplier)
                .supplierName(supplier.getUser().getFirstName() + " " + supplier.getUser().getLastName())
                .supplierEmail(supplier.getUser().getEmail())
                .purchaseDate(LocalDateTime.now())
                .status(PurchaseStatus.COMPLETED) // Admin/employee purchases are completed immediately
                .notes(request.notes())
                .totalAmount(BigDecimal.ZERO) // Will be calculated later
                .build();

        log.info("Created purchase object with status: {}", purchase.getStatus());

        // Add items to the purchase
        for (CreatePurchaseItemRequest itemRequest : request.items()) {
            log.info("Processing item with product ID: {}, quantity: {}", itemRequest.productId(), itemRequest.quantity());

            // Find the product
            Product product = productRepository.findById(itemRequest.productId())
                    .orElseThrow(() -> {
                        log.error("Product not found with ID: {}", itemRequest.productId());
                        return new ResourceNotFoundException("Product not found with ID: " + itemRequest.productId());
                    });

            log.info("Found product with ID: {}, brand: {}", product.getId(), product.getBrand());

            // Find the supplier product to get the price
            SupplierProduct supplierProduct = supplierProductService.getSupplierProductBySupplierAndProduct(
                    supplier.getId(), product.getId());

            if (supplierProduct == null) {
                log.error("Product not found for supplier: {}", product.getBrand());
                throw new ResourceNotFoundException("Product not found for supplier: " + product.getBrand());
            }

            log.info("Found supplier product: {}, current stock: {}", supplierProduct.getLabel(), supplierProduct.getStockQuantity());

            // Check if there's enough stock
            if (supplierProduct.getStockQuantity() < itemRequest.quantity()) {
                log.error("Not enough stock for product: {}. Available: {}, Requested: {}",
                    product.getBrand(), supplierProduct.getStockQuantity(), itemRequest.quantity());
                throw new IllegalArgumentException("Not enough stock for product: " + product.getBrand() +
                        ". Available: " + supplierProduct.getStockQuantity() + ", Requested: " + itemRequest.quantity());
            }

            // Create the purchase item
            PurchaseItem item = PurchaseItem.builder()
                    .supplier(supplier)
                    .product(product)
                    .productLabel(supplierProduct.getLabel())
                    .quantity(itemRequest.quantity())
                    .unitPrice(supplierProduct.getSellingPrice())
                    .build();

            log.info("Created purchase item for product: {}, quantity: {}, unit price: {}",
                supplierProduct.getLabel(), item.getQuantity(), item.getUnitPrice());

            // Add the item to the purchase
            purchase.addItem(item);

            // Decrease the supplier's stock quantity immediately
            int newQuantity = supplierProduct.getStockQuantity() - itemRequest.quantity();
            supplierProduct.setStockQuantity(Math.max(0, newQuantity)); // Ensure it doesn't go below 0
            supplierProductService.saveSupplierProduct(supplierProduct);

            log.info("Updated supplier product stock quantity from {} to {}",
                supplierProduct.getStockQuantity() + itemRequest.quantity(), supplierProduct.getStockQuantity());

            // Add stock to company inventory
            companyProductService.addStockFromPurchase(
                product,
                supplierProduct.getLabel(),
                supplierProduct.getDescription(),
                itemRequest.quantity(),
                supplierProduct.getSellingPrice(), // Use supplier's selling price as our cost price
                supplierProduct.getSellingPrice().multiply(new BigDecimal("1.2")), // Add 20% markup for company selling price
                supplierProduct.getDeliveryTime()
            );

            log.info("Added {} units of {} to company inventory", itemRequest.quantity(), supplierProduct.getLabel());
        }

        // Calculate the total amount
        purchase.calculateTotalAmount();
        log.info("Calculated total purchase amount: {}", purchase.getTotalAmount());

        // Save the purchase
        Purchase savedPurchase = purchaseRepository.save(purchase);
        log.info("Saved purchase with ID: {}", savedPurchase.getId());

        // Return the DTO
        PurchaseDTO purchaseDTO = mapToDTO(savedPurchase);
        log.info("Returning purchase DTO with ID: {}", purchaseDTO.id());

        return purchaseDTO;
    }

    /**
     * Update the status of a purchase.
     *
     * @param purchaseId The ID of the purchase
     * @param request    The update request
     * @return The updated purchase DTO
     */
    @Transactional
    public PurchaseDTO updatePurchaseStatus(UUID purchaseId, UpdatePurchaseStatusRequest request) {
        // Find the purchase
        Purchase purchase = purchaseRepository.findById(purchaseId)
                .orElseThrow(() -> new ResourceNotFoundException("Purchase not found with ID: " + purchaseId));

        // Update the status
        purchase.setStatus(request.status());

        // Update notes if provided
        if (request.notes() != null) {
            purchase.setNotes(request.notes());
        }

        // Note: Stock quantities are already updated when the purchase is created
        // This is only needed for status changes from CANCELLED back to COMPLETED
        if (request.status() == PurchaseStatus.COMPLETED && purchase.getStatus() == PurchaseStatus.CANCELLED) {
            for (PurchaseItem item : purchase.getItems()) {
                // Find the supplier product
                SupplierProduct supplierProduct = supplierProductService.getSupplierProductBySupplierAndProduct(
                        item.getSupplier().getId(), item.getProduct().getId());

                if (supplierProduct != null) {
                    // Decrease the supplier stock quantity
                    int newQuantity = supplierProduct.getStockQuantity() - item.getQuantity();
                    supplierProduct.setStockQuantity(Math.max(0, newQuantity)); // Ensure it doesn't go below 0
                    supplierProductService.saveSupplierProduct(supplierProduct);

                    // Add stock to company inventory
                    companyProductService.addStockFromPurchase(
                        item.getProduct(),
                        item.getProductLabel(),
                        supplierProduct.getDescription(),
                        item.getQuantity(),
                        item.getUnitPrice(), // Use purchase price as our cost price
                        item.getUnitPrice().multiply(new BigDecimal("1.2")), // Add 20% markup for company selling price
                        supplierProduct.getDeliveryTime()
                    );

                    log.info("Re-added {} units of {} to company inventory after status change to COMPLETED",
                        item.getQuantity(), item.getProductLabel());
                }
            }
        }

        // If changing from COMPLETED to CANCELLED, restore the supplier stock quantities
        // Note: We don't remove from company inventory as the products might have already been sold to clients
        if (request.status() == PurchaseStatus.CANCELLED && purchase.getStatus() == PurchaseStatus.COMPLETED) {
            for (PurchaseItem item : purchase.getItems()) {
                // Find the supplier product
                SupplierProduct supplierProduct = supplierProductService.getSupplierProductBySupplierAndProduct(
                        item.getSupplier().getId(), item.getProduct().getId());

                if (supplierProduct != null) {
                    // Increase the supplier stock quantity
                    int newQuantity = supplierProduct.getStockQuantity() + item.getQuantity();
                    supplierProduct.setStockQuantity(newQuantity);
                    supplierProductService.saveSupplierProduct(supplierProduct);

                    log.info("Restored {} units of {} to supplier inventory after status change to CANCELLED",
                        item.getQuantity(), item.getProductLabel());
                }
            }
        }

        // Save the purchase
        Purchase savedPurchase = purchaseRepository.save(purchase);

        // Return the DTO
        return mapToDTO(savedPurchase);
    }

    /**
     * Get a purchase by ID.
     *
     * @param purchaseId The ID of the purchase
     * @return The purchase DTO
     */
    public PurchaseDTO getPurchaseById(UUID purchaseId) {
        Purchase purchase = purchaseRepository.findById(purchaseId)
                .orElseThrow(() -> new ResourceNotFoundException("Purchase not found with ID: " + purchaseId));
        return mapToDTO(purchase);
    }

    /**
     * Get all purchases with pagination.
     *
     * @param pageable The pagination information
     * @return A page of purchase DTOs
     */
    public Page<PurchaseDTO> getAllPurchases(Pageable pageable) {
        return purchaseRepository.findAll(pageable)
                .map(this::mapToDTO);
    }

    /**
     * Get all purchases for a supplier with pagination.
     *
     * @param supplierId The ID of the supplier
     * @param pageable   The pagination information
     * @return A page of purchase DTOs
     */
    public Page<PurchaseDTO> getPurchasesBySupplier(UUID supplierId, Pageable pageable) {
        return purchaseRepository.findBySupplierId(supplierId, pageable)
                .map(this::mapToDTO);
    }

    /**
     * Get all purchases created by the authenticated user with pagination.
     *
     * @param principal The authenticated user
     * @param pageable  The pagination information
     * @return A page of purchase DTOs created by the authenticated user
     */
    public Page<PurchaseDTO> getPurchasesForCurrentUser(Principal principal, Pageable pageable) {
        String username = principal.getName();
        log.info("Fetching purchases for user: {}", username);

        return purchaseRepository.findByCreatedBy(username, pageable)
                .map(this::mapToDTO);
    }

    /**
     * Get monthly analytics data for a supplier.
     *
     * @param supplierId The ID of the supplier
     * @return The monthly analytics data
     */
    public MonthlyAnalyticsResponse getMonthlyAnalytics(UUID supplierId) {
        // Get the start and end dates for the last 12 months
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusMonths(11).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);

        // Get the monthly sales statistics
        List<Object[]> monthlyStats = purchaseItemRepository.getMonthlySupplierSalesStatistics(
                supplierId, startDate, endDate);

        // Create a list of monthly data points
        List<MonthlyAnalyticsResponse.MonthlyDataPoint> monthlyData = new ArrayList<>();

        // Initialize all months with zero values
        for (int i = 0; i < 12; i++) {
            YearMonth yearMonth = YearMonth.from(startDate.plusMonths(i));
            String monthName = yearMonth.getMonth().toString().substring(0, 3);
            monthlyData.add(new MonthlyAnalyticsResponse.MonthlyDataPoint(
                    monthName, 0, BigDecimal.ZERO));
        }

        // Fill in the actual data
        for (Object[] stat : monthlyStats) {
            int month = (int) stat[0]; // 1-based month
            int year = (int) stat[1];
            long totalQuantity = (long) stat[2];
            BigDecimal totalAmount = (BigDecimal) stat[3];

            // Find the index in our list
            YearMonth statYearMonth = YearMonth.of(year, month);
            YearMonth startYearMonth = YearMonth.from(startDate);

            int monthDiff = (year - startYearMonth.getYear()) * 12 + (month - startYearMonth.getMonthValue());

            if (monthDiff >= 0 && monthDiff < 12) {
                String monthName = Month.of(month).toString().substring(0, 3);
                monthlyData.set(monthDiff, new MonthlyAnalyticsResponse.MonthlyDataPoint(
                        monthName, totalQuantity > Integer.MAX_VALUE ? Integer.MAX_VALUE : (int) totalQuantity, totalAmount));
            }
        }

        return new MonthlyAnalyticsResponse(monthlyData);
    }

    /**
     * Map a Purchase entity to a PurchaseDTO.
     *
     * @param purchase The purchase entity
     * @return The purchase DTO
     */
    private PurchaseDTO mapToDTO(Purchase purchase) {
        List<PurchaseItemDTO> itemDTOs = purchase.getItems().stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());

        // Handle case where supplier might be null (deleted supplier)
        UUID supplierId = null;
        String supplierName = purchase.getSupplierName(); // Use preserved name
        String supplierEmail = purchase.getSupplierEmail(); // Use preserved email

        if (purchase.getSupplier() != null) {
            supplierId = purchase.getSupplier().getId();
            // If preserved data is null, get from supplier entity
            if (supplierName == null) {
                supplierName = purchase.getSupplier().getUser().getFirstName() + " " + purchase.getSupplier().getUser().getLastName();
            }
            if (supplierEmail == null) {
                supplierEmail = purchase.getSupplier().getUser().getEmail();
            }
        }

        return new PurchaseDTO(
                purchase.getId(),
                supplierId,
                supplierName,
                supplierEmail,
                purchase.getPurchaseDate(),
                purchase.getStatus(),
                purchase.getTotalAmount(),
                purchase.getNotes(),
                itemDTOs,
                purchase.getCreatedDate(),
                purchase.getLastModifiedDate(),
                purchase.getCreatedBy(),
                purchase.getLastModifiedBy()
        );
    }

    /**
     * Map a PurchaseItem entity to a PurchaseItemDTO.
     *
     * @param item The purchase item entity
     * @return The purchase item DTO
     */
    private PurchaseItemDTO mapToDTO(PurchaseItem item) {
        // Get the product's image URL and brand
        String imageUrl = item.getProduct() != null ? item.getProduct().getImageUrl() : null;
        String brand = item.getProduct() != null ? item.getProduct().getBrand() : null;

        // Handle case where supplier might be null (deleted supplier)
        UUID supplierId = null;
        String supplierName = null;

        if (item.getSupplier() != null) {
            supplierId = item.getSupplier().getId();
            supplierName = item.getSupplier().getUser().getFirstName() + " " + item.getSupplier().getUser().getLastName();
        } else {
            // Use preserved supplier info from the purchase
            supplierName = item.getPurchase().getSupplierName();
        }

        return new PurchaseItemDTO(
                item.getId(),
                item.getPurchase().getId(),
                supplierId,
                supplierName,
                item.getProduct().getId(),
                item.getProductLabel(),
                item.getQuantity(),
                item.getUnitPrice(),
                item.getTotalPrice(),
                imageUrl,
                brand,
                item.getCreatedDate(),
                item.getLastModifiedDate()
        );
    }
}
