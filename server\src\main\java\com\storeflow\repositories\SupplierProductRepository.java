package com.storeflow.repositories;

import com.storeflow.embeddables.SupplierProductId;
import com.storeflow.models.SupplierProduct;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface SupplierProductRepository extends JpaRepository<SupplierProduct, SupplierProductId>, JpaSpecificationExecutor<SupplierProduct> {

    /**
     * Finds a SupplierProduct by supplier ID and product ID.
     *
     * @param supplierId The ID of the supplier
     * @param productId The ID of the product
     * @return An Optional containing the SupplierProduct if found, empty otherwise
     */
    @Query("SELECT sp FROM SupplierProduct sp " +
        "WHERE sp.supplier.id = :supplierId " +
        "AND sp.product.id = :productId")
    Optional<SupplierProduct> findBySupplierIdAndProductId(
        @Param("supplierId") UUID supplierId,
        @Param("productId") UUID productId);

    /**
     * Checks if a supplier has a product with the specified label (case-insensitive).
     *
     * @param supplierId The ID of the supplier
     * @param label The product label to check
     * @return true if the supplier has a product with the given label, false otherwise
     */
    @Query("SELECT COUNT(sp) > 0 FROM SupplierProduct sp " +
        "WHERE sp.supplier.id = :supplierId " +
        "AND LOWER(sp.label) = LOWER(:label)")
    boolean supplierHasProductWithLabel(@Param("supplierId") UUID supplierId, @Param("label") String label);

    /**
     * Finds all products for a specific supplier.
     *
     * @param supplierId The ID of the supplier
     * @return A list of SupplierProduct entities
     */
    @Query("SELECT sp FROM SupplierProduct sp WHERE sp.supplier.id = :supplierId ORDER BY sp.label ASC")
    List<SupplierProduct> findAllBySupplierId(@Param("supplierId") UUID supplierId);

    /**
     * Finds all products for a specific supplier with pagination.
     *
     * @param supplierId The ID of the supplier
     * @param pageable The pagination information
     * @return A page of SupplierProduct entities
     */
    @Query("SELECT sp FROM SupplierProduct sp WHERE sp.supplier.id = :supplierId")
    Page<SupplierProduct> findAllBySupplierId(@Param("supplierId") UUID supplierId, Pageable pageable);

    /**
     * Searches for products of a specific supplier by various criteria.
     *
     * @param supplierId The ID of the supplier
     * @param searchQuery The search query to match against label, description, or brand
     * @param pageable The pagination information
     * @return A page of SupplierProduct entities matching the search criteria
     */
    @Query("SELECT sp FROM SupplierProduct sp WHERE sp.supplier.id = :supplierId AND (" +
           "LOWER(sp.label) LIKE LOWER(CONCAT('%', :searchQuery, '%')) OR " +
           "LOWER(sp.description) LIKE LOWER(CONCAT('%', :searchQuery, '%')) OR " +
           "LOWER(sp.product.brand) LIKE LOWER(CONCAT('%', :searchQuery, '%')))")
    Page<SupplierProduct> searchBySupplierId(
        @Param("supplierId") UUID supplierId,
        @Param("searchQuery") String searchQuery,
        Pageable pageable);

    /**
     * Checks if any supplier has the specified product.
     *
     * @param productId The ID of the product to check
     * @return true if any supplier has the product, false otherwise
     */
    @Query("SELECT COUNT(sp) > 0 FROM SupplierProduct sp WHERE sp.product.id = :productId")
    boolean existsByProductId(@Param("productId") UUID productId);

    /**
     * Finds all SupplierProduct entries for a specific product.
     *
     * @param productId The ID of the product
     * @return A list of SupplierProduct entities for the given product
     */
    List<SupplierProduct> findByProductId(UUID productId);
}
