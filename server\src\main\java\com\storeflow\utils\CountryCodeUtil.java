package com.storeflow.utils;

import com.google.i18n.phonenumbers.PhoneNumberUtil;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class CountryCodeUtil {

    private final PhoneNumberUtil phoneUtil;

    public CountryCodeUtil() {
        this.phoneUtil = PhoneNumberUtil.getInstance();
    }

    /**
     * Get all supported countries with their codes
     */
    public Map<String, CountryInfo> getAllCountries() {
        Map<String, CountryInfo> countries = new HashMap<>();

        for (String regionCode : phoneUtil.getSupportedRegions()) {
            int countryCode = phoneUtil.getCountryCodeForRegion(regionCode);
            String countryName = new Locale("", regionCode).getDisplayCountry(Locale.ENGLISH);

            countries.put(regionCode, new CountryInfo(regionCode, countryCode, countryName));
        }

        return countries;
    }

    /**
     * Get all supported region codes
     */
    public Set<String> getAllRegionCodes() {
        return phoneUtil.getSupportedRegions();
    }

    /**
     * Get all region codes for a specific country code
     * (Some country codes are shared by multiple regions)
     */
    public List<String> getRegionsForCountryCode(int countryCode) {
        List<String> regions = new ArrayList<>();

        for (String regionCode : phoneUtil.getSupportedRegions()) {
            if (phoneUtil.getCountryCodeForRegion(regionCode) == countryCode) {
                regions.add(regionCode);
            }
        }

        return regions;
    }

    public static class CountryInfo {
        private final String regionCode;
        private final int countryCode;
        private final String countryName;

        public CountryInfo(String regionCode, int countryCode, String countryName) {
            this.regionCode = regionCode;
            this.countryCode = countryCode;
            this.countryName = countryName;
        }

        // Getters
        public String getRegionCode() {
            return regionCode;
        }

        public int getCountryCode() {
            return countryCode;
        }

        public String getCountryName() {
            return countryName;
        }

        @Override
        public String toString() {
            return String.format("%s (%s): +%d", countryName, regionCode, countryCode);
        }
    }
}
