package com.storeflow.services;

import com.storeflow.exception.InvalidTokenException;
import com.storeflow.exception.PasswordMismatchException;
import com.storeflow.exception.ResourceNotFoundException;
import com.storeflow.exception.TooManyRequestsException;
import com.storeflow.models.PasswordResetToken;
import com.storeflow.models.User;
import com.storeflow.repositories.PasswordResetTokenRepository;
import com.storeflow.repositories.UserRepository;
import com.storeflow.requests.ForgotPasswordRequest;
import com.storeflow.requests.ResetPasswordRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class PasswordResetService {

    private final UserRepository userRepository;
    private final PasswordResetTokenRepository passwordResetTokenRepository;
    private final EmailService emailService;
    private final PasswordEncoder passwordEncoder;
    private final SecureRandom secureRandom = new SecureRandom();

    private static final int CODE_EXPIRY_MINUTES = 10;
    private static final int CODE_LENGTH = 6;

    @Transactional
    public void processForgotPasswordRequest(ForgotPasswordRequest request) {
        String email = request.email().toLowerCase().trim();

        // Find user by email
        User user = userRepository.findByEmail(email)
            .orElseThrow(() -> new ResourceNotFoundException("No account found with this email address"));

        // For code-based system, allow new requests but limit frequency (e.g., max 1 per minute)
        Optional<PasswordResetToken> recentToken = passwordResetTokenRepository
            .findByUserAndUsedFalseAndExpiryDateAfter(user, LocalDateTime.now());

        if (recentToken.isPresent()) {
            LocalDateTime lastRequestTime = recentToken.get().getCreatedAt();
            LocalDateTime oneMinuteAgo = LocalDateTime.now().minusMinutes(1);

            if (lastRequestTime.isAfter(oneMinuteAgo)) {
                throw new TooManyRequestsException("Please wait a moment before requesting another verification code.");
            }
        }

        // Mark all existing tokens for this user as used
        passwordResetTokenRepository.markAllUserTokensAsUsed(user);

        // Generate secure 6-digit code
        String verificationCode = generateVerificationCode();

        // Create password reset token
        PasswordResetToken passwordResetToken = PasswordResetToken.builder()
            .verificationCode(verificationCode)
            .user(user)
            .expiryDate(LocalDateTime.now().plusMinutes(CODE_EXPIRY_MINUTES))
            .build();

        passwordResetTokenRepository.save(passwordResetToken);

        // Send email asynchronously
        sendPasswordResetCodeAsync(user.getEmail(), verificationCode, user.getFirstName());

        log.info("Password reset token generated for user: {}", email);
    }

    @Transactional
    public boolean verifyResetCode(String email, String verificationCode) {
        User user = userRepository.findByEmail(email.toLowerCase().trim())
            .orElseThrow(() -> new ResourceNotFoundException("No account found with this email address"));

        // Find valid verification code
        PasswordResetToken resetToken = passwordResetTokenRepository
            .findByUserAndVerificationCodeAndUsedFalseAndExpiryDateAfter(user, verificationCode, LocalDateTime.now())
            .orElse(null);

        return resetToken != null && resetToken.isValid();
    }

    @Transactional
    public void resetPassword(ResetPasswordRequest request) {
        // Validate password confirmation
        if (!request.newPassword().equals(request.confirmationPassword())) {
            throw new PasswordMismatchException("New password and confirmation do not match");
        }

        // Find user
        User user = userRepository.findByEmail(request.email().toLowerCase().trim())
            .orElseThrow(() -> new ResourceNotFoundException("No account found with this email address"));

        // Find and validate verification code
        PasswordResetToken resetToken = passwordResetTokenRepository
            .findByUserAndVerificationCodeAndUsedFalseAndExpiryDateAfter(user, request.verificationCode(), LocalDateTime.now())
            .orElseThrow(() -> new InvalidTokenException("Invalid or expired verification code"));

        if (!resetToken.isValid()) {
            throw new InvalidTokenException("Invalid or expired verification code");
        }

        // Update password
        user.setPassword(passwordEncoder.encode(request.newPassword()));
        userRepository.save(user);

        // Mark token as used
        resetToken.setUsed(true);
        passwordResetTokenRepository.save(resetToken);

        // Send confirmation email asynchronously
        sendPasswordResetConfirmationEmailAsync(user.getEmail(), user.getFirstName());

        log.info("Password successfully reset for user: {}", user.getEmail());
    }

    @Async
    public void sendPasswordResetCodeAsync(String email, String verificationCode, String userName) {
        try {
            emailService.sendPasswordResetCode(email, verificationCode, userName);
        } catch (Exception e) {
            log.error("Failed to send password reset code asynchronously", e);
        }
    }

    @Async
    public void sendPasswordResetConfirmationEmailAsync(String email, String userName) {
        try {
            emailService.sendPasswordResetConfirmationEmail(email, userName);
        } catch (Exception e) {
            log.error("Failed to send password reset confirmation email asynchronously", e);
        }
    }

    private String generateVerificationCode() {
        // Generate a 6-digit code
        int code = 100000 + secureRandom.nextInt(900000);
        return String.valueOf(code);
    }

    @Scheduled(fixedRate = 3600000) // Run every hour
    @Transactional
    public void cleanupExpiredTokens() {
        try {
            passwordResetTokenRepository.deleteExpiredTokens(LocalDateTime.now());
            log.debug("Cleaned up expired password reset tokens");
        } catch (Exception e) {
            log.error("Error cleaning up expired password reset tokens", e);
        }
    }
}
