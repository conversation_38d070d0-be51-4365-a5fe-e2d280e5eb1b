import { useRef, useState } from "react";

export const usePasswordVisibility = () => {
  const [isVisible, setIsVisible] = useState(false);
  const inputRef = useRef(null);

  const toggleVisibility = () => {
    if (inputRef.current) {
      const position = inputRef.current.selectionStart;
      setIsVisible((prev) => !prev);

      setTimeout(() => {
        inputRef.current?.setSelectionRange(position, position);
        inputRef.current?.focus();
      }, 0);
    }
  };

  return { isVisible, toggleVisibility, inputRef };
};

export default usePasswordVisibility;
