package com.storeflow.configurations;

import io.minio.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Slf4j
public class MinioConfig {

    @Value("${minio.endpoint}")
    private String endpoint;

    @Value("${minio.accessKey}")
    private String accessKey;

    @Value("${minio.secretKey}")
    private String secretKey;

    @Value("${minio.profile-bucket}")
    private String profileBucket;

    @Value("${minio.product-bucket}")
    private String productBucket;

    @Value("${minio.max-file-size}")
    private long maxFileSize;

    @Bean
    public MinioClient minioClient() {
        log.info("Initializing MinIO client with endpoint: {}", endpoint);
        try {
            MinioClient client = MinioClient.builder()
                    .endpoint(endpoint)
                    .credentials(accessKey, secretKey)
                    .build();

            // Initialize buckets
            initializeBuckets(client);

            return client;
        } catch (Exception e) {
            log.error("Failed to initialize MinIO client", e);
            throw new RuntimeException("MinIO client initialization failed", e);
        }
    }

    private void initializeBuckets(MinioClient client) {
        try {
            // Check and create profile pictures bucket
            createPublicBucketIfNotExists(client, profileBucket);

            // Check and create product pictures bucket
            createPublicBucketIfNotExists(client, productBucket);

        } catch (Exception e) {
            log.error("Error initializing MinIO buckets", e);
            throw new RuntimeException("MinIO bucket initialization failed", e);
        }
    }

    private void createPublicBucketIfNotExists(MinioClient client, String bucketName) {
        try {
            boolean bucketExists = client.bucketExists(BucketExistsArgs.builder()
                    .bucket(bucketName)
                    .build());

            if (!bucketExists) {
                log.info("Creating bucket: {}", bucketName);
                client.makeBucket(MakeBucketArgs.builder()
                        .bucket(bucketName)
                        .build());

                // Set bucket policy to allow public read access
                String readOnlyPolicy = "{"
                        + "\"Version\":\"2012-10-17\","
                        + "\"Statement\":["
                        + "  {"
                        + "    \"Effect\":\"Allow\","
                        + "    \"Principal\":{\"AWS\":[\"*\"]},"
                        + "    \"Action\":[\"s3:GetObject\"],"
                        + "    \"Resource\":[\"arn:aws:s3:::" + bucketName + "/*\"]"
                        + "  }"
                        + "]"
                        + "}";

                client.setBucketPolicy(SetBucketPolicyArgs.builder()
                        .bucket(bucketName)
                        .config(readOnlyPolicy)
                        .build());

                log.info("Successfully created bucket with public read access: {}", bucketName);
            } else {
                log.info("Bucket already exists: {}", bucketName);

                // Ensure bucket has the correct public read access
                String readOnlyPolicy = "{"
                        + "\"Version\":\"2012-10-17\","
                        + "\"Statement\":["
                        + "  {"
                        + "    \"Effect\":\"Allow\","
                        + "    \"Principal\":{\"AWS\":[\"*\"]},"
                        + "    \"Action\":[\"s3:GetObject\"],"
                        + "    \"Resource\":[\"arn:aws:s3:::" + bucketName + "/*\"]"
                        + "  }"
                        + "]"
                        + "}";

                client.setBucketPolicy(SetBucketPolicyArgs.builder()
                        .bucket(bucketName)
                        .config(readOnlyPolicy)
                        .build());

                log.info("Updated bucket policy for public read access: {}", bucketName);
            }
        } catch (Exception e) {
            log.error("Failed to create/configure bucket: {}", bucketName, e);
            throw new RuntimeException("Failed to create bucket: " + bucketName, e);
        }
    }
}
