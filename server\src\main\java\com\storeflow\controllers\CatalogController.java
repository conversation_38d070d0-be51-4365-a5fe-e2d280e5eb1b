package com.storeflow.controllers;

import com.storeflow.DTOs.CatalogProductDTO;
import com.storeflow.responses.ApiResponse;
import com.storeflow.services.CatalogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * Controller for managing the product catalog available to clients.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/catalog")
@RequiredArgsConstructor
public class CatalogController {
    
    private final CatalogService catalogService;

    /**
     * Get all available products in the catalog with pagination.
     *
     * @param pageable The pagination information
     * @return A page of catalog products
     */
    @GetMapping("/products")
    @PreAuthorize("hasRole('CLIENT')")
    public ResponseEntity<ApiResponse<Page<CatalogProductDTO>>> getAvailableProducts(Pageable pageable) {
        log.info("Fetching available products for catalog");
        
        Page<CatalogProductDTO> products = catalogService.getAvailableProducts(pageable);
        
        return ResponseEntity.ok(new ApiResponse<>(
            true, 
            "Products retrieved successfully", 
            products
        ));
    }

    /**
     * Get a specific product from the catalog by label.
     *
     * @param label The product label
     * @return The catalog product
     */
    @GetMapping("/products/{label}")
    @PreAuthorize("hasRole('CLIENT')")
    public ResponseEntity<ApiResponse<CatalogProductDTO>> getProductByLabel(@PathVariable String label) {
        log.info("Fetching catalog product by label: {}", label);
        
        CatalogProductDTO product = catalogService.getProductByLabel(label);
        
        if (product == null) {
            return ResponseEntity.notFound().build();
        }
        
        return ResponseEntity.ok(new ApiResponse<>(
            true, 
            "Product retrieved successfully", 
            product
        ));
    }
}
