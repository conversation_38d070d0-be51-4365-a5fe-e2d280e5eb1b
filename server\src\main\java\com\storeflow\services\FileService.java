package com.storeflow.services;

import com.storeflow.configurations.MinioConfig;
import com.storeflow.exception.*;
import io.minio.*;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Random;

@Service
@RequiredArgsConstructor
@Slf4j
public class FileService {

    private final MinioClient minioClient;
    /**
     * -- GETTER --
     *  Get MinIO config
     */
    @Getter
    private final MinioConfig minioConfig;
    private static final String DEFAULT_PROFILE_PICTURE = "default-profile.jpg";
    private static final List<String> ALLOWED_EXTENSIONS = Arrays.asList("jpg", "jpeg", "png", "webp");
    private final Random random = new Random();

    /**
     * Upload a profile picture and return a direct URL
     */
    public String uploadProfilePicture(MultipartFile file) {
        String randomDigits = generateRandomDigits(8);
        String prefix = "avatar-" + randomDigits;
        log.info("Uploading profile picture with prefix: {}", prefix);

        String objectName = uploadFileAndReturnObjectName(file, minioConfig.getProfileBucket(), prefix);
        return getDirectFileUrl(minioConfig.getProfileBucket(), objectName);
    }

    /**
     * Upload a product picture and return a direct URL
     */
    public String uploadProductPicture(MultipartFile file) {
        String randomDigits = generateRandomDigits(8);
        String prefix = "product-" + randomDigits;
        log.info("Uploading product picture with prefix: {}", prefix);

        String objectName = uploadFileAndReturnObjectName(file, minioConfig.getProductBucket(), prefix);
        return getDirectFileUrl(minioConfig.getProductBucket(), objectName);
    }

    /**
     * Generate random digits (non-zero)
     */
    private String generateRandomDigits(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            // Generate digits 1-9 (avoiding 0)
            int digit = random.nextInt(9) + 1;
            sb.append(digit);
        }
        return sb.toString();
    }

    /**
     * Get default profile picture URL directly from resource
     * This uploads the default profile picture directly with a unique name
     */
    public String getDefaultProfilePictureUrl() {
        log.info("Creating default profile picture for the user");
        try {
            // Load the default image from resources
            Resource resource = new ClassPathResource("static/images/" + DEFAULT_PROFILE_PICTURE);
            if (!resource.exists()) {
                log.error("Default profile picture not found in resources");
                throw new FileNotFoundException("Default profile picture not found");
            }

            // Generate a unique name for this user's default profile
            String randomDigits = generateRandomDigits(8);
            String userDefaultPicture = "avatar-" + randomDigits + "-default." +
                    FilenameUtils.getExtension(DEFAULT_PROFILE_PICTURE);

            // Upload the default image with the unique name
            InputStream inputStream = resource.getInputStream();
            byte[] content = StreamUtils.copyToByteArray(inputStream);

            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(minioConfig.getProfileBucket())
                    .object(userDefaultPicture)
                    .stream(new ByteArrayInputStream(content), content.length, -1)
                    .contentType("image/jpeg")
                    .build());

            inputStream.close();
            log.info("Successfully created default profile picture: {}", userDefaultPicture);

            // Return direct URL to the newly created default profile
            String url = getDirectFileUrl(minioConfig.getProfileBucket(), userDefaultPicture);
            log.debug("Generated direct URL for default profile picture: {}", url);
            return url;
        } catch (Exception e) {
            log.error("Failed to create default profile picture", e);
            throw new FileStorageException("Could not create default profile picture", e);
        }
    }

    /**
     * Delete a file
     */
    public void deleteFile(String bucket, String fileName) {
        log.info("Deleting file: {} from bucket: {}", fileName, bucket);
        try {
            minioClient.removeObject(RemoveObjectArgs.builder()
                .bucket(bucket)
                .object(fileName)
                .build());
            log.info("Successfully deleted file: {}", fileName);
        } catch (Exception e) {
            log.error("Failed to delete file: {} from bucket: {}", fileName, bucket, e);
            throw new FileDeletionException("Could not delete file: " + fileName, e);
        }
    }

    /**
     * Get a direct URL to access the file (non-expiring)
     */
    public String getDirectFileUrl(String bucket, String fileName) {
        // Return a direct URL to the object
        return minioConfig.getEndpoint() + "/" + bucket + "/" + fileName;
    }

    /**
     * Helper method to upload file and return just the object name
     */
    private String uploadFileAndReturnObjectName(MultipartFile file, String bucket, String filenamePrefix) {
        validateFile(file);

        try {
            String originalFilename = file.getOriginalFilename();
            String extension = FilenameUtils.getExtension(originalFilename);
            String objectName = filenamePrefix + "." + extension;

            minioClient.putObject(PutObjectArgs.builder()
                .bucket(bucket)
                .object(objectName)
                .stream(file.getInputStream(), file.getSize(), -1)
                .contentType(file.getContentType())
                .build());

            log.info("Successfully uploaded file to: {}/{}", bucket, objectName);
            return objectName;
        } catch (Exception e) {
            log.error("Failed to upload file: {} to bucket: {}", file.getOriginalFilename(), bucket, e);
            throw new FileUploadException("Could not store file " + file.getOriginalFilename(), e);
        }
    }

    /**
     * Validate uploaded file
     */
    private void validateFile(MultipartFile file) {
        log.debug("Validating file: {}", file.getOriginalFilename());
        if (file.isEmpty()) {
            log.warn("Empty file submitted for upload");
            throw new FileUploadException("Cannot upload empty file");
        }

        if (file.getSize() > minioConfig.getMaxFileSize()) {
            log.warn("File size {} exceeds maximum limit of {} bytes",
                file.getSize(), minioConfig.getMaxFileSize());
            throw new FileSizeLimitExceededException(
                "File size exceeds maximum limit of " +
                    (minioConfig.getMaxFileSize() / (1024 * 1024)) + " MB");
        }

        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            log.warn("Invalid file type: {}", contentType);
            throw new FileUploadException("Only image files are allowed");
        }

        String extension = Objects.requireNonNull(FilenameUtils.getExtension(file.getOriginalFilename())).toLowerCase();
        if (!ALLOWED_EXTENSIONS.contains(extension)) {
            log.warn("Unsupported file extension: {}", extension);
            throw new FileUploadException("Only JPG, JPEG, PNG and WebP files are allowed");
        }

        log.debug("File validation passed");
    }

    /**
     * Check if the given URL points to a default profile picture
     *
     * @param pictureUrl The profile picture URL to check
     * @return true if it's a default profile picture, false otherwise
     */
    public boolean isNotDefaultProfilePicture(String pictureUrl) {
        if (pictureUrl == null || pictureUrl.isEmpty()) {
            throw new FileNotFoundException("No image chosen.");
        }

        // Extract just the filename from the URL
        String filename = pictureUrl.substring(pictureUrl.lastIndexOf('/') + 1);

        // Check if the filename contains the "-default" pattern
        return !filename.contains("-default.");
    }
}
