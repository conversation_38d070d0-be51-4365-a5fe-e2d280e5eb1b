import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import {
  Package,
  TrendingUp,
  DollarSign,
  ShoppingCart,
  ArrowUpRight,
  Wallet,
  Users,
  UserRound,
  Building2,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { adminAnalyticsService } from "@/services/admin-analytics-service";
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from "recharts";

// Helper function to format currency
const formatCurrency = (value) => {
  if (value === undefined || value === null) return "0 MAD";
  return new Intl.NumberFormat("fr-MA", {
    style: "currency",
    currency: "MAD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

// Custom tooltip for charts
const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border border-slate-200 rounded-lg shadow-sm">
        <p className="font-medium text-slate-900">{label}</p>
        {payload.map((entry, index) => (
          <p
            key={`item-${index}`}
            className="text-sm"
            style={{ color: entry.color }}
          >
            {entry.name}:{" "}
            {entry.name.includes("Revenue")
              ? formatCurrency(entry.value)
              : entry.value.toLocaleString()}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// Helper function to create empty monthly data
const createEmptyMonthlyData = () => {
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  return months.map((month) => ({
    name: month,
    purchases: 0,
    revenue: 0,
  }));
};

export const Dashboard = () => {
  const { userData: user } = useSelector((state) => state.user);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    supplierCount: 0,
    employeeCount: 0,
    clientCount: 0,
    totalPurchases: 0,
    totalSpent: 0,
  });

  // Monthly data for charts
  const [purchaseData, setPurchaseData] = useState([]);
  const [revenueData, setRevenueData] = useState([]);

  // Fetch analytics data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        console.log("Starting to fetch dashboard data...");

        // Fetch dashboard statistics
        console.log("Fetching dashboard statistics...");
        const statsResponse = await adminAnalyticsService.getDashboardStats();
        console.log("Dashboard statistics response:", statsResponse);

        // Fetch monthly analytics data
        console.log("Fetching monthly analytics data...");
        const monthlyResponse =
          await adminAnalyticsService.getMonthlyPurchaseAnalytics();
        console.log("Monthly analytics response:", monthlyResponse);

        // Process statistics data
        if (statsResponse.success && statsResponse.data) {
          const { purchases, supplierCount, employeeCount, clientCount } =
            statsResponse.data;

          console.log("Dashboard stats data:", statsResponse.data);
          console.log("Purchases stats:", purchases);
          console.log("Total purchases:", purchases?.totalPurchases);
          console.log("Total spent:", purchases?.totalSpent);

          setStats({
            supplierCount,
            employeeCount,
            clientCount,
            totalPurchases: purchases?.totalPurchases || 0,
            totalSpent: purchases?.totalSpent || 0,
          });
        } else {
          console.warn(
            "Failed to get dashboard statistics:",
            statsResponse.message
          );
        }

        // Process monthly data for charts
        if (monthlyResponse.success && monthlyResponse.data) {
          console.log("Monthly data received:", monthlyResponse.data);

          // Check if we have purchase data
          if (
            monthlyResponse.data.purchases &&
            Array.isArray(monthlyResponse.data.purchases)
          ) {
            // Map the data to the format expected by the chart
            const formattedPurchaseData = monthlyResponse.data.purchases.map(
              (item) => ({
                name: item.name,
                purchases: item.count || 0,
              })
            );
            console.log("Formatted purchase data:", formattedPurchaseData);

            // Check if we have any non-zero values
            const hasData = formattedPurchaseData.some(
              (item) => item.purchases > 0
            );
            console.log("Purchase data has non-zero values:", hasData);

            setPurchaseData(formattedPurchaseData);
          } else {
            console.warn("No purchase data available in monthly response");
            setPurchaseData(createEmptyMonthlyData());
          }

          // Check if we have revenue data
          if (
            monthlyResponse.data.revenue &&
            Array.isArray(monthlyResponse.data.revenue)
          ) {
            // Map the data to the format expected by the chart
            const formattedRevenueData = monthlyResponse.data.revenue.map(
              (item) => ({
                name: item.name,
                revenue: item.amount || 0,
              })
            );
            console.log("Formatted revenue data:", formattedRevenueData);

            // Check if we have any non-zero values
            const hasData = formattedRevenueData.some(
              (item) => item.revenue > 0
            );
            console.log("Revenue data has non-zero values:", hasData);

            setRevenueData(formattedRevenueData);
          } else {
            console.warn("No revenue data available in monthly response");
            setRevenueData(createEmptyMonthlyData());
          }
        } else {
          console.warn(
            "Failed to get monthly analytics:",
            monthlyResponse?.message
          );
          // If no data is available, create empty data structures
          setPurchaseData(createEmptyMonthlyData());
          setRevenueData(createEmptyMonthlyData());
        }
      } catch (error) {
        console.error("Error fetching dashboard data:", error);

        // Reset stats
        setStats({
          supplierCount: 0,
          employeeCount: 0,
          clientCount: 0,
          totalPurchases: 0,
          totalSpent: 0,
        });

        // Set empty chart data
        setPurchaseData(createEmptyMonthlyData());
        setRevenueData(createEmptyMonthlyData());
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="space-y-4">
      {/* Welcome section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
        <div>
          <h1 className="text-2xl font-bold text-slate-900">Admin Dashboard</h1>
          <p className="text-slate-500 mt-1">
            Welcome back, {user?.firstName}! Here's an overview of your system.
          </p>
        </div>
        <Button
          className="mt-4 md:mt-0 bg-blue-600 hover:bg-blue-700"
          onClick={() => (window.location.href = "/admin/suppliers")}
        >
          <Building2 className="mr-2 h-4 w-4" />
          Manage Suppliers
        </Button>
      </div>

      {/* Stats Cards - First Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card className="bg-gradient-to-br from-white to-blue-50/30 border-blue-100 hover:shadow-md transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-slate-600">
              Total Suppliers
            </CardTitle>
            <div className="rounded-full bg-blue-50 p-2">
              <Building2 className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-slate-900">
              {stats.supplierCount}
            </div>
            <p className="text-xs text-slate-500 mt-1">Registered suppliers</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-white to-purple-50/30 border-purple-100 hover:shadow-md transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-slate-600">
              Total Employees
            </CardTitle>
            <div className="rounded-full bg-purple-50 p-2">
              <Users className="h-4 w-4 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-slate-900">
              {stats.employeeCount}
            </div>
            <p className="text-xs text-slate-500 mt-1">Active employees</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-white to-green-50/30 border-green-100 hover:shadow-md transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-slate-600">
              Total Clients
            </CardTitle>
            <div className="rounded-full bg-green-50 p-2">
              <UserRound className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-slate-900">
              {stats.clientCount}
            </div>
            <p className="text-xs text-slate-500 mt-1">Registered clients</p>
          </CardContent>
        </Card>
      </div>

      {/* Stats Cards - Second Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="bg-gradient-to-br from-white to-amber-50/30 border-amber-100 hover:shadow-md transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-slate-600">
              Total Purchases
            </CardTitle>
            <div className="rounded-full bg-amber-50 p-2">
              <ShoppingCart className="h-4 w-4 text-amber-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-slate-900">
              {stats.totalPurchases}
            </div>
            <p className="text-xs text-slate-500 mt-1">
              Products purchased from suppliers
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-white to-red-50/30 border-red-100 hover:shadow-md transition-all duration-300">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-slate-600">
              Total Spent
            </CardTitle>
            <div className="rounded-full bg-red-50 p-2">
              <DollarSign className="h-4 w-4 text-red-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900 tabular-nums tracking-tight">
              {formatCurrency(stats.totalSpent)}
            </div>
            <p className="text-xs text-slate-500 mt-1">
              Total amount spent on purchases
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Monthly Purchases</span>
              <ShoppingCart className="h-4 w-4 text-blue-600" />
            </CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            {!loading && (
              <ResponsiveContainer width="100%" height="100%">
                {purchaseData.length > 0 ? (
                  <BarChart
                    data={purchaseData}
                    margin={{ top: 10, right: 10, left: 0, bottom: 20 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis
                      dataKey="name"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: "#64748b" }}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: "#64748b" }}
                      tickFormatter={(value) => value.toLocaleString()}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend wrapperStyle={{ paddingTop: 10 }} />
                    <Bar
                      dataKey="purchases"
                      name="Purchases"
                      fill="#3b82f6"
                      radius={[4, 4, 0, 0]}
                      barSize={30}
                    />
                  </BarChart>
                ) : (
                  <div className="h-full w-full flex items-center justify-center">
                    <p className="text-slate-400">No purchase data available</p>
                  </div>
                )}
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>

        <Card className="col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Monthly Spent</span>
              <ArrowUpRight className="h-4 w-4 text-green-600" />
            </CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            {!loading && (
              <ResponsiveContainer width="100%" height="100%">
                {revenueData.length > 0 ? (
                  <AreaChart
                    data={revenueData}
                    margin={{ top: 10, right: 10, left: 0, bottom: 20 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis
                      dataKey="name"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: "#64748b" }}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: "#64748b" }}
                      tickFormatter={(value) =>
                        value >= 1000 ? `${(value / 1000).toFixed(0)}k` : value
                      }
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend wrapperStyle={{ paddingTop: 10 }} />
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      name="Revenue (MAD)"
                      stroke="#10b981"
                      fill="#10b981"
                      fillOpacity={0.2}
                      activeDot={{ r: 6 }}
                    />
                  </AreaChart>
                ) : (
                  <div className="h-full w-full flex items-center justify-center">
                    <p className="text-slate-400">No revenue data available</p>
                  </div>
                )}
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
