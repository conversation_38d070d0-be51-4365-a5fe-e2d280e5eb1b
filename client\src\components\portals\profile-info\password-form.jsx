// src/components/profile/PasswordForm.jsx
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";
import { Eye, EyeOff, KeyRound, CheckCircle, Lock, Shield } from "lucide-react";
import { profileService } from "@/services";

export const PasswordForm = () => {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirmation: false,
  });

  // Toggle password visibility
  const togglePasswordVisibility = (field) => {
    setShowPasswords({
      ...showPasswords,
      [field]: !showPasswords[field],
    });
  };

  // Create validation schema for password change
  const formSchema = z
    .object({
      currentPassword: z
        .string()
        .min(1, { message: "Current password is required" }),
      newPassword: z
        .string()
        .min(8, { message: "Password must be at least 8 characters" })
        .regex(
          /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
          {
            message:
              "Password must contain uppercase, lowercase, number and special character",
          }
        )
        .refine((val) => val !== form.getValues().currentPassword, {
          message: "New password must be different from current password",
        }),
      confirmationPassword: z
        .string()
        .min(1, { message: "Please confirm your password" }),
    })
    .refine((data) => data.newPassword === data.confirmationPassword, {
      message: "Passwords do not match",
      path: ["confirmationPassword"],
    });

  // Initialize form with react-hook-form
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmationPassword: "",
    },
    mode: "onChange",
  });

  // Form submission handler
  const onSubmit = async (values) => {
    setLoading(true);
    setSuccess(false);

    try {
      await profileService.changePassword(values);
      toast.success("Password changed successfully!");
      setSuccess(true);

      // Reset form values
      form.reset();

      // Wait for a moment before hiding success indicator
      setTimeout(() => setSuccess(false), 3000);
    } catch (error) {
      console.error("Error changing password:", error);
      toast.error(error.response?.data?.message || "Failed to change password");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="grid grid-cols-1 lg:grid-cols-2 gap-4 text-blue-gray-700"
      >
        <div className="space-y-4">
          {/* Current Password */}
          <FormField
            control={form.control}
            name="currentPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-blue-gray-700 font-medium">
                  Current Password
                </FormLabel>
                <FormControl>
                  <div className="flex items-center w-full border rounded-xl focus-within:border-blue-gray-800 transition-all shadow-sm h-[53px] bg-white">
                    <Lock
                      className={`ml-3 size-5 lg:size-6 ${
                        field.value
                          ? "text-blue-gray-700"
                          : "text-blue-gray-400"
                      }`}
                    />
                    <input
                      type={showPasswords.current ? "text" : "password"}
                      className="flex-1 h-full text-base focus:outline-0 transition-all placeholder:text-blue-gray-400 rounded-xl px-2 lg:text-lg bg-transparent"
                      placeholder="Enter your current password"
                      disabled={loading}
                      {...field}
                    />
                    <button
                      type="button"
                      className="mr-3 text-blue-gray-400 hover:text-blue-gray-600 focus:outline-none"
                      onClick={() => togglePasswordVisibility("current")}
                    >
                      {showPasswords.current ? (
                        <Eye className="size-5 lg:size-6" />
                      ) : (
                        <EyeOff className="size-5 lg:size-6" />
                      )}
                    </button>
                  </div>
                </FormControl>
                <FormMessage className="text-red-500" />
              </FormItem>
            )}
          />

          {/* New Password */}
          <FormField
            control={form.control}
            name="newPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-blue-gray-700 font-medium">
                  New Password
                </FormLabel>
                <FormControl>
                  <div className="flex items-center w-full border rounded-xl focus-within:border-blue-gray-800 transition-all shadow-sm h-[53px] bg-white">
                    <KeyRound
                      className={`ml-3 size-5 lg:size-6 ${
                        field.value
                          ? "text-blue-gray-700"
                          : "text-blue-gray-400"
                      }`}
                    />
                    <input
                      type={showPasswords.new ? "text" : "password"}
                      className="flex-1 h-full text-base focus:outline-0 transition-all placeholder:text-blue-gray-400 rounded-xl px-2 lg:text-lg bg-transparent"
                      placeholder="Enter your new password"
                      disabled={loading}
                      {...field}
                    />
                    <button
                      type="button"
                      className="mr-3 text-blue-gray-400 hover:text-blue-gray-600 focus:outline-none"
                      onClick={() => togglePasswordVisibility("new")}
                    >
                      {showPasswords.new ? (
                        <Eye className="size-5 lg:size-6" />
                      ) : (
                        <EyeOff className="size-5 lg:size-6" />
                      )}
                    </button>
                  </div>
                </FormControl>
                <FormMessage className="text-red-500" />
              </FormItem>
            )}
          />

          {/* Confirm Password */}
          <FormField
            control={form.control}
            name="confirmationPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-blue-gray-700 font-medium">
                  Confirm New Password
                </FormLabel>
                <FormControl>
                  <div className="flex items-center w-full border rounded-xl focus-within:border-blue-gray-800 transition-all shadow-sm h-[53px] bg-white">
                    <KeyRound
                      className={`ml-3 size-5 lg:size-6 ${
                        field.value
                          ? "text-blue-gray-700"
                          : "text-blue-gray-400"
                      }`}
                    />
                    <input
                      type={showPasswords.confirmation ? "text" : "password"}
                      className="flex-1 h-full text-base focus:outline-0 transition-all placeholder:text-blue-gray-400 rounded-xl px-2 lg:text-lg bg-transparent"
                      placeholder="Confirm your new password"
                      disabled={loading}
                      {...field}
                    />
                    <button
                      type="button"
                      className="mr-3 text-blue-gray-400 hover:text-blue-gray-600 focus:outline-none"
                      onClick={() => togglePasswordVisibility("confirmation")}
                    >
                      {showPasswords.confirmation ? (
                        <Eye className="size-5 lg:size-6" />
                      ) : (
                        <EyeOff className="size-5 lg:size-6" />
                      )}
                    </button>
                  </div>
                </FormControl>
                <FormMessage className="text-red-500" />
              </FormItem>
            )}
          />
        </div>

        {/* Password Requirements */}
        <div className="flex flex-col gap-4">
          <div className="h-full flex flex-col">
            <h4 className="text-sm lg:text-base font-medium mb-2">
              Password Requirements
            </h4>
            <div className="bg-blue-gray-50/80 p-5 rounded-xl shadow-sm flex-1 flex items-center my-auto border border-blue-gray-100/70">
              <div className="space-4 w-full">
                <div
                  className={`flex items-center gap-2 ${
                    form.watch("newPassword")?.length >= 8
                      ? "text-green-600"
                      : "text-blue-gray-500"
                  }`}
                >
                  <div
                    className={`size-2.5 rounded-full ${
                      form.watch("newPassword")?.length >= 8
                        ? "bg-green-500"
                        : "bg-blue-gray-300"
                    }`}
                  ></div>
                  <p className="text-base">At least 8 characters</p>
                </div>
                <div
                  className={`flex items-center gap-2 ${
                    /[A-Z]/.test(form.watch("newPassword") || "")
                      ? "text-green-600"
                      : "text-blue-gray-500"
                  }`}
                >
                  <div
                    className={`size-2.5 rounded-full ${
                      /[A-Z]/.test(form.watch("newPassword") || "")
                        ? "bg-green-500"
                        : "bg-blue-gray-300"
                    }`}
                  ></div>
                  <p className="text-base">At least one uppercase letter</p>
                </div>
                <div
                  className={`flex items-center gap-2 ${
                    /[a-z]/.test(form.watch("newPassword") || "")
                      ? "text-green-600"
                      : "text-blue-gray-500"
                  }`}
                >
                  <div
                    className={`size-2.5 rounded-full ${
                      /[a-z]/.test(form.watch("newPassword") || "")
                        ? "bg-green-500"
                        : "bg-blue-gray-300"
                    }`}
                  ></div>
                  <p className="text-base">At least one lowercase letter</p>
                </div>
                <div
                  className={`flex items-center gap-2 ${
                    /\d/.test(form.watch("newPassword") || "")
                      ? "text-green-600"
                      : "text-blue-gray-500"
                  }`}
                >
                  <div
                    className={`size-2.5 rounded-full ${
                      /\d/.test(form.watch("newPassword") || "")
                        ? "bg-green-500"
                        : "bg-blue-gray-300"
                    }`}
                  ></div>
                  <p className="text-base">At least one number</p>
                </div>
                <div
                  className={`flex items-center gap-2 ${
                    /[@$!%*?&]/.test(form.watch("newPassword") || "")
                      ? "text-green-600"
                      : "text-blue-gray-500"
                  }`}
                >
                  <div
                    className={`size-2.5 rounded-full ${
                      /[@$!%*?&]/.test(form.watch("newPassword") || "")
                        ? "bg-green-500"
                        : "bg-blue-gray-300"
                    }`}
                  ></div>
                  <p className="text-base">At least one special character</p>
                </div>
                <div
                  className={`flex items-center gap-2 ${
                    form.watch("newPassword") !==
                      form.watch("currentPassword") && form.watch("newPassword")
                      ? "text-green-600"
                      : "text-blue-gray-500"
                  }`}
                >
                  <div
                    className={`size-2.5 rounded-full ${
                      form.watch("newPassword") !==
                        form.watch("currentPassword") &&
                      form.watch("newPassword")
                        ? "bg-green-500"
                        : "bg-blue-gray-300"
                    }`}
                  ></div>
                  <p className="text-base">Different from current password</p>
                </div>
              </div>
            </div>
          </div>
          <button
            type="submit"
            disabled={
              loading || !form.formState.isDirty || !form.formState.isValid
            }
            className={`inline-flex items-center justify-center gap-2 text-white text-base px-6 py-3 rounded-xl lg:text-lg transition-all ${
              loading || !form.formState.isDirty || !form.formState.isValid
                ? "bg-blue-gray-400 cursor-not-allowed"
                : "bg-blue-gray-900 hover:bg-blue-gray-800 shadow-sm hover:shadow"
            }`}
          >
            {loading ? (
              <>
                <div className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></div>
                <span>Changing...</span>
              </>
            ) : success ? (
              <>
                <CheckCircle className="h-5 w-5" />
                <span>Password Changed</span>
              </>
            ) : (
              <>
                <Shield className="h-5 w-5" />
                <span>Change Password</span>
              </>
            )}
          </button>
        </div>
      </form>
    </Form>
  );
};

export default PasswordForm;
