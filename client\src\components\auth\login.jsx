import { usePasswordVisibility } from "@/hooks";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { isDesktop, ripple } from "@/utils";
import {
  AtSignIcon,
  EyeIcon,
  EyeOffIcon,
  KeyRoundIcon,
  MailIcon,
  AlertCircleIcon,
} from "lucide-react";
import { useAuth } from "@/context/auth-context";
import { useFormik } from "formik";
import * as Yup from "yup";

export const Login = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login, loading, error, getUserRole } = useAuth();
  const { isVisible, toggleVisibility, inputRef } = usePasswordVisibility();

  // Define validation schema using Yup
  const LoginSchema = Yup.object().shape({
    email: Yup.string()
      .email("Email must be valid")
      .required("Email is required"),
    password: Yup.string()
      .min(8, "Password must contain at least 8 characters")
      .required("Password is required"),
  });

  // Initialize formik
  const formik = useFormik({
    initialValues: {
      email: "",
      password: "",
    },
    validationSchema: LoginSchema,
    onSubmit: async (values) => {
      const loginResult = await login(values);

      if (loginResult.success) {
        console.log("Login successful");

        // Get the user's role from the login result
        const userRole = loginResult.userRole;

        // Determine redirect path based on role or previous location
        let redirectTo;

        // Check if there's a saved location the user was trying to access
        if (location.state?.from?.pathname) {
          redirectTo = location.state.from.pathname;
        } else {
          // Otherwise, redirect based on role
          switch (userRole) {
            case "admin":
              redirectTo = "/admin/dashboard";
              break;
            case "client":
              redirectTo = "/client/dashboard";
              break;
            case "supplier":
              redirectTo = "/supplier/dashboard";
              break;
            case "employee":
              redirectTo = "/employee/dashboard";
              break;
            default:
              // Fallback for unknown roles
              redirectTo = "/";
              break;
          }
        }

        // Navigate to the appropriate dashboard
        navigate(redirectTo, { replace: true });
      } else {
        console.log("Login failed");
        // Error is handled by the AuthContext and stored in error state
      }
    },
  });

  return (
    <section className="min-h-screen flex flex-col justify-center items-center px-8 py-20 lg:px-[380px] bg-blue-gray-50/40">
      <div className="flex items-center justify-center absolute top-0 right-0 px-6 py-3 select-none">
        <Link to="/">
          <div className="flex items-center justify-center gap-x-1.5 ">
            <img src="/icon.png" className="size-7" alt="Logo" />
            <p className="text-blue-gray-700 font-satoshi text-xl">
              <span className="text-crimson">S</span>tore
              <span className="text-crimson">F</span>low
            </p>
          </div>
        </Link>
      </div>

      <div className="lg:bg-white lg:rounded-3xl w-full lg:p-10 lg:border lg:border-blue-gray-50 lg:shadow-dark text-blue-gray-700">
        <p className="font-satoshi font-bold text-header text-blue-gray-800 text-center w-full py-0">
          Login to your account
        </p>
        <p className="font-normal text-subheader text-blue-gray-800 text-center w-full mb-7 py-0">
          Welcome back to your space
        </p>

        <form
          onSubmit={formik.handleSubmit}
          className="w-full flex flex-col items-center gap-y-7 mb-7"
        >
          {/* Show only API/login errors here, not field validation errors */}
          {error && (
            <div className="w-full mb-4 p-3 bg-red-50 text-red-600 rounded-lg flex items-center justify-center">
              <p className="text-base">{error}</p>
            </div>
          )}

          {/* ---------------Sign in Form--------------- */}
          <div className="flex flex-col gap-y-1 w-full">
            <p className="text-base">Email</p>
            <div
              className={`flex items-center w-full border ${
                formik.touched.email && formik.errors.email
                  ? "border-red-500"
                  : "border-blue-gray-500"
              } rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px]`}
            >
              <AtSignIcon
                className={`ml-3 size-5 lg:size-6 ${
                  formik.values.email ? "" : "text-blue-gray-500"
                } ${
                  formik.touched.email && formik.errors.email
                    ? "text-red-500"
                    : ""
                }`}
                onMouseDown={(event) => event.preventDefault()}
              />
              <input
                id="email"
                name="email"
                type="email"
                className="flex-1 h-full text-base focus:outline-0 transition-all placeholder:text-blue-gray-500 rounded-xl pr-3 pl-2 lg:text-lg"
                placeholder="Type your email address"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.email}
                disabled={loading}
              />
              {formik.touched.email && formik.errors.email && (
                <AlertCircleIcon className="mr-3 size-5 text-red-500" />
              )}
            </div>
            {formik.touched.email && formik.errors.email && (
              <p className="text-red-500 text-sm mt-1">{formik.errors.email}</p>
            )}
          </div>

          <div className="flex flex-col gap-y-1 w-full">
            <p className="text-base">Password</p>
            <div
              className={`flex items-center w-full border ${
                formik.touched.password && formik.errors.password
                  ? "border-red-500"
                  : "border-blue-gray-500"
              } rounded-xl focus-within:border-blue-gray-800 transition-all h-[53px]`}
            >
              <KeyRoundIcon
                className={`ml-3 size-5 lg:size-6 ${
                  formik.values.password
                    ? "text-blue-gray-800"
                    : "text-blue-gray-500"
                } ${
                  formik.touched.password && formik.errors.password
                    ? "text-red-500"
                    : ""
                }`}
              />
              <input
                ref={inputRef}
                id="password"
                name="password"
                type={isVisible ? "text" : "password"}
                className="flex-1 h-full text-base focus:outline-0 transition-all placeholder:text-blue-gray-500 rounded-xl px-2 lg:text-lg"
                placeholder="Type your password"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.password}
                disabled={loading}
              />
              {isVisible ? (
                <EyeIcon
                  onClick={toggleVisibility}
                  className={`mr-3 size-5 cursor-pointer lg:size-6 ${
                    formik.values.password
                      ? "text-blue-gray-800"
                      : "text-blue-gray-500"
                  }`}
                  onMouseDown={(event) => event.preventDefault()}
                />
              ) : (
                <EyeOffIcon
                  onClick={toggleVisibility}
                  className={`mr-3 size-5 cursor-pointer lg:size-6 ${
                    formik.values.password
                      ? "text-blue-gray-800"
                      : "text-blue-gray-500"
                  }`}
                  onMouseDown={(event) => event.preventDefault()}
                />
              )}
            </div>
            {formik.touched.password && formik.errors.password && (
              <p className="text-red-500 text-sm mt-1">
                {formik.errors.password}
              </p>
            )}
          </div>

          <p className="font-normal text-base self-start select-none lg:text-lg">
            <Link
              to="/auth/forgot-password"
              className="text-blue-gray-600 hover:text-blue-gray-800 transition-colors duration-200"
            >
              Forgot password?
            </Link>
          </p>

          <button
            onMouseDown={(event) =>
              ripple.create(event, isDesktop() ? "light" : "dark")
            }
            type="submit"
            disabled={loading}
            className={`group cursor-pointer text-base text-white bg-blue-gray-900 border border-blue-gray-800 shadow-dark flex items-center justify-between px-4 w-full rounded-full py-3 font-medium transition-all duration-300 hover:bg-transparent hover:text-blue-gray-800 hover:border-blue-gray-50 hover:shadow-darker active:shadow-darker lg:rounded-xl lg:text-lg ${
              loading ? "opacity-70 cursor-not-allowed" : ""
            }`}
          >
            {/* Icon with hover color change */}
            <MailIcon className="text-white transition-all duration-300 group-hover:text-blue-gray-800 size-5 lg:size-6" />

            {/* Text centered */}
            <span className="text-center">
              {loading ? "Signing in..." : "Sign in"}
            </span>

            <MailIcon className="bg-blue-300 text-white transition-all duration-300 group-hover:text-blue-gray-800 size-5 lg:size-6 invisible" />
          </button>
        </form>
        <p className="text-center text-base lg:text-lg">
          Don't have an account?{" "}
          <Link to="/auth/signup" className="underline">
            Sign up
          </Link>
        </p>
      </div>
    </section>
  );
};

export default Login;
